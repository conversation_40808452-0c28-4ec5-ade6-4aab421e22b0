{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {"cell_id": "e1bd6f1893e44e798d8a9a818a2f4b54", "deepnote_cell_type": "code", "execution_context_id": "5ba8fb5a-825a-485f-81db-f13eaf5a5225", "execution_millis": 7447, "execution_start": *************, "source_hash": "70f387e9"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Documents/stoned-apes/finance/crypto-accounting/utils.py:74: FutureWarning: The frame.append method is deprecated and will be removed from pandas in a future version. Use pandas.concat instead.\n", "  company_wallets = company_wallets.append({\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sac_pubkey</th>\n", "      <th>sac_name</th>\n", "      <th>sac_category</th>\n", "      <th>sac_tag</th>\n", "      <th>sac_Spalte 2</th>\n", "      <th>sac_Spalte 3</th>\n", "      <th>sac_Spalte 4</th>\n", "      <th>sac_email</th>\n", "      <th>sac_phone</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>8vQeeeyg8VjH7ZFCTa2Fk6JcjEnPCwoBjXT3NsbfqUSD</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>**********************************</td>\n", "      <td>BTC Wirex</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>4ifAYTxnxwDr6cyDDcZdWewRQJqmpszo43iiK6stzvG9</td>\n", "      <td>C Binance</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>CiFNkttVWHUj3LVtjbh8mADcaysXmSpnqdN7NB8RNmcj</td>\n", "      <td>NaN</td>\n", "      <td>lucky dip</td>\n", "      <td>NaN</td>\n", "      <td>authority of the lucky dip #2 raffle</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>CcmkU1vLCyjXnH46BfVA1623XkEnau3dSUfFm6rc5Upg</td>\n", "      <td>Luck Dip #1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>77</th>\n", "      <td>********************************************</td>\n", "      <td><PERSON></td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>78</th>\n", "      <td>F8F7q5WCtaRiYoH7aQGutRhDsXFzJdiZLGgA8A1PDDHX</td>\n", "      <td>Investment-Wallet</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>79</th>\n", "      <td>treZeLEpyJaJ9xDuHC4sytyPkMWmQMC1dn8iNNUvZG1</td>\n", "      <td>Treasury 1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>80</th>\n", "      <td>strfkDDF2oMXoiYWhY4aa2LKuEHNuLTrimJh6ngrQBN</td>\n", "      <td>Treasury 2</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81</th>\n", "      <td>ABGLAB5UQ4oeyVH61RkFkxP9R5LY3iDQFBrDSiP86Tzc</td>\n", "      <td>Stoned Apes</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td><EMAIL></td>\n", "      <td>0000000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>82 rows × 9 columns</p>\n", "</div>"], "text/plain": ["                                      sac_pubkey           sac_name sac_category  sac_tag                          sac_Spalte 2  sac_Spalte 3  sac_Spalte 4                sac_email   sac_phone\n", "0   8vQeeeyg8VjH7ZFCTa2Fk6JcjEnPCwoBjXT3NsbfqUSD                NaN          NaN      NaN                                   NaN           NaN           NaN                      NaN         NaN\n", "1             **********************************          BTC Wirex          NaN      NaN                                   NaN           NaN           NaN                      NaN         NaN\n", "2   4ifAYTxnxwDr6cyDDcZdWewRQJqmpszo43iiK6stzvG9          C Binance          NaN      NaN                                   NaN           NaN           NaN                      NaN         NaN\n", "3   CiFNkttVWHUj3LVtjbh8mADcaysXmSpnqdN7NB8RNmcj                NaN    lucky dip      NaN  authority of the lucky dip #2 raffle           NaN           NaN                      NaN         NaN\n", "4   CcmkU1vLCyjXnH46BfVA1623XkEnau3dSUfFm6rc5Upg        Luck Dip #1          NaN      NaN                                   NaN           NaN           NaN                      NaN         NaN\n", "..                                           ...                ...          ...      ...                                   ...           ...           ...                      ...         ...\n", "77  ********************************************   Matthias Binance          NaN      NaN                                   NaN           NaN           NaN                      NaN         NaN\n", "78  F8F7q5WCtaRiYoH7aQGutRhDsXFzJdiZLGgA8A1PDDHX  Investment-Wallet          NaN      NaN                                   NaN           NaN           NaN                      NaN         NaN\n", "79   treZeLEpyJaJ9xDuHC4sytyPkMWmQMC1dn8iNNUvZG1         Treasury 1          NaN      NaN                                   NaN           NaN           NaN                      NaN         NaN\n", "80   strfkDDF2oMXoiYWhY4aa2LKuEHNuLTrimJh6ngrQBN         Treasury 2          NaN      NaN                                   NaN           NaN           NaN                      NaN         NaN\n", "81  ABGLAB5UQ4oeyVH61RkFkxP9R5LY3iDQFBrDSiP86Tzc        Stoned Apes          NaN      NaN                                   NaN           NaN           NaN  <EMAIL>  0000000000\n", "\n", "[82 rows x 9 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "from dotenv import load_dotenv\n", "import pandas as pd\n", "import importlib\n", "import utils\n", "importlib.reload(utils)\n", "\n", "pd.set_option('display.float_format', '{:,.2f}'.format)\n", "\n", "\n", "company_wallets = utils.load_company_wallets()\n", "company_wallets\n"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["found 4196 rows\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>block_timestamp</th>\n", "      <th>tx_id</th>\n", "      <th>mint_price</th>\n", "      <th>succeeded</th>\n", "      <th>purchaser</th>\n", "      <th>mint</th>\n", "      <th>collection_id</th>\n", "      <th>nft_collection_name</th>\n", "      <th>block_timestamp_hour</th>\n", "      <th>price_usd</th>\n", "      <th>__row_index</th>\n", "      <th>day</th>\n", "      <th>accounting_period</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2022-02-11 21:23:29+00:00</td>\n", "      <td>5aH9owHNrStWL6SHTkf6qubfHuWc1v2GY1YD5GbavRUPX8YE6XZXd1xTGqCCZzsZaSnNcQswcsp9HbPnoh6KrFYa</td>\n", "      <td>0.01847</td>\n", "      <td>True</td>\n", "      <td>NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC</td>\n", "      <td>2fscN46CxjVZ1fFM96FPM6o1xmaPKLZHreRw3Hy5cHw4</td>\n", "      <td>6yzCdFZ72HRd5xkzAdWrJLJo96XDHUeEKuHXvTvCJktq</td>\n", "      <td>None</td>\n", "      <td>2022-02-11T21:00:00.000Z</td>\n", "      <td>99.276980</td>\n", "      <td>0</td>\n", "      <td>2022-02-11</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2022-02-13 19:03:25+00:00</td>\n", "      <td>GrMNp2EsjcCjB2MMiZXESkDhLKMENA91BUdRt4HXZUVQrZWpn2y7sCAhqzpPR8HRu7HhXRMBW78J6scjtrooGBu</td>\n", "      <td>0.01847</td>\n", "      <td>True</td>\n", "      <td>NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC</td>\n", "      <td>9w2a57gV3BeYm3TPfTuFimRtVyy4XqSoEBgojE1mrvo2</td>\n", "      <td>6yzCdFZ72HRd5xkzAdWrJLJo96XDHUeEKuHXvTvCJktq</td>\n", "      <td>None</td>\n", "      <td>2022-02-13T19:00:00.000Z</td>\n", "      <td>93.710630</td>\n", "      <td>1</td>\n", "      <td>2022-02-13</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2022-02-13 19:04:03+00:00</td>\n", "      <td>4SPz8p7pr4fS5pfkdDmjSH8eo4aHxoocsaC4qpRuy8CT6DV22UXRUSgBGBoNXi7F9QKRMbYShF2Tib5dHYnrByfG</td>\n", "      <td>0.01847</td>\n", "      <td>True</td>\n", "      <td>NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC</td>\n", "      <td>EvnNnbBLJ3vMWnoLuPwiMvqbEnTFcESSXoiKkjeZ6SFK</td>\n", "      <td>6yzCdFZ72HRd5xkzAdWrJLJo96XDHUeEKuHXvTvCJktq</td>\n", "      <td>None</td>\n", "      <td>2022-02-13T19:00:00.000Z</td>\n", "      <td>93.710630</td>\n", "      <td>2</td>\n", "      <td>2022-02-13</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2022-02-16 08:49:35+00:00</td>\n", "      <td>fvsxGceSgfWJ1r1bZS1MUkoPhXUJJYtSP9VK1a1xyH1NDRBDocQ5KDzMBYTM4er855aHcFYLioNwHxxgSezG8Z2</td>\n", "      <td>0.01847</td>\n", "      <td>True</td>\n", "      <td>NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC</td>\n", "      <td>DJRB4VrsmkfiLxjiKGcpPVphZXT1ZiAYWodk6q3iefbU</td>\n", "      <td>6yzCdFZ72HRd5xkzAdWrJLJo96XDHUeEKuHXvTvCJktq</td>\n", "      <td>None</td>\n", "      <td>2022-02-16T08:00:00.000Z</td>\n", "      <td>103.309500</td>\n", "      <td>3</td>\n", "      <td>2022-02-16</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2022-02-17 10:46:58+00:00</td>\n", "      <td>anKC2JtM38Jm1HTybUYrzqWCssVrroFUuhuZCf8PCtWE12hdxBga8UiH4GAnBhhiKKTjENFW2WEfq3Dko3sHc6T</td>\n", "      <td>0.01847</td>\n", "      <td>True</td>\n", "      <td>NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC</td>\n", "      <td>Be7ZGDw6YHruSibxLEQtS7tCXQEcuxocKEStSJQaF9fF</td>\n", "      <td>6yzCdFZ72HRd5xkzAdWrJLJo96XDHUeEKuHXvTvCJktq</td>\n", "      <td>None</td>\n", "      <td>2022-02-17T10:00:00.000Z</td>\n", "      <td>99.111093</td>\n", "      <td>4</td>\n", "      <td>2022-02-17</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4191</th>\n", "      <td>2022-09-25 20:14:02+00:00</td>\n", "      <td>3TN35JmjD92PooaQbMuHXtwHGskmDt4edZU9zD3JbtqLL3KWQ6sohyB5bPhCrNpYpzpkxiyiFoBnMKmYF1FTAcdp</td>\n", "      <td>0.01847</td>\n", "      <td>True</td>\n", "      <td>NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC</td>\n", "      <td>9v2jUsCgvfkZgzpCJvGeYn2DLYYHB5ES65wETuh614SN</td>\n", "      <td>6yzCdFZ72HRd5xkzAdWrJLJo96XDHUeEKuHXvTvCJktq</td>\n", "      <td>None</td>\n", "      <td>2022-09-25T20:00:00.000Z</td>\n", "      <td>32.948732</td>\n", "      <td>4191</td>\n", "      <td>2022-09-25</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4192</th>\n", "      <td>2022-09-25 20:14:15+00:00</td>\n", "      <td>5SsGuK5zsZCq23jfCUWgVUXWGfsi2L2VDpozwQnm3xhTd2cUQJfv7FKZqkB7uYQcstrcTVrcuXp1iMgZCJqPe1AQ</td>\n", "      <td>0.01847</td>\n", "      <td>True</td>\n", "      <td>NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC</td>\n", "      <td>GZ9m49ExPvtLDJhCAVDi8ViYbw5dVxiNhmJ288WqdyPe</td>\n", "      <td>6yzCdFZ72HRd5xkzAdWrJLJo96XDHUeEKuHXvTvCJktq</td>\n", "      <td>None</td>\n", "      <td>2022-09-25T20:00:00.000Z</td>\n", "      <td>32.948732</td>\n", "      <td>4192</td>\n", "      <td>2022-09-25</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4193</th>\n", "      <td>2022-09-25 20:14:33+00:00</td>\n", "      <td>XKZKKS59wABzLSxTJF89sDZSz2whEhXnUigXChmhTzsM1LoQgGQHR5xcbqKcTxVwUq5kELCUi47WCoBQBbJ6kyu</td>\n", "      <td>0.01847</td>\n", "      <td>True</td>\n", "      <td>NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC</td>\n", "      <td>7CcgoTTJ9iG1Gikc1esqWNpSvKhbDQDRLu3xS8WjCRHT</td>\n", "      <td>6yzCdFZ72HRd5xkzAdWrJLJo96XDHUeEKuHXvTvCJktq</td>\n", "      <td>None</td>\n", "      <td>2022-09-25T20:00:00.000Z</td>\n", "      <td>32.948732</td>\n", "      <td>4193</td>\n", "      <td>2022-09-25</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4194</th>\n", "      <td>2022-09-25 20:14:39+00:00</td>\n", "      <td>2ohv3MDedoDRvtVL5BGbAeB3XsQMw8vNRdpf2xdsgJjBoBd2pmzzzv4x5oxAibUgvCLGmNM8f1ZHoj1EhtLasB1V</td>\n", "      <td>0.01847</td>\n", "      <td>True</td>\n", "      <td>NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC</td>\n", "      <td>DGDS3iQfVJacgeaNDW1HYuXoiRmvzRgdAi5bC1i2u8eP</td>\n", "      <td>6yzCdFZ72HRd5xkzAdWrJLJo96XDHUeEKuHXvTvCJktq</td>\n", "      <td>None</td>\n", "      <td>2022-09-25T20:00:00.000Z</td>\n", "      <td>32.948732</td>\n", "      <td>4194</td>\n", "      <td>2022-09-25</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4195</th>\n", "      <td>2022-09-25 20:14:56+00:00</td>\n", "      <td>5RTKBsk6YFHZoiMiGAEz6o4qeKjLVSNgwvDoLrHENqRv8oHnzexnh6xTydXfSDse2EgMQW3VYuuob2Q97zMUMUMX</td>\n", "      <td>0.01847</td>\n", "      <td>True</td>\n", "      <td>NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC</td>\n", "      <td>********************************************</td>\n", "      <td>6yzCdFZ72HRd5xkzAdWrJLJo96XDHUeEKuHXvTvCJktq</td>\n", "      <td>None</td>\n", "      <td>2022-09-25T20:00:00.000Z</td>\n", "      <td>32.948732</td>\n", "      <td>4195</td>\n", "      <td>2022-09-25</td>\n", "      <td>2022</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4196 rows × 13 columns</p>\n", "</div>"], "text/plain": ["               block_timestamp                                                                                     tx_id  mint_price  succeeded                                    purchaser                                          mint                                 collection_id nft_collection_name      block_timestamp_hour   price_usd  __row_index         day  accounting_period\n", "0    2022-02-11 21:23:29+00:00  5aH9owHNrStWL6SHTkf6qubfHuWc1v2GY1YD5GbavRUPX8YE6XZXd1xTGqCCZzsZaSnNcQswcsp9HbPnoh6KrFYa     0.01847       True  NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC  2fscN46CxjVZ1fFM96FPM6o1xmaPKLZHreRw3Hy5cHw4  6yzCdFZ72HRd5xkzAdWrJLJo96XDHUeEKuHXvTvCJktq                None  2022-02-11T21:00:00.000Z   99.276980            0  2022-02-11               2022\n", "1    2022-02-13 19:03:25+00:00   GrMNp2EsjcCjB2MMiZXESkDhLKMENA91BUdRt4HXZUVQrZWpn2y7sCAhqzpPR8HRu7HhXRMBW78J6scjtrooGBu     0.01847       True  NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC  9w2a57gV3BeYm3TPfTuFimRtVyy4XqSoEBgojE1mrvo2  6yzCdFZ72HRd5xkzAdWrJLJo96XDHUeEKuHXvTvCJktq                None  2022-02-13T19:00:00.000Z   93.710630            1  2022-02-13               2022\n", "2    2022-02-13 19:04:03+00:00  4SPz8p7pr4fS5pfkdDmjSH8eo4aHxoocsaC4qpRuy8CT6DV22UXRUSgBGBoNXi7F9QKRMbYShF2Tib5dHYnrByfG     0.01847       True  NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC  EvnNnbBLJ3vMWnoLuPwiMvqbEnTFcESSXoiKkjeZ6SFK  6yzCdFZ72HRd5xkzAdWrJLJo96XDHUeEKuHXvTvCJktq                None  2022-02-13T19:00:00.000Z   93.710630            2  2022-02-13               2022\n", "3    2022-02-16 08:49:35+00:00   fvsxGceSgfWJ1r1bZS1MUkoPhXUJJYtSP9VK1a1xyH1NDRBDocQ5KDzMBYTM4er855aHcFYLioNwHxxgSezG8Z2     0.01847       True  NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC  DJRB4VrsmkfiLxjiKGcpPVphZXT1ZiAYWodk6q3iefbU  6yzCdFZ72HRd5xkzAdWrJLJo96XDHUeEKuHXvTvCJktq                None  2022-02-16T08:00:00.000Z  103.309500            3  2022-02-16               2022\n", "4    2022-02-17 10:46:58+00:00   anKC2JtM38Jm1HTybUYrzqWCssVrroFUuhuZCf8PCtWE12hdxBga8UiH4GAnBhhiKKTjENFW2WEfq3Dko3sHc6T     0.01847       True  NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC  Be7ZGDw6YHruSibxLEQtS7tCXQEcuxocKEStSJQaF9fF  6yzCdFZ72HRd5xkzAdWrJLJo96XDHUeEKuHXvTvCJktq                None  2022-02-17T10:00:00.000Z   99.111093            4  2022-02-17               2022\n", "...                        ...                                                                                       ...         ...        ...                                          ...                                           ...                                           ...                 ...                       ...         ...          ...         ...                ...\n", "4191 2022-09-25 20:14:02+00:00  3TN35JmjD92PooaQbMuHXtwHGskmDt4edZU9zD3JbtqLL3KWQ6sohyB5bPhCrNpYpzpkxiyiFoBnMKmYF1FTAcdp     0.01847       True  NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC  9v2jUsCgvfkZgzpCJvGeYn2DLYYHB5ES65wETuh614SN  6yzCdFZ72HRd5xkzAdWrJLJo96XDHUeEKuHXvTvCJktq                None  2022-09-25T20:00:00.000Z   32.948732         4191  2022-09-25               2022\n", "4192 2022-09-25 20:14:15+00:00  5SsGuK5zsZCq23jfCUWgVUXWGfsi2L2VDpozwQnm3xhTd2cUQJfv7FKZqkB7uYQcstrcTVrcuXp1iMgZCJqPe1AQ     0.01847       True  NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC  GZ9m49ExPvtLDJhCAVDi8ViYbw5dVxiNhmJ288WqdyPe  6yzCdFZ72HRd5xkzAdWrJLJo96XDHUeEKuHXvTvCJktq                None  2022-09-25T20:00:00.000Z   32.948732         4192  2022-09-25               2022\n", "4193 2022-09-25 20:14:33+00:00   XKZKKS59wABzLSxTJF89sDZSz2whEhXnUigXChmhTzsM1LoQgGQHR5xcbqKcTxVwUq5kELCUi47WCoBQBbJ6kyu     0.01847       True  NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC  7CcgoTTJ9iG1Gikc1esqWNpSvKhbDQDRLu3xS8WjCRHT  6yzCdFZ72HRd5xkzAdWrJLJo96XDHUeEKuHXvTvCJktq                None  2022-09-25T20:00:00.000Z   32.948732         4193  2022-09-25               2022\n", "4194 2022-09-25 20:14:39+00:00  2ohv3MDedoDRvtVL5BGbAeB3XsQMw8vNRdpf2xdsgJjBoBd2pmzzzv4x5oxAibUgvCLGmNM8f1ZHoj1EhtLasB1V     0.01847       True  NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC  DGDS3iQfVJacgeaNDW1HYuXoiRmvzRgdAi5bC1i2u8eP  6yzCdFZ72HRd5xkzAdWrJLJo96XDHUeEKuHXvTvCJktq                None  2022-09-25T20:00:00.000Z   32.948732         4194  2022-09-25               2022\n", "4195 2022-09-25 20:14:56+00:00  5RTKBsk6YFHZoiMiGAEz6o4qeKjLVSNgwvDoLrHENqRv8oHnzexnh6xTydXfSDse2EgMQW3VYuuob2Q97zMUMUMX     0.01847       True  NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC  ********************************************  6yzCdFZ72HRd5xkzAdWrJLJo96XDHUeEKuHXvTvCJktq                None  2022-09-25T20:00:00.000Z   32.948732         4195  2022-09-25               2022\n", "\n", "[4196 rows x 13 columns]"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["collections = \"', '\".join(utils.collections)\n", "wallet_list = \"', '\".join(company_wallets[\"sac_pubkey\"].to_list())\n", "        \n", "nft_mints = utils.query_df(f\"\"\"\n", "    SELECT\n", "        mints.block_timestamp, mints.tx_id, mints.mint_price, mints.succeeded, mints.purchaser, mints.mint, nft.collection_id, nft.nft_collection_name, date_trunc(\"hour\", mints.block_timestamp) as block_timestamp_hour, prices.price as price_usd\n", "    FROM\n", "        solana.nft.fact_nft_mints mints\n", "        join solana.nft.dim_nft_metadata nft on mints.mint = nft.mint\n", "        join solana.price.ez_prices_hourly prices on block_timestamp_hour = prices.hour\n", "    WHERE\n", "        prices.token_address = 'So11111111111111111111111111111111111111112' and\n", "        nft.collection_id in ('{collections}')\n", "    ORDER BY\n", "        block_timestamp;\"\"\")\n", "\n", "# nft_mints = utils.merge_company_wallets(nft_mints, company_wallets, \"purchaser\")\n", "# floor_sweeps = utils.move_columns_to_front(floor_sweeps, [\"block_timestamp\", \"accounting_period\", \"tx_id\", \"by_sac\", \"buyer_address\", \"price_usd\"])\n", "# floor_sweeps.info()\n", "nft_mints"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["found 8396 rows\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>block_timestamp</th>\n", "      <th>tx_id</th>\n", "      <th>mint_price</th>\n", "      <th>succeeded</th>\n", "      <th>purchaser</th>\n", "      <th>mint</th>\n", "      <th>collection_id</th>\n", "      <th>nft_collection_name</th>\n", "      <th>block_timestamp_hour</th>\n", "      <th>price_usd</th>\n", "      <th>__row_index</th>\n", "      <th>day</th>\n", "      <th>accounting_period</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2021-11-28 15:09:07+00:00</td>\n", "      <td>4GzTS6kEMo1hfxumJ4khtEE43FYwcW5fbvLz2jH45yB5DQgK4HNVm8kLojq1Xtwv2zRegWrSZxtFHRMmCJwUsPqE</td>\n", "      <td>0.69847</td>\n", "      <td>True</td>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>9TaMBYWxMZ9tLbTQD1aZFB582ZuRHTbwbG6fqvmeN88T</td>\n", "      <td>6o3ggULJGUBX8CvsGhomfZFirUYdH3wVdgT2d4Q7yHb8</td>\n", "      <td>Stoned Ape Crew</td>\n", "      <td>2021-11-28T15:00:00.000Z</td>\n", "      <td>189.631097</td>\n", "      <td>0</td>\n", "      <td>2021-11-28</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2021-11-28 15:09:08+00:00</td>\n", "      <td>5ZCpsDqpir9mmggr3v8z77CfCznBXAcAf76S9SuHGmkRzJpEL7kPiV5GYDdg6sWAWL63dgtBxNohaB99jmtiwyZ2</td>\n", "      <td>0.69847</td>\n", "      <td>True</td>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>8X3Js5RzxHTXQrMRmL7kVkgw7DogtF9Vg6SuMynZhUag</td>\n", "      <td>6o3ggULJGUBX8CvsGhomfZFirUYdH3wVdgT2d4Q7yHb8</td>\n", "      <td>Stoned Ape Crew</td>\n", "      <td>2021-11-28T15:00:00.000Z</td>\n", "      <td>189.631097</td>\n", "      <td>1</td>\n", "      <td>2021-11-28</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2021-11-28 15:12:10+00:00</td>\n", "      <td>2VbgkpGQJtPmxQNEyam4puppZ63Tm9c43rFZv6cdXUcUZEbBjA9wXJyij4qj9XJNYePTDjVZiC7WyahkM49fYdcS</td>\n", "      <td>0.69847</td>\n", "      <td>True</td>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>FBBjTYaoyKKT1dSB6jokTbtThPR9SD4AC1bAbLVCQneS</td>\n", "      <td>6o3ggULJGUBX8CvsGhomfZFirUYdH3wVdgT2d4Q7yHb8</td>\n", "      <td>Stoned Ape Crew</td>\n", "      <td>2021-11-28T15:00:00.000Z</td>\n", "      <td>189.631097</td>\n", "      <td>2</td>\n", "      <td>2021-11-28</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2021-11-28 15:12:11+00:00</td>\n", "      <td>3mxEY3YSrhKtQuUrNnv3bzAjosTtWckjTU3KpBGFgyyomv28CYeLR18epaMtwPSkXkrc15Lfhpt3D5mS2w1i4KsX</td>\n", "      <td>0.69847</td>\n", "      <td>True</td>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>C5q2aJ1ybZKarCi58omXcA1B3XQKDriEDu41w5VB7JG1</td>\n", "      <td>6o3ggULJGUBX8CvsGhomfZFirUYdH3wVdgT2d4Q7yHb8</td>\n", "      <td>Stoned Ape Crew</td>\n", "      <td>2021-11-28T15:00:00.000Z</td>\n", "      <td>189.631097</td>\n", "      <td>3</td>\n", "      <td>2021-11-28</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2021-11-28 15:12:13+00:00</td>\n", "      <td>2ignswHBmwmWiRRcwrgGrh5z95irhxDDZcsvHh6HGDSLGMmX5ML87EjdRHWjZm6twVUu8egHQNy18aKXTXDNDjkj</td>\n", "      <td>0.69847</td>\n", "      <td>True</td>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>RVwAe9Y23qQLhnfppQWdhmzBWXpc6GLYqoKyTF2XNPH</td>\n", "      <td>6o3ggULJGUBX8CvsGhomfZFirUYdH3wVdgT2d4Q7yHb8</td>\n", "      <td>Stoned Ape Crew</td>\n", "      <td>2021-11-28T15:00:00.000Z</td>\n", "      <td>189.631097</td>\n", "      <td>4</td>\n", "      <td>2021-11-28</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8391</th>\n", "      <td>2022-09-25 20:14:02+00:00</td>\n", "      <td>3TN35JmjD92PooaQbMuHXtwHGskmDt4edZU9zD3JbtqLL3KWQ6sohyB5bPhCrNpYpzpkxiyiFoBnMKmYF1FTAcdp</td>\n", "      <td>0.01847</td>\n", "      <td>True</td>\n", "      <td>NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC</td>\n", "      <td>9v2jUsCgvfkZgzpCJvGeYn2DLYYHB5ES65wETuh614SN</td>\n", "      <td>6yzCdFZ72HRd5xkzAdWrJLJo96XDHUeEKuHXvTvCJktq</td>\n", "      <td>None</td>\n", "      <td>2022-09-25T20:00:00.000Z</td>\n", "      <td>32.956038</td>\n", "      <td>8391</td>\n", "      <td>2022-09-25</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8392</th>\n", "      <td>2022-09-25 20:14:15+00:00</td>\n", "      <td>5SsGuK5zsZCq23jfCUWgVUXWGfsi2L2VDpozwQnm3xhTd2cUQJfv7FKZqkB7uYQcstrcTVrcuXp1iMgZCJqPe1AQ</td>\n", "      <td>0.01847</td>\n", "      <td>True</td>\n", "      <td>NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC</td>\n", "      <td>GZ9m49ExPvtLDJhCAVDi8ViYbw5dVxiNhmJ288WqdyPe</td>\n", "      <td>6yzCdFZ72HRd5xkzAdWrJLJo96XDHUeEKuHXvTvCJktq</td>\n", "      <td>None</td>\n", "      <td>2022-09-25T20:00:00.000Z</td>\n", "      <td>32.956038</td>\n", "      <td>8392</td>\n", "      <td>2022-09-25</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8393</th>\n", "      <td>2022-09-25 20:14:33+00:00</td>\n", "      <td>XKZKKS59wABzLSxTJF89sDZSz2whEhXnUigXChmhTzsM1LoQgGQHR5xcbqKcTxVwUq5kELCUi47WCoBQBbJ6kyu</td>\n", "      <td>0.01847</td>\n", "      <td>True</td>\n", "      <td>NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC</td>\n", "      <td>7CcgoTTJ9iG1Gikc1esqWNpSvKhbDQDRLu3xS8WjCRHT</td>\n", "      <td>6yzCdFZ72HRd5xkzAdWrJLJo96XDHUeEKuHXvTvCJktq</td>\n", "      <td>None</td>\n", "      <td>2022-09-25T20:00:00.000Z</td>\n", "      <td>32.956038</td>\n", "      <td>8393</td>\n", "      <td>2022-09-25</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8394</th>\n", "      <td>2022-09-25 20:14:39+00:00</td>\n", "      <td>2ohv3MDedoDRvtVL5BGbAeB3XsQMw8vNRdpf2xdsgJjBoBd2pmzzzv4x5oxAibUgvCLGmNM8f1ZHoj1EhtLasB1V</td>\n", "      <td>0.01847</td>\n", "      <td>True</td>\n", "      <td>NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC</td>\n", "      <td>DGDS3iQfVJacgeaNDW1HYuXoiRmvzRgdAi5bC1i2u8eP</td>\n", "      <td>6yzCdFZ72HRd5xkzAdWrJLJo96XDHUeEKuHXvTvCJktq</td>\n", "      <td>None</td>\n", "      <td>2022-09-25T20:00:00.000Z</td>\n", "      <td>32.956038</td>\n", "      <td>8394</td>\n", "      <td>2022-09-25</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8395</th>\n", "      <td>2022-09-25 20:14:56+00:00</td>\n", "      <td>5RTKBsk6YFHZoiMiGAEz6o4qeKjLVSNgwvDoLrHENqRv8oHnzexnh6xTydXfSDse2EgMQW3VYuuob2Q97zMUMUMX</td>\n", "      <td>0.01847</td>\n", "      <td>True</td>\n", "      <td>NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC</td>\n", "      <td>********************************************</td>\n", "      <td>6yzCdFZ72HRd5xkzAdWrJLJo96XDHUeEKuHXvTvCJktq</td>\n", "      <td>None</td>\n", "      <td>2022-09-25T20:00:00.000Z</td>\n", "      <td>32.956038</td>\n", "      <td>8395</td>\n", "      <td>2022-09-25</td>\n", "      <td>2022</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>8396 rows × 13 columns</p>\n", "</div>"], "text/plain": ["               block_timestamp                                                                                     tx_id  mint_price  succeeded                                    purchaser                                          mint                                 collection_id nft_collection_name      block_timestamp_hour   price_usd  __row_index         day  accounting_period\n", "0    2021-11-28 15:09:07+00:00  4GzTS6kEMo1hfxumJ4khtEE43FYwcW5fbvLz2jH45yB5DQgK4HNVm8kLojq1Xtwv2zRegWrSZxtFHRMmCJwUsPqE     0.69847       True  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb  9TaMBYWxMZ9tLbTQD1aZFB582ZuRHTbwbG6fqvmeN88T  6o3ggULJGUBX8CvsGhomfZFirUYdH3wVdgT2d4Q7yHb8     Stoned Ape Crew  2021-11-28T15:00:00.000Z  189.631097            0  2021-11-28               2022\n", "1    2021-11-28 15:09:08+00:00  5ZCpsDqpir9mmggr3v8z77CfCznBXAcAf76S9SuHGmkRzJpEL7kPiV5GYDdg6sWAWL63dgtBxNohaB99jmtiwyZ2     0.69847       True  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb  8X3Js5RzxHTXQrMRmL7kVkgw7DogtF9Vg6SuMynZhUag  6o3ggULJGUBX8CvsGhomfZFirUYdH3wVdgT2d4Q7yHb8     Stoned Ape Crew  2021-11-28T15:00:00.000Z  189.631097            1  2021-11-28               2022\n", "2    2021-11-28 15:12:10+00:00  2VbgkpGQJtPmxQNEyam4puppZ63Tm9c43rFZv6cdXUcUZEbBjA9wXJyij4qj9XJNYePTDjVZiC7WyahkM49fYdcS     0.69847       True  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb  FBBjTYaoyKKT1dSB6jokTbtThPR9SD4AC1bAbLVCQneS  6o3ggULJGUBX8CvsGhomfZFirUYdH3wVdgT2d4Q7yHb8     Stoned Ape Crew  2021-11-28T15:00:00.000Z  189.631097            2  2021-11-28               2022\n", "3    2021-11-28 15:12:11+00:00  3mxEY3YSrhKtQuUrNnv3bzAjosTtWckjTU3KpBGFgyyomv28CYeLR18epaMtwPSkXkrc15Lfhpt3D5mS2w1i4KsX     0.69847       True  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb  C5q2aJ1ybZKarCi58omXcA1B3XQKDriEDu41w5VB7JG1  6o3ggULJGUBX8CvsGhomfZFirUYdH3wVdgT2d4Q7yHb8     Stoned Ape Crew  2021-11-28T15:00:00.000Z  189.631097            3  2021-11-28               2022\n", "4    2021-11-28 15:12:13+00:00  2ignswHBmwmWiRRcwrgGrh5z95irhxDDZcsvHh6HGDSLGMmX5ML87EjdRHWjZm6twVUu8egHQNy18aKXTXDNDjkj     0.69847       True  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb   RVwAe9Y23qQLhnfppQWdhmzBWXpc6GLYqoKyTF2XNPH  6o3ggULJGUBX8CvsGhomfZFirUYdH3wVdgT2d4Q7yHb8     Stoned Ape Crew  2021-11-28T15:00:00.000Z  189.631097            4  2021-11-28               2022\n", "...                        ...                                                                                       ...         ...        ...                                          ...                                           ...                                           ...                 ...                       ...         ...          ...         ...                ...\n", "8391 2022-09-25 20:14:02+00:00  3TN35JmjD92PooaQbMuHXtwHGskmDt4edZU9zD3JbtqLL3KWQ6sohyB5bPhCrNpYpzpkxiyiFoBnMKmYF1FTAcdp     0.01847       True  NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC  9v2jUsCgvfkZgzpCJvGeYn2DLYYHB5ES65wETuh614SN  6yzCdFZ72HRd5xkzAdWrJLJo96XDHUeEKuHXvTvCJktq                None  2022-09-25T20:00:00.000Z   32.956038         8391  2022-09-25               2022\n", "8392 2022-09-25 20:14:15+00:00  5SsGuK5zsZCq23jfCUWgVUXWGfsi2L2VDpozwQnm3xhTd2cUQJfv7FKZqkB7uYQcstrcTVrcuXp1iMgZCJqPe1AQ     0.01847       True  NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC  GZ9m49ExPvtLDJhCAVDi8ViYbw5dVxiNhmJ288WqdyPe  6yzCdFZ72HRd5xkzAdWrJLJo96XDHUeEKuHXvTvCJktq                None  2022-09-25T20:00:00.000Z   32.956038         8392  2022-09-25               2022\n", "8393 2022-09-25 20:14:33+00:00   XKZKKS59wABzLSxTJF89sDZSz2whEhXnUigXChmhTzsM1LoQgGQHR5xcbqKcTxVwUq5kELCUi47WCoBQBbJ6kyu     0.01847       True  NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC  7CcgoTTJ9iG1Gikc1esqWNpSvKhbDQDRLu3xS8WjCRHT  6yzCdFZ72HRd5xkzAdWrJLJo96XDHUeEKuHXvTvCJktq                None  2022-09-25T20:00:00.000Z   32.956038         8393  2022-09-25               2022\n", "8394 2022-09-25 20:14:39+00:00  2ohv3MDedoDRvtVL5BGbAeB3XsQMw8vNRdpf2xdsgJjBoBd2pmzzzv4x5oxAibUgvCLGmNM8f1ZHoj1EhtLasB1V     0.01847       True  NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC  DGDS3iQfVJacgeaNDW1HYuXoiRmvzRgdAi5bC1i2u8eP  6yzCdFZ72HRd5xkzAdWrJLJo96XDHUeEKuHXvTvCJktq                None  2022-09-25T20:00:00.000Z   32.956038         8394  2022-09-25               2022\n", "8395 2022-09-25 20:14:56+00:00  5RTKBsk6YFHZoiMiGAEz6o4qeKjLVSNgwvDoLrHENqRv8oHnzexnh6xTydXfSDse2EgMQW3VYuuob2Q97zMUMUMX     0.01847       True  NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC  ********************************************  6yzCdFZ72HRd5xkzAdWrJLJo96XDHUeEKuHXvTvCJktq                None  2022-09-25T20:00:00.000Z   32.956038         8395  2022-09-25               2022\n", "\n", "[8396 rows x 13 columns]"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["royalties = [\n", "    {\n", "        value: 0.072        \n", "    },\n", "    {\n", "        value\n", "    }\n", "    \n", "]\n", "\n", "collections = \"', '\".join(utils.collections)\n", "wallet_list = \"', '\".join(company_wallets[\"sac_pubkey\"].to_list())\n", "        \n", "nft_mints = utils.query_df(f\"\"\"\n", "    SELECT\n", "        mints.block_timestamp, mints.tx_id, mints.mint_price, mints.succeeded, mints.purchaser, mints.mint, nft.collection_id, nft.nft_collection_name, \n", "        date_trunc(\"hour\", mints.block_timestamp) as block_timestamp_hour, prices.price as price_usd\n", "    FROM\n", "        solana.nft.fact_nft_mints mints\n", "        join solana.nft.dim_nft_metadata nft on mints.mint = nft.mint\n", "        join solana.price.ez_prices_hourly prices on block_timestamp_hour = prices.hour\n", "    WHERE\n", "        prices.token_address is null and\n", "        nft.collection_id in ('{collections}')\n", "    ORDER BY\n", "        block_timestamp;\"\"\")\n", "\n", "# nft_mints = utils.merge_company_wallets(nft_mints, company_wallets, \"purchaser\")\n", "# floor_sweeps = utils.move_columns_to_front(floor_sweeps, [\"block_timestamp\", \"accounting_period\", \"tx_id\", \"by_sac\", \"buyer_address\", \"price_usd\"])\n", "# floor_sweeps.info()\n", "nft_mints"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>block_timestamp</th>\n", "      <th>tx_id</th>\n", "      <th>mint_price</th>\n", "      <th>succeeded</th>\n", "      <th>purchaser</th>\n", "      <th>mint</th>\n", "      <th>collection_id</th>\n", "      <th>block_timestamp_hour</th>\n", "      <th>price_usd</th>\n", "      <th>__row_index</th>\n", "      <th>day</th>\n", "      <th>accounting_period</th>\n", "      <th>cost</th>\n", "    </tr>\n", "    <tr>\n", "      <th>nft_collection_name</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>N<PERSON><PERSON></th>\n", "      <td>4196</td>\n", "      <td>4196</td>\n", "      <td>4196</td>\n", "      <td>4196</td>\n", "      <td>4196</td>\n", "      <td>4196</td>\n", "      <td>4196</td>\n", "      <td>4196</td>\n", "      <td>4196</td>\n", "      <td>4196</td>\n", "      <td>4196</td>\n", "      <td>4196</td>\n", "      <td>4196</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Stoned Ape Crew</th>\n", "      <td>4200</td>\n", "      <td>4200</td>\n", "      <td>4200</td>\n", "      <td>4200</td>\n", "      <td>4200</td>\n", "      <td>4200</td>\n", "      <td>4200</td>\n", "      <td>4200</td>\n", "      <td>4200</td>\n", "      <td>4200</td>\n", "      <td>4200</td>\n", "      <td>4200</td>\n", "      <td>4200</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                     block_timestamp  tx_id  mint_price  succeeded  purchaser  mint  collection_id  block_timestamp_hour  price_usd  __row_index   day  accounting_period  cost\n", "nft_collection_name                                                                                                                                                            \n", "Nuked Apes                      4196   4196        4196       4196       4196  4196           4196                  4196       4196         4196  4196               4196  4196\n", "Stoned Ape Crew                 4200   4200        4200       4200       4200  4200           4200                  4200       4200         4200  4200               4200  4200"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["# Fill in missing nft_collection_name values with \"Nuked Apes\"\n", "nft_mints.loc[nft_mints['nft_collection_name'].isna(), 'nft_collection_name'] = 'Nuked Apes'\n", "nft_mints[\"cost\"] = nft_mints[\"mint_price\"] * nft_mints[\"price_usd\"]\n", "nft_mints.groupby('nft_collection_name').count()"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/plain": ["nft_collection_name\n", "Nuked Apes           6684.156107\n", "Stoned Ape Crew    547720.199874\n", "Name: cost, dtype: float64"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["nft_mints.groupby('nft_collection_name')['cost'].sum()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["found 10 rows\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>marketplace</th>\n", "      <th>marketplace_version</th>\n", "      <th>block_timestamp</th>\n", "      <th>block_id</th>\n", "      <th>tx_id</th>\n", "      <th>succeeded</th>\n", "      <th>index</th>\n", "      <th>inner_index</th>\n", "      <th>program_id</th>\n", "      <th>buyer_address</th>\n", "      <th>...</th>\n", "      <th>authority</th>\n", "      <th>metadata</th>\n", "      <th>image_url</th>\n", "      <th>metadata_uri</th>\n", "      <th>ez_nft_sales_id</th>\n", "      <th>inserted_timestamp</th>\n", "      <th>modified_timestamp</th>\n", "      <th>__row_index</th>\n", "      <th>day</th>\n", "      <th>accounting_period</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>magic eden v1</td>\n", "      <td>v1</td>\n", "      <td>2021-11-28 20:29:04+00:00</td>\n", "      <td>*********</td>\n", "      <td>2NocT4CK8yB5CLPFsM8S1XBzFwkE61FfAT3i1zu1scVynt2qo3X584XXmGuoBFEazZwSvxS2naZCpYuSX237kV3F</td>\n", "      <td>True</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>MEisE1HzehtrDpAAT8PnLHjpSSkRYakotTuJRPjTpo8</td>\n", "      <td>A9gRAYuYF3fzUd91N948vZZyd7Rf5nvEGKhutAz4vgVf</td>\n", "      <td>...</td>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>[{'trait_type': 'Role', 'value': 'Farmer'}, {'trait_type': 'Background', 'value': 'Blue'}, {'trait_type': 'Eyes', 'value': 'Angry'}, {'trait_type': 'Form', 'value': 'White'}, {'trait_type': 'Hats', 'value': 'Brown Flat Cap'}, {'trait_type': 'Mouth', 'value': 'Smoking Joint'}, {'trait_type': 'Hair', 'value': 'Smooth Long White'}, {'trait_type': 'Hand with Items', 'value': 'Brownie White Fur'}, {'trait_type': 'Random Things', 'value': 'Cannabis Plant'}, {'trait_type': 'Upper Part', 'value': 'Brown Farmer Overall'}, {'trait_type': 'Genesis Role?', 'value': 'Yes'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]</td>\n", "      <td>https://arweave.net/yUoHYmtBw9e5-q_-RBV4UINm-VHdNZtChkDZDfcsQ7E?ext=webp</td>\n", "      <td>https://arweave.net/qdL8zHFwEtv_AA1cOMMojDfWlwyhpG1-K9-LoyxbfzM</td>\n", "      <td>57582d85662a0b7b78d55b44863eeae0</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>0</td>\n", "      <td>2021-11-28</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>magic eden v1</td>\n", "      <td>v1</td>\n", "      <td>2021-11-28 20:29:48+00:00</td>\n", "      <td>109163543</td>\n", "      <td>4r1mQCdA6e7MpGCKSwscezLBz85rR6B6Re3SwgventnC8hnXKVPya7p3NvNQ3hydaEjUuG9zmeAsLvSdo7p3t7FF</td>\n", "      <td>True</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>MEisE1HzehtrDpAAT8PnLHjpSSkRYakotTuJRPjTpo8</td>\n", "      <td>A9gRAYuYF3fzUd91N948vZZyd7Rf5nvEGKhutAz4vgVf</td>\n", "      <td>...</td>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>[{'trait_type': 'Role', 'value': 'Businessman'}, {'trait_type': 'Background', 'value': 'Blue Gradient'}, {'trait_type': 'Ears', 'value': 'Double Piercing'}, {'trait_type': 'Facetats', 'value': 'Tiny SAC'}, {'trait_type': 'Eyes', 'value': 'Red'}, {'trait_type': 'Eyes Items', 'value': 'Red John <PERSON> Glasses'}, {'trait_type': 'Form', 'value': 'Light Blonde'}, {'trait_type': 'Mouth', 'value': 'Grinding'}, {'trait_type': 'Hand with Items', 'value': 'Business Lighter Light Blonde Fur'}, {'trait_type': 'Upper Part', 'value': 'Brown Business Suit With Bow Tie &amp; Hat'}, {'trait_type': 'Genesis Role?', 'value': 'Yes'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]</td>\n", "      <td>https://arweave.net/2xkvo0nDtD8-myprcq4WxdQiAhQVgzrzlGORV-6vybw?ext=webp</td>\n", "      <td>https://arweave.net/UW5zRS9V_AfHY4S4yl6GBkLBoog8m9c_NijsxPXQJV8</td>\n", "      <td>85e9a3ce561bc59144ccdc2693a188c4</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>1</td>\n", "      <td>2021-11-28</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>magic eden v1</td>\n", "      <td>v1</td>\n", "      <td>2021-11-28 20:29:50+00:00</td>\n", "      <td>109163547</td>\n", "      <td>cKE1mbQYRQHxXPECDTXXuxcRJAARHPsoa1WvbEoQTgAGS8nSzNVJkcZm2dqghdLfmX2QoiVCX6h44n3XZyUKzoJ</td>\n", "      <td>True</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>MEisE1HzehtrDpAAT8PnLHjpSSkRYakotTuJRPjTpo8</td>\n", "      <td>HevD7LwzN2GFEqbLxziwVFWVS1VGN4hLopCzbVFE9gDW</td>\n", "      <td>...</td>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>[{'trait_type': 'Role', 'value': 'Businessman'}, {'trait_type': 'Background', 'value': 'Hippie 3'}, {'trait_type': 'Companions', 'value': 'Blue Parrot'}, {'trait_type': 'Ears', 'value': 'Golden Earring'}, {'trait_type': 'Eyes', 'value': 'Red'}, {'trait_type': 'Eyes Items', 'value': 'Gold Monocle'}, {'trait_type': 'Form', 'value': 'White'}, {'trait_type': 'Mouth', 'value': 'Open'}, {'trait_type': 'Hair', 'value': 'Backstreetboy Style White'}, {'trait_type': 'Hand with Items', 'value': 'Joint White Fur'}, {'trait_type': 'Random Things', 'value': 'Questionmark'}, {'trait_type': 'Upper Part', 'value': 'Just Hit It Hoodie'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]</td>\n", "      <td>https://arweave.net/gA0nwFqylzdosOGIkwvkJAW8ZXA_wlcW5ouHPD_pXec?ext=webp</td>\n", "      <td>https://arweave.net/gREYlKDwMtHcHQd3dPAgPKD4t-9B_Ug6m7qYWxuNMsI</td>\n", "      <td>d2733e23db79f4046e8e2baf5940f9b0</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2</td>\n", "      <td>2021-11-28</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>magic eden v1</td>\n", "      <td>v1</td>\n", "      <td>2021-11-28 20:29:58+00:00</td>\n", "      <td>109163564</td>\n", "      <td>MKxkuyR2hCTpZCfYcAPErBrMtU41HMgtcL35Unzm241Tmn6YFhADkVYNzvBy7oxbpfiAQMLa5q6A89vEdxSZipr</td>\n", "      <td>True</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>MEisE1HzehtrDpAAT8PnLHjpSSkRYakotTuJRPjTpo8</td>\n", "      <td>Byhp5fvBUteNZZLMdFYTbix6Kv89Xi7KEys7xruqrquu</td>\n", "      <td>...</td>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>[{'trait_type': 'Role', 'value': 'Farmer'}, {'trait_type': 'Background', 'value': '<PERSON>b<PERSON> Gradient'}, {'trait_type': 'Eyes', 'value': 'Angry'}, {'trait_type': 'Eyes Items', 'value': 'Flashing Eyes'}, {'trait_type': 'Form', 'value': 'Light Blonde'}, {'trait_type': 'Hats', 'value': 'Pothat'}, {'trait_type': 'Mouth', 'value': 'Puffin'}, {'trait_type': 'Hair', 'value': 'Long Wavy Light Blonde'}, {'trait_type': 'Hand with Items', 'value': 'Watering Can Light Blonde Fur'}, {'trait_type': 'Upper Part', 'value': 'Brown Hippie Top'}, {'trait_type': 'Genesis Role?', 'value': 'Yes'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]</td>\n", "      <td>https://arweave.net/ZgeavQqfLnvk7tYhd3keCpMAZXTLbNZaCZdzRpq-g2E?ext=webp</td>\n", "      <td>https://arweave.net/RpZ3kbheRn2EPLNVHNMSdW4o_n8kgryv3Dw1O1j_CQM</td>\n", "      <td>b8a239bec0cbb24fe59d4305d6c5cb86</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>3</td>\n", "      <td>2021-11-28</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>magic eden v1</td>\n", "      <td>v1</td>\n", "      <td>2021-11-28 20:30:28+00:00</td>\n", "      <td>109163616</td>\n", "      <td>mYKj8SgdwbHzeZbuUyPLWho82BGJYyqH6M5VmJwpUdKVM5fQLZSeHKMStATbn7ddwea4LmarCrg39mKspXTPbWY</td>\n", "      <td>True</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>MEisE1HzehtrDpAAT8PnLHjpSSkRYakotTuJRPjTpo8</td>\n", "      <td>8V98j4xGca9CDC4H8uYiTyvZEuYkSjA4VLX9mytYPHc2</td>\n", "      <td>...</td>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>[{'trait_type': 'Role', 'value': 'Artist'}, {'trait_type': 'Background', 'value': 'Blue'}, {'trait_type': 'Ears', 'value': 'Double Piercing'}, {'trait_type': 'Eyes', 'value': 'Red'}, {'trait_type': 'Eyes Items', 'value': 'Red John <PERSON>'}, {'trait_type': 'Form', 'value': 'White'}, {'trait_type': 'Mouth', 'value': 'Grinding'}, {'trait_type': 'Hair', 'value': 'Yellow Beanie White Fur'}, {'trait_type': 'Hand with Items', 'value': 'Brownie &amp; Joint White Fur'}, {'trait_type': 'Upper Part', 'value': 'Green Poloshirt'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]</td>\n", "      <td>https://arweave.net/fzYog-MyUHni083HllvMngVySxy0DMkUXAMtv8mipTQ?ext=webp</td>\n", "      <td>https://arweave.net/Y8r6vQR4CsDeRAPR2X4zhDQv0l6UG6OCGLUj3lYWawY</td>\n", "      <td>a172ab4dc669fc698f83761f05aded10</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>4</td>\n", "      <td>2021-11-28</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>magic eden v1</td>\n", "      <td>v1</td>\n", "      <td>2021-11-28 20:30:35+00:00</td>\n", "      <td>109163630</td>\n", "      <td>3yLgfaQhWhotBPA9gjaxEENV5fpA9WgBnRgJrqumNTb3f2ZMjNhse94Y9GRSghkCjMkG65SYekhkgYAHD9GMXiE7</td>\n", "      <td>True</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>MEisE1HzehtrDpAAT8PnLHjpSSkRYakotTuJRPjTpo8</td>\n", "      <td>2Ja2L6VeNkg2TJA9da1Qg573PkpF5Sb3bNcWrrQqBBVT</td>\n", "      <td>...</td>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>[{'trait_type': 'Role', 'value': 'Artist'}, {'trait_type': 'Background', 'value': 'Space 2'}, {'trait_type': 'Eyes', 'value': 'Red'}, {'trait_type': 'Form', 'value': 'Red Gradient'}, {'trait_type': 'Hats', 'value': 'Weed Hairband'}, {'trait_type': 'Mouth', 'value': 'Rolled Joint'}, {'trait_type': 'Hair', 'value': 'Facon Style Red'}, {'trait_type': 'Neck', 'value': 'Necklace'}, {'trait_type': 'Random Things', 'value': 'Questionmark'}, {'trait_type': 'Upper Part', 'value': 'Violet Canna Jacket'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]</td>\n", "      <td>https://arweave.net/gH5qHxBH0Zw6WGZPOQZQQHR9GHDpynWh9nEnCU-hvPo?ext=webp</td>\n", "      <td>https://arweave.net/SW5kFmzBzsuWjWuXYvsXejPWgfYX2cQmm3M39l7Rubc</td>\n", "      <td>8aeb1130543ad6354beb0e1b564b336e</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>5</td>\n", "      <td>2021-11-28</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>magic eden v1</td>\n", "      <td>v1</td>\n", "      <td>2021-11-28 20:30:38+00:00</td>\n", "      <td>109163636</td>\n", "      <td>5fTwPgivSzeKNLBA5emxc4D1JBzR4FYWQTPeZSn4ZSKZWGH2muXtCXVSpaA2wepyQorFpwHndzNTC99SQLGjFPLv</td>\n", "      <td>True</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>MEisE1HzehtrDpAAT8PnLHjpSSkRYakotTuJRPjTpo8</td>\n", "      <td>2tg3t7i5ZZJwfAfjbd7qQppbpY1QbS3jtuXwK43WX3EN</td>\n", "      <td>...</td>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>[{'trait_type': 'Role', 'value': 'Businessman'}, {'trait_type': 'Background', 'value': 'Blue Gradient'}, {'trait_type': 'Ears', 'value': 'Leaf behind Ear'}, {'trait_type': 'Eyes', 'value': 'Sad'}, {'trait_type': 'Form', 'value': 'Rainbow'}, {'trait_type': 'Mouth', 'value': 'Rainbow Teeth'}, {'trait_type': 'Hair', 'value': 'Smooth Long Rainbow'}, {'trait_type': 'Hand with Items', 'value': 'Brownie Rainbow Fur'}, {'trait_type': 'Upper Part', 'value': 'Black Business Suit With Bow Tie &amp; Hat'}, {'trait_type': 'Genesis Role?', 'value': 'Yes'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]</td>\n", "      <td>https://arweave.net/o3FE3zv0yfEwwz7fWL1vkjZU5QRv8wrF9s720KgM4EA?ext=webp</td>\n", "      <td>https://arweave.net/Ub8rAQDZIibpX-Hyzmo54yAK7_J9Vro_qIbd2jM2Zb0</td>\n", "      <td>72b384534f4bb5b5606583b1f9e22a65</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>6</td>\n", "      <td>2021-11-28</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>magic eden v1</td>\n", "      <td>v1</td>\n", "      <td>2021-11-28 20:30:48+00:00</td>\n", "      <td>109163655</td>\n", "      <td>4d3NHZrcS3uhQVQpxutzPnbK6wqeQUGQekWrNEGhuRRHLuq5EAYtuJFF1PK5j7qyeQx4LLCNPCN1zdsNZT8qgbwC</td>\n", "      <td>True</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>MEisE1HzehtrDpAAT8PnLHjpSSkRYakotTuJRPjTpo8</td>\n", "      <td>H51c4azJs1qVY5btFkG475DD9AY2ckbtya57oMQbbr6D</td>\n", "      <td>...</td>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>[{'trait_type': 'Role', 'value': 'Farmer'}, {'trait_type': 'Background', 'value': 'Solana'}, {'trait_type': 'Companions', 'value': 'Weedy with Glass<PERSON>'}, {'trait_type': 'Facetats', 'value': '<PERSON><PERSON>'}, {'trait_type': 'Eyes', 'value': 'Blue'}, {'trait_type': 'Eyes Items', 'value': '420 Googles'}, {'trait_type': 'Form', 'value': 'Light Blonde'}, {'trait_type': 'Hats', 'value': 'Dark Grey Headphones'}, {'trait_type': 'Mouth', 'value': 'Money Joint'}, {'trait_type': 'Hair', 'value': 'Long Wavy Light Blonde'}, {'trait_type': 'Nose', 'value': 'Nose Piercing'}, {'trait_type': 'Hand with Items', 'value': 'Farmer Scissors Light Blonde Fur'}, {'trait_type': 'Upper Part', 'value': 'Red Puff Puff Hoodie'}, {'trait_type': 'Genesis Role?', 'value': 'Yes'}]</td>\n", "      <td>https://www.arweave.net/rIi_vqCeZP0AqmAf1GijqMoYxqEZ7jCJgIRhy4nwAhY?ext=png</td>\n", "      <td>https://arweave.net/cOc-vmEpAT30GtTIHpWLacZBt4pkKP9P7tkHSt6EkOQ</td>\n", "      <td>7e922c912d1e0674834968582bcdd2ec</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>7</td>\n", "      <td>2021-11-28</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>magic eden v1</td>\n", "      <td>v1</td>\n", "      <td>2021-11-28 20:31:01+00:00</td>\n", "      <td>109163683</td>\n", "      <td>BbxpmbPRYHevDdaK8qNVrsi71VrAMoKdihrniTWELaS1ebwqUBcxL3NvofX3WqMuSFUsoyYPypSkSzb5FqRM3MU</td>\n", "      <td>True</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>MEisE1HzehtrDpAAT8PnLHjpSSkRYakotTuJRPjTpo8</td>\n", "      <td>EtbLfuZ9ro4thPCasgJWWpCMaUFbjLG4s1B3BSr6zdfm</td>\n", "      <td>...</td>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>[{'trait_type': 'Role', 'value': 'Artist'}, {'trait_type': 'Background', 'value': 'Lightbrown'}, {'trait_type': 'Ears', 'value': 'Double Piercing'}, {'trait_type': 'Eyes', 'value': 'Red'}, {'trait_type': 'Form', 'value': 'Dark Blonde'}, {'trait_type': 'Mouth', 'value': 'Rainbow Teeth'}, {'trait_type': 'Hair', 'value': 'Long Wavy Dark Blonde'}, {'trait_type': 'Nose', 'value': 'Nose Piercing'}, {'trait_type': 'Upper Part', 'value': 'Just Hit It Hoodie'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]</td>\n", "      <td>https://arweave.net/hMmZqWbfe_XLpmpEfOpuXAA528DNBUdSeBzwx4MryYE?ext=webp</td>\n", "      <td>https://arweave.net/7OEMgAK1y4nMyCu8Fzkzxg2A_j8Popf9ED_44saLEm8</td>\n", "      <td>e045361bfd713d2819ca98409dd50266</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>8</td>\n", "      <td>2021-11-28</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>magic eden v1</td>\n", "      <td>v1</td>\n", "      <td>2021-11-28 20:31:03+00:00</td>\n", "      <td>109163685</td>\n", "      <td>5QShxqPFPNizRhSNHeiwXqejirG2f8AbPgVsp1xumwPp9azwSXo4zvUnrPCg511HcTVZK68jbnJ5gK4x8ziMmFet</td>\n", "      <td>True</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>MEisE1HzehtrDpAAT8PnLHjpSSkRYakotTuJRPjTpo8</td>\n", "      <td>7wTGmX7g4finjeuMHGB2rDPWLjK9ZuecyCPWeTUB74ZY</td>\n", "      <td>...</td>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>[{'trait_type': 'Role', 'value': 'Farmer'}, {'trait_type': 'Background', 'value': 'Blue'}, {'trait_type': 'Eyes', 'value': 'Black'}, {'trait_type': 'Eyes Items', 'value': '<PERSON>'}, {'trait_type': 'Form', 'value': '<PERSON><PERSON> Brown'}, {'trait_type': 'Hats', 'value': 'Blue Beret'}, {'trait_type': 'Mouth', 'value': 'Eating Pizza'}, {'trait_type': 'Hair', 'value': 'Long Wavy Chocbrown'}, {'trait_type': 'Nose', 'value': 'Nose Piercing'}, {'trait_type': 'Hand with Items', 'value': 'Pink Donut Chocbrown Fur'}, {'trait_type': 'Random Things', 'value': 'Cannabis Plant'}, {'trait_type': 'Upper Part', 'value': 'Pink Canna Jacket'}, {'trait_type': 'Genesis Role?', 'value': 'Yes'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]</td>\n", "      <td>https://arweave.net/T9yc4OjAhASqGZnxQ4gm6rmtqz7fS62YirqM4Y9XI78?ext=webp</td>\n", "      <td>https://arweave.net/MJFRKaezWFffQL48eVIIrklUyzwc1cBzvBYFpYlPdds</td>\n", "      <td>1ed0292f13705a4da8a6ffeb87af13e2</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>9</td>\n", "      <td>2021-11-28</td>\n", "      <td>2022</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10 rows × 34 columns</p>\n", "</div>"], "text/plain": ["     marketplace marketplace_version           block_timestamp   block_id                                                                                     tx_id  succeeded  index inner_index                                   program_id                                 buyer_address  ...                                    authority                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     metadata                                                                    image_url                                                     metadata_uri                   ez_nft_sales_id        inserted_timestamp        modified_timestamp __row_index         day accounting_period\n", "0  magic eden v1                  v1 2021-11-28 20:29:04+00:00  *********  2NocT4CK8yB5CLPFsM8S1XBzFwkE61FfAT3i1zu1scVynt2qo3X584XXmGuoBFEazZwSvxS2naZCpYuSX237kV3F       True      0        None  MEisE1HzehtrDpAAT8PnLHjpSSkRYakotTuJRPjTpo8  A9gRAYuYF3fzUd91N948vZZyd7Rf5nvEGKhutAz4vgVf  ...  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb                                                                                                                              [{'trait_type': 'Role', 'value': 'Farmer'}, {'trait_type': 'Background', 'value': 'Blue'}, {'trait_type': 'Eyes', 'value': 'Angry'}, {'trait_type': 'Form', 'value': 'White'}, {'trait_type': 'Hats', 'value': 'Brown Flat Cap'}, {'trait_type': 'Mouth', 'value': 'Smoking Joint'}, {'trait_type': 'Hair', 'value': 'Smooth Long White'}, {'trait_type': 'Hand with Items', 'value': 'Brownie White Fur'}, {'trait_type': 'Random Things', 'value': 'Cannabis Plant'}, {'trait_type': 'Upper Part', 'value': 'Brown Farmer Overall'}, {'trait_type': 'Genesis Role?', 'value': 'Yes'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]     https://arweave.net/yUoHYmtBw9e5-q_-RBV4UINm-VHdNZtChkDZDfcsQ7E?ext=webp  https://arweave.net/qdL8zHFwEtv_AA1cOMMojDfWlwyhpG1-K9-LoyxbfzM  57582d85662a0b7b78d55b44863eeae0  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z           0  2021-11-28              2022\n", "1  magic eden v1                  v1 2021-11-28 20:29:48+00:00  109163543  4r1mQCdA6e7MpGCKSwscezLBz85rR6B6Re3SwgventnC8hnXKVPya7p3NvNQ3hydaEjUuG9zmeAsLvSdo7p3t7FF       True      0        None  MEisE1HzehtrDpAAT8PnLHjpSSkRYakotTuJRPjTpo8  A9gRAYuYF3fzUd91N948vZZyd7Rf5nvEGKhutAz4vgVf  ...  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb                                                                            [{'trait_type': 'Role', 'value': 'Businessman'}, {'trait_type': 'Background', 'value': 'Blue Gradient'}, {'trait_type': 'Ears', 'value': 'Double Piercing'}, {'trait_type': 'Facetats', 'value': 'Tiny SAC'}, {'trait_type': 'Eyes', 'value': 'Red'}, {'trait_type': 'Eyes Items', 'value': '<PERSON> John <PERSON> Glasses'}, {'trait_type': 'Form', 'value': 'Light Blonde'}, {'trait_type': 'Mouth', 'value': 'Grinding'}, {'trait_type': 'Hand with Items', 'value': 'Business Lighter Light Blonde Fur'}, {'trait_type': 'Upper Part', 'value': 'Brown Business Suit With Bow Tie & Hat'}, {'trait_type': 'Genesis Role?', 'value': 'Yes'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]     https://arweave.net/2xkvo0nDtD8-myprcq4WxdQiAhQVgzrzlGORV-6vybw?ext=webp  https://arweave.net/UW5zRS9V_AfHY4S4yl6GBkLBoog8m9c_NijsxPXQJV8  85e9a3ce561bc59144ccdc2693a188c4  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z           1  2021-11-28              2022\n", "2  magic eden v1                  v1 2021-11-28 20:29:50+00:00  109163547   cKE1mbQYRQHxXPECDTXXuxcRJAARHPsoa1WvbEoQTgAGS8nSzNVJkcZm2dqghdLfmX2QoiVCX6h44n3XZyUKzoJ       True      0        None  MEisE1HzehtrDpAAT8PnLHjpSSkRYakotTuJRPjTpo8  HevD7LwzN2GFEqbLxziwVFWVS1VGN4hLopCzbVFE9gDW  ...  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb                                                                  [{'trait_type': 'Role', 'value': 'Businessman'}, {'trait_type': 'Background', 'value': 'Hippie 3'}, {'trait_type': 'Companions', 'value': 'Blue Parrot'}, {'trait_type': 'Ears', 'value': 'Golden Earring'}, {'trait_type': 'Eyes', 'value': 'Red'}, {'trait_type': 'Eyes Items', 'value': 'Gold Monocle'}, {'trait_type': 'Form', 'value': 'White'}, {'trait_type': 'Mouth', 'value': 'Open'}, {'trait_type': 'Hair', 'value': 'Backstreetboy Style White'}, {'trait_type': 'Hand with Items', 'value': 'Joint White Fur'}, {'trait_type': 'Random Things', 'value': 'Questionmark'}, {'trait_type': 'Upper Part', 'value': 'Just Hit It Hoodie'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]     https://arweave.net/gA0nwFqylzdosOGIkwvkJAW8ZXA_wlcW5ouHPD_pXec?ext=webp  https://arweave.net/gREYlKDwMtHcHQd3dPAgPKD4t-9B_Ug6m7qYWxuNMsI  d2733e23db79f4046e8e2baf5940f9b0  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z           2  2021-11-28              2022\n", "3  magic eden v1                  v1 2021-11-28 20:29:58+00:00  109163564   MKxkuyR2hCTpZCfYcAPErBrMtU41HMgtcL35Unzm241Tmn6YFhADkVYNzvBy7oxbpfiAQMLa5q6A89vEdxSZipr       True      0        None  MEisE1HzehtrDpAAT8PnLHjpSSkRYakotTuJRPjTpo8  Byhp5fvBUteNZZLMdFYTbix6Kv89Xi7KEys7xruqrquu  ...  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb                                                                                                                [{'trait_type': 'Role', 'value': 'Farmer'}, {'trait_type': 'Background', 'value': 'Darkblue Gradient'}, {'trait_type': 'Eyes', 'value': 'Angry'}, {'trait_type': 'Eyes Items', 'value': 'Flashing Eyes'}, {'trait_type': 'Form', 'value': 'Light Blonde'}, {'trait_type': 'Hats', 'value': 'Pothat'}, {'trait_type': 'Mouth', 'value': 'Puffin'}, {'trait_type': 'Hair', 'value': 'Long Wavy Light Blonde'}, {'trait_type': 'Hand with Items', 'value': 'Watering Can Light Blonde Fur'}, {'trait_type': 'Upper Part', 'value': 'Brown Hippie Top'}, {'trait_type': 'Genesis Role?', 'value': 'Yes'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]     https://arweave.net/ZgeavQqfLnvk7tYhd3keCpMAZXTLbNZaCZdzRpq-g2E?ext=webp  https://arweave.net/RpZ3kbheRn2EPLNVHNMSdW4o_n8kgryv3Dw1O1j_CQM  b8a239bec0cbb24fe59d4305d6c5cb86  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z           3  2021-11-28              2022\n", "4  magic eden v1                  v1 2021-11-28 20:30:28+00:00  109163616   mYKj8SgdwbHzeZbuUyPLWho82BGJYyqH6M5VmJwpUdKVM5fQLZSeHKMStATbn7ddwea4LmarCrg39mKspXTPbWY       True      0        None  MEisE1HzehtrDpAAT8PnLHjpSSkRYakotTuJRPjTpo8  8V98j4xGca9CDC4H8uYiTyvZEuYkSjA4VLX9mytYPHc2  ...  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb                                                                                                                                                                      [{'trait_type': 'Role', 'value': 'Artist'}, {'trait_type': 'Background', 'value': 'Blue'}, {'trait_type': 'Ears', 'value': 'Double Piercing'}, {'trait_type': 'Eyes', 'value': 'Red'}, {'trait_type': 'Eyes Items', 'value': '<PERSON>'}, {'trait_type': 'Form', 'value': 'White'}, {'trait_type': 'Mouth', 'value': 'Grinding'}, {'trait_type': 'Hair', 'value': 'Yellow Beanie White Fur'}, {'trait_type': 'Hand with Items', 'value': 'Brownie & Joint White Fur'}, {'trait_type': 'Upper Part', 'value': 'Green Poloshirt'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]     https://arweave.net/fzYog-MyUHni083HllvMngVySxy0DMkUXAMtv8mipTQ?ext=webp  https://arweave.net/Y8r6vQR4CsDeRAPR2X4zhDQv0l6UG6OCGLUj3lYWawY  a172ab4dc669fc698f83761f05aded10  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z           4  2021-11-28              2022\n", "5  magic eden v1                  v1 2021-11-28 20:30:35+00:00  109163630  3yLgfaQhWhotBPA9gjaxEENV5fpA9WgBnRgJrqumNTb3f2ZMjNhse94Y9GRSghkCjMkG65SYekhkgYAHD9GMXiE7       True      0        None  MEisE1HzehtrDpAAT8PnLHjpSSkRYakotTuJRPjTpo8  2Ja2L6VeNkg2TJA9da1Qg573PkpF5Sb3bNcWrrQqBBVT  ...  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb                                                                                                                                                                                                  [{'trait_type': 'Role', 'value': 'Artist'}, {'trait_type': 'Background', 'value': 'Space 2'}, {'trait_type': 'Eyes', 'value': 'Red'}, {'trait_type': 'Form', 'value': 'Red Gradient'}, {'trait_type': 'Hats', 'value': 'Weed Hairband'}, {'trait_type': 'Mouth', 'value': 'Rolled Joint'}, {'trait_type': 'Hair', 'value': 'Facon Style Red'}, {'trait_type': 'Neck', 'value': 'Necklace'}, {'trait_type': 'Random Things', 'value': 'Questionmark'}, {'trait_type': 'Upper Part', 'value': 'Violet Canna Jacket'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]     https://arweave.net/gH5qHxBH0Zw6WGZPOQZQQHR9GHDpynWh9nEnCU-hvPo?ext=webp  https://arweave.net/SW5kFmzBzsuWjWuXYvsXejPWgfYX2cQmm3M39l7Rubc  8aeb1130543ad6354beb0e1b564b336e  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z           5  2021-11-28              2022\n", "6  magic eden v1                  v1 2021-11-28 20:30:38+00:00  109163636  5fTwPgivSzeKNLBA5emxc4D1JBzR4FYWQTPeZSn4ZSKZWGH2muXtCXVSpaA2wepyQorFpwHndzNTC99SQLGjFPLv       True      0        None  MEisE1HzehtrDpAAT8PnLHjpSSkRYakotTuJRPjTpo8  2tg3t7i5ZZJwfAfjbd7qQppbpY1QbS3jtuXwK43WX3EN  ...  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb                                                                                                                                                     [{'trait_type': 'Role', 'value': 'Businessman'}, {'trait_type': 'Background', 'value': 'Blue Gradient'}, {'trait_type': 'Ears', 'value': 'Leaf behind Ear'}, {'trait_type': 'Eyes', 'value': 'Sad'}, {'trait_type': 'Form', 'value': 'Rainbow'}, {'trait_type': 'Mouth', 'value': 'Rainbow Teeth'}, {'trait_type': 'Hair', 'value': 'Smooth Long Rainbow'}, {'trait_type': 'Hand with Items', 'value': 'Brownie Rainbow Fur'}, {'trait_type': 'Upper Part', 'value': 'Black Business Suit With Bow Tie & Hat'}, {'trait_type': 'Genesis Role?', 'value': 'Yes'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]     https://arweave.net/o3FE3zv0yfEwwz7fWL1vkjZU5QRv8wrF9s720KgM4EA?ext=webp  https://arweave.net/Ub8rAQDZIibpX-Hyzmo54yAK7_J9Vro_qIbd2jM2Zb0  72b384534f4bb5b5606583b1f9e22a65  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z           6  2021-11-28              2022\n", "7  magic eden v1                  v1 2021-11-28 20:30:48+00:00  109163655  4d3NHZrcS3uhQVQpxutzPnbK6wqeQUGQekWrNEGhuRRHLuq5EAYtuJFF1PK5j7qyeQx4LLCNPCN1zdsNZT8qgbwC       True      0        None  MEisE1HzehtrDpAAT8PnLHjpSSkRYakotTuJRPjTpo8  H51c4azJs1qVY5btFkG475DD9AY2ckbtya57oMQbbr6D  ...  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb                              [{'trait_type': 'Role', 'value': 'Farmer'}, {'trait_type': 'Background', 'value': 'Solana'}, {'trait_type': 'Companions', 'value': 'Weedy with Glasses'}, {'trait_type': 'Facetats', 'value': 'Solana'}, {'trait_type': 'Eyes', 'value': 'Blue'}, {'trait_type': 'Eyes Items', 'value': '420 Googles'}, {'trait_type': 'Form', 'value': 'Light Blonde'}, {'trait_type': 'Hats', 'value': 'Dark Grey Headphones'}, {'trait_type': 'Mouth', 'value': 'Money Joint'}, {'trait_type': 'Hair', 'value': 'Long Wavy Light Blonde'}, {'trait_type': 'Nose', 'value': 'Nose Piercing'}, {'trait_type': 'Hand with Items', 'value': 'Farmer Scissors Light Blonde Fur'}, {'trait_type': 'Upper Part', 'value': 'Red Puff Puff Hoodie'}, {'trait_type': 'Genesis Role?', 'value': 'Yes'}]  https://www.arweave.net/rIi_vqCeZP0AqmAf1GijqMoYxqEZ7jCJgIRhy4nwAhY?ext=png  https://arweave.net/cOc-vmEpAT30GtTIHpWLacZBt4pkKP9P7tkHSt6EkOQ  7e922c912d1e0674834968582bcdd2ec  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z           7  2021-11-28              2022\n", "8  magic eden v1                  v1 2021-11-28 20:31:01+00:00  109163683   BbxpmbPRYHevDdaK8qNVrsi71VrAMoKdihrniTWELaS1ebwqUBcxL3NvofX3WqMuSFUsoyYPypSkSzb5FqRM3MU       True      0        None  MEisE1HzehtrDpAAT8PnLHjpSSkRYakotTuJRPjTpo8  EtbLfuZ9ro4thPCasgJWWpCMaUFbjLG4s1B3BSr6zdfm  ...  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb                                                                                                                                                                                                                                             [{'trait_type': 'Role', 'value': 'Artist'}, {'trait_type': 'Background', 'value': 'Lightbrown'}, {'trait_type': 'Ears', 'value': 'Double Piercing'}, {'trait_type': 'Eyes', 'value': 'Red'}, {'trait_type': 'Form', 'value': 'Dark Blonde'}, {'trait_type': 'Mouth', 'value': 'Rainbow Teeth'}, {'trait_type': 'Hair', 'value': 'Long Wavy Dark Blonde'}, {'trait_type': 'Nose', 'value': 'Nose Piercing'}, {'trait_type': 'Upper Part', 'value': 'Just Hit It Hoodie'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]     https://arweave.net/hMmZqWbfe_XLpmpEfOpuXAA528DNBUdSeBzwx4MryYE?ext=webp  https://arweave.net/7OEMgAK1y4nMyCu8Fzkzxg2A_j8Popf9ED_44saLEm8  e045361bfd713d2819ca98409dd50266  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z           8  2021-11-28              2022\n", "9  magic eden v1                  v1 2021-11-28 20:31:03+00:00  109163685  5QShxqPFPNizRhSNHeiwXqejirG2f8AbPgVsp1xumwPp9azwSXo4zvUnrPCg511HcTVZK68jbnJ5gK4x8ziMmFet       True      0        None  MEisE1HzehtrDpAAT8PnLHjpSSkRYakotTuJRPjTpo8  7wTGmX7g4finjeuMHGB2rDPWLjK9ZuecyCPWeTUB74ZY  ...  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb  [{'trait_type': 'Role', 'value': 'Farmer'}, {'trait_type': 'Background', 'value': 'Blue'}, {'trait_type': 'Eyes', 'value': 'Black'}, {'trait_type': 'Eyes Items', 'value': 'Green John <PERSON>es'}, {'trait_type': 'Form', 'value': '<PERSON><PERSON>'}, {'trait_type': 'Hats', 'value': 'Blue Beret'}, {'trait_type': 'Mouth', 'value': 'Eating Pizza'}, {'trait_type': 'Hair', 'value': 'Long Wavy Chocbrown'}, {'trait_type': 'Nose', 'value': 'Nose Piercing'}, {'trait_type': 'Hand with Items', 'value': 'Pink Donut Chocbrown Fur'}, {'trait_type': 'Random Things', 'value': 'Cannabis Plant'}, {'trait_type': 'Upper Part', 'value': 'Pink Canna Jacket'}, {'trait_type': 'Genesis Role?', 'value': 'Yes'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]     https://arweave.net/T9yc4OjAhASqGZnxQ4gm6rmtqz7fS62YirqM4Y9XI78?ext=webp  https://arweave.net/MJFRKaezWFffQL48eVIIrklUyzwc1cBzvBYFpYlPdds  1ed0292f13705a4da8a6ffeb87af13e2  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z           9  2021-11-28              2022\n", "\n", "[10 rows x 34 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["collections = \"', '\".join(utils.collections)\n", "wallet_list = \"', '\".join(company_wallets[\"sac_pubkey\"].to_list())\n", "        \n", "sac_nft_sales = utils.query_df(f\"\"\"\n", "    SELECT\n", "        *\n", "    FROM\n", "        solana.nft.ez_nft_sales\n", "    WHERE\n", "        collection_id = '{utils.sac_collection}'\n", "    ORDER BY\n", "        block_timestamp\n", "    LIMIT 10\"\"\")\n", "\n", "# floor_sweeps = utils.merge_company_wallets(floor_sweeps, company_wallets, \"buyer_address\")\n", "# floor_sweeps = utils.move_columns_to_front(floor_sweeps, [\"block_timestamp\", \"accounting_period\", \"tx_id\", \"by_sac\", \"buyer_address\", \"price_usd\"])\n", "# floor_sweeps.info()\n", "sac_nft_sales"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["found 35024 rows\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 35024 entries, 0 to 35023\n", "Data columns (total 41 columns):\n", " #   Column                Non-Null Count  Dtype              \n", "---  ------                --------------  -----              \n", " 0   transfer_to           35024 non-null  object             \n", " 1   transfer_from         35024 non-null  object             \n", " 2   transfer_mint         35024 non-null  object             \n", " 3   transfer_amount       35024 non-null  float64            \n", " 4   transfer_index        35024 non-null  object             \n", " 5   block_timestamp_hour  35024 non-null  object             \n", " 6   transfer_usd          35024 non-null  float64            \n", " 7   marketplace           35024 non-null  object             \n", " 8   marketplace_version   35024 non-null  object             \n", " 9   block_timestamp       35024 non-null  datetime64[ns, UTC]\n", " 10  block_id              35024 non-null  int64              \n", " 11  tx_id                 35024 non-null  object             \n", " 12  succeeded             35024 non-null  bool               \n", " 13  index                 35024 non-null  int64              \n", " 14  inner_index           375 non-null    float64            \n", " 15  program_id            35024 non-null  object             \n", " 16  buyer_address         35024 non-null  object             \n", " 17  seller_address        35024 non-null  object             \n", " 18  mint                  35024 non-null  object             \n", " 19  nft_name              35024 non-null  object             \n", " 20  price                 35024 non-null  float64            \n", " 21  currency_address      35024 non-null  object             \n", " 22  currency_symbol       35024 non-null  object             \n", " 23  price_usd             29871 non-null  float64            \n", " 24  tree_authority        0 non-null      object             \n", " 25  merkle_tree           0 non-null      object             \n", " 26  leaf_index            0 non-null      object             \n", " 27  is_compressed         35024 non-null  bool               \n", " 28  nft_collection_name   18264 non-null  object             \n", " 29  collection_id         35024 non-null  object             \n", " 30  creators              35024 non-null  object             \n", " 31  authority             35024 non-null  object             \n", " 32  metadata              35024 non-null  object             \n", " 33  image_url             35024 non-null  object             \n", " 34  metadata_uri          35024 non-null  object             \n", " 35  ez_nft_sales_id       35024 non-null  object             \n", " 36  inserted_timestamp    35018 non-null  object             \n", " 37  modified_timestamp    35018 non-null  object             \n", " 38  __row_index           35024 non-null  int64              \n", " 39  day                   35024 non-null  object             \n", " 40  accounting_period     35024 non-null  int64              \n", "dtypes: bool(2), datetime64[ns, UTC](1), float64(5), int64(4), object(29)\n", "memory usage: 10.5+ MB\n"]}], "source": ["collections = \"', '\".join(utils.collections)\n", "wallet_list = \"', '\".join(company_wallets[\"sac_pubkey\"].to_list())\n", "\n", "royaltyWallets = [\n", "    \"PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb\",\n", "    \"NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC\",\n", "    \"sain87NazoHou2KH9kHjB5NLejYimf174PX9Cjy7PFW\",\n", "    \"sastbeJnZR9aXnEG2P5eidDcVYGB8VNy3EiR3DwWSNg\",\n", "    \"nuinTBZK45YybFaYypuBuJfYr6cq6v9ZtoRn1hmEr7V\",\n", "    \"nustrwYoBqryuctcdV7H4R8suJLER7UFfZHZWvAtrvd\"\n", "]\n", "\n", "        \n", "royalties = utils.query_df(f\"\"\"\n", "    SELECT\n", "        transfers.tx_to as transfer_to,\n", "        transfers.tx_from as transfer_from,\n", "        transfers.mint as transfer_mint,\n", "        transfers.amount as transfer_amount,\n", "        transfers.index as transfer_index,\n", "        date_trunc(\"hour\", sales.block_timestamp) as block_timestamp_hour,\n", "        transfer_amount * prices.price as transfer_usd,\n", "        sales.*,\n", "    FROM\n", "        solana.nft.ez_nft_sales sales\n", "        join solana.core.fact_transfers transfers on transfers.tx_id = sales.tx_id\n", "        join solana.price.ez_prices_hourly prices on block_timestamp_hour = prices.hour\n", "    WHERE\n", "        collection_id in ('{collections}')\n", "        and transfer_to in ('{\"', '\".join(royaltyWallets)}') and transfer_mint = 'So11111111111111111111111111111111111111111'\n", "        and prices.token_address is null\n", "    ORDER BY\n", "        block_timestamp;\"\"\")\n", "\n", "#royalties = utils.merge_company_wallets(royalties, company_wallets, \"buyer_address\")\n", "#royalties = utils.move_columns_to_front(royalties, [\"block_timestamp\", \"accounting_period\", \"tx_id\", \"by_sac\", \"buyer_address\", \"price_usd\"])\n", "royalties.info()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["royalties[\"amount_usd\"] = royalties[\"transfer_usd\"] "]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["royalties.to_csv(\"royalties.csv\", index=False)"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [{"data": {"text/plain": ["transfer_to\n", "PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb   1,511,079.08\n", "NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC     617,472.72\n", "sain87NazoHou2KH9kHjB5NLejYimf174PX9Cjy7PFW      53,054.93\n", "sastbeJnZR9aXnEG2P5eidDcVYGB8VNy3EiR3DwWSNg      22,737.83\n", "nuinTBZK45YybFaYypuBuJfYr6cq6v9ZtoRn1hmEr7V      16,893.46\n", "nustrwYoBqryuctcdV7H4R8suJLER7UFfZHZWvAtrvd       7,240.03\n", "Name: transfer_usd, dtype: float64"]}, "execution_count": 66, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "royalties.groupby(\"transfer_to\")[\"transfer_usd\"].sum().sort_values(ascending=False)"]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found 19366 rows with duplicate tx_ids\n", "                                                                                          tx_id                                  transfer_to  transfer_usd\n", "25016  121F7y7fyd74kzSWzaG81vvuCLUTapJyVa24GPS1m9tdKz36Xasjy7CwJ674W5ZrpBvPZXaCnGVhbbZvTxypL7sp  sastbeJnZR9aXnEG2P5eidDcVYGB8VNy3EiR3DwWSNg          9.21\n", "25017  121F7y7fyd74kzSWzaG81vvuCLUTapJyVa24GPS1m9tdKz36Xasjy7CwJ674W5ZrpBvPZXaCnGVhbbZvTxypL7sp  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb          0.00\n", "25015  121F7y7fyd74kzSWzaG81vvuCLUTapJyVa24GPS1m9tdKz36Xasjy7CwJ674W5ZrpBvPZXaCnGVhbbZvTxypL7sp  sain87NazoHou2KH9kHjB5NLejYimf174PX9Cjy7PFW         21.48\n", "15834  121mngqWMW3NRGS1jSuzvLVsDsV9YNYA2PwpPEMjx7LZfX8cidvKNXfe8TJ7cTVkrJsvXeB9LKZQfciBp2juAWUu  sastbeJnZR9aXnEG2P5eidDcVYGB8VNy3EiR3DwWSNg          6.26\n", "15832  121mngqWMW3NRGS1jSuzvLVsDsV9YNYA2PwpPEMjx7LZfX8cidvKNXfe8TJ7cTVkrJsvXeB9LKZQfciBp2juAWUu  sain87NazoHou2KH9kHjB5NLejYimf174PX9Cjy7PFW         14.61\n", "...                                                                                         ...                                          ...           ...\n", "25728   zx7FKjC74PZGM4Kt35AFMdKa1dmfkZFWCic7TGoj2RC6HiNXtVCE7zPgYCqhwdBK8koi2XLnPHQ4rqNbw9DEwcz  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb          0.00\n", "25730   zx7FKjC74PZGM4Kt35AFMdKa1dmfkZFWCic7TGoj2RC6HiNXtVCE7zPgYCqhwdBK8koi2XLnPHQ4rqNbw9DEwcz  sain87NazoHou2KH9kHjB5NLejYimf174PX9Cjy7PFW         17.78\n", "15817   zyCFmw3K9SiWWKQCb8GuXhhJQQzueRcGfpkSJgmDNrZ6nhczgmEoqUWenTpwpqDojkLREC5PiXLkg9gJx9BFrh9  NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC          0.00\n", "15816   zyCFmw3K9SiWWKQCb8GuXhhJQQzueRcGfpkSJgmDNrZ6nhczgmEoqUWenTpwpqDojkLREC5PiXLkg9gJx9BFrh9  nustrwYoBqryuctcdV7H4R8suJLER7UFfZHZWvAtrvd          1.29\n", "15815   zyCFmw3K9SiWWKQCb8GuXhhJQQzueRcGfpkSJgmDNrZ6nhczgmEoqUWenTpwpqDojkLREC5PiXLkg9gJx9BFrh9  nuinTBZK45YybFaYypuBuJfYr6cq6v9ZtoRn1hmEr7V          3.01\n", "\n", "[19366 rows x 3 columns]\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>tx_id</th>\n", "      <th>transfer_index</th>\n", "      <th>transfer_to</th>\n", "      <th>transfer_usd</th>\n", "      <th>price</th>\n", "      <th>transfer_from</th>\n", "      <th>transfer_mint</th>\n", "      <th>transfer_amount</th>\n", "      <th>block_timestamp_hour</th>\n", "      <th>marketplace</th>\n", "      <th>...</th>\n", "      <th>authority</th>\n", "      <th>metadata</th>\n", "      <th>image_url</th>\n", "      <th>metadata_uri</th>\n", "      <th>ez_nft_sales_id</th>\n", "      <th>inserted_timestamp</th>\n", "      <th>modified_timestamp</th>\n", "      <th>__row_index</th>\n", "      <th>day</th>\n", "      <th>accounting_period</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>9390</th>\n", "      <td>4Aj25CYnm7AukesHtuGJZWTJ6UPx57Y2XKb8tQP8jwfzNoiKx3fk6Q4vgLSYtaCXvk1CUk8BoQpx9aJYgMVoV2fA</td>\n", "      <td>6.0</td>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>1,126.99</td>\n", "      <td>127.00</td>\n", "      <td>HyFaXRPjhKy7eNzw9p6afz3u1o9EXJGskTHUn9AAQ6v1</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>9.42</td>\n", "      <td>2022-04-06T15:00:00.000Z</td>\n", "      <td>opensea</td>\n", "      <td>...</td>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>[{'trait_type': 'Role', 'value': 'Farmer'}, {'trait_type': 'Background', 'value': 'Brown Plain'}, {'trait_type': 'Eyes', 'value': 'Red'}, {'trait_type': 'Form', 'value': 'Rainbow'}, {'trait_type': 'Mouth', 'value': 'Laughing'}, {'trait_type': 'Hair', 'value': 'Long Wavy Rainbow'}, {'trait_type': 'Neck', 'value': 'Weed Chain'}, {'trait_type': 'Nose', 'value': 'Nose Piercing'}, {'trait_type': 'Hand with Items', 'value': 'Pink Donut Rainbow Fur'}, {'trait_type': 'Random Things', 'value': 'Cannabis Plant'}, {'trait_type': 'Upper Part', 'value': 'Violet Canna Jacket'}, {'trait_type': 'Genesis Role?', 'value': 'Yes'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]</td>\n", "      <td>https://arweave.net/jdbAvzJhYwMyn4PmP2kfWb3mGc0regYCL1yvv6Rze5c?ext=webp</td>\n", "      <td>https://arweave.net/KwDX2o6yddqFyieT6pCNp5bpTd837JzWcturx7gfECA</td>\n", "      <td>04a0a0180eaaa1a4317a551b1ad32e6c</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>9390</td>\n", "      <td>2022-04-06</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9391</th>\n", "      <td>4Aj25CYnm7AukesHtuGJZWTJ6UPx57Y2XKb8tQP8jwfzNoiKx3fk6Q4vgLSYtaCXvk1CUk8BoQpx9aJYgMVoV2fA</td>\n", "      <td>3</td>\n", "      <td>NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC</td>\n", "      <td>0.11</td>\n", "      <td>127.00</td>\n", "      <td>pAHAKoTJsAAe2ZcvTZUxoYzuygVAFAmbYmJYdWT886r</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>0.00</td>\n", "      <td>2022-04-06T15:00:00.000Z</td>\n", "      <td>opensea</td>\n", "      <td>...</td>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>[{'trait_type': 'Role', 'value': 'Farmer'}, {'trait_type': 'Background', 'value': 'Brown Plain'}, {'trait_type': 'Eyes', 'value': 'Red'}, {'trait_type': 'Form', 'value': 'Rainbow'}, {'trait_type': 'Mouth', 'value': 'Laughing'}, {'trait_type': 'Hair', 'value': 'Long Wavy Rainbow'}, {'trait_type': 'Neck', 'value': 'Weed Chain'}, {'trait_type': 'Nose', 'value': 'Nose Piercing'}, {'trait_type': 'Hand with Items', 'value': 'Pink Donut Rainbow Fur'}, {'trait_type': 'Random Things', 'value': 'Cannabis Plant'}, {'trait_type': 'Upper Part', 'value': 'Violet Canna Jacket'}, {'trait_type': 'Genesis Role?', 'value': 'Yes'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]</td>\n", "      <td>https://arweave.net/jdbAvzJhYwMyn4PmP2kfWb3mGc0regYCL1yvv6Rze5c?ext=webp</td>\n", "      <td>https://arweave.net/KwDX2o6yddqFyieT6pCNp5bpTd837JzWcturx7gfECA</td>\n", "      <td>04a0a0180eaaa1a4317a551b1ad32e6c</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>9391</td>\n", "      <td>2022-04-06</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9764</th>\n", "      <td>4y4GMTQu7BT4mF6TeGqZvnN3X35SakNMEFGv5cNozfdGzvJTpkYAhusWtvBPTjHk77wKLd75mHhRNNvddzLY9Tgs</td>\n", "      <td>2.0</td>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>756.79</td>\n", "      <td>100.00</td>\n", "      <td>4xxXpsYQJbPQBTSbaZkoHkJswDcurgnr5L6juEnAaNRR</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>7.42</td>\n", "      <td>2022-04-16T20:00:00.000Z</td>\n", "      <td>magic eden v2</td>\n", "      <td>...</td>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>[{'trait_type': 'Role', 'value': 'Chimpion'}, {'trait_type': 'Background', 'value': 'Lightbrown'}, {'trait_type': 'Ears', 'value': 'Double Piercing'}, {'trait_type': 'Eyes', 'value': 'Black'}, {'trait_type': 'Form', 'value': 'Light Blonde'}, {'trait_type': 'Hats', 'value': 'Samo Hairband'}, {'trait_type': 'Mouth', 'value': 'Biting'}, {'trait_type': 'Hair', 'value': 'Facon Style Light Blonde'}, {'trait_type': 'Hand with Items', 'value': 'Joint Light Blonde Fur'}, {'trait_type': 'Upper Part', 'value': '420 Da Vinci Tee'}]</td>\n", "      <td>https://www.arweave.net/oBmFJu0cPlxCnc3E8R7DaWQqK-ZPnKCwIWLdQS63xhM?ext=png</td>\n", "      <td>https://arweave.net/WrzInzifWBe0S0woLvscFcDJax-wdKy0inf7TnaSwu8</td>\n", "      <td>68e60b51bd559e21eeb123083ca04105</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>9764</td>\n", "      <td>2022-04-16</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9765</th>\n", "      <td>4y4GMTQu7BT4mF6TeGqZvnN3X35SakNMEFGv5cNozfdGzvJTpkYAhusWtvBPTjHk77wKLd75mHhRNNvddzLY9Tgs</td>\n", "      <td>2.2</td>\n", "      <td>NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC</td>\n", "      <td>9,238.53</td>\n", "      <td>100.00</td>\n", "      <td>4xxXpsYQJbPQBTSbaZkoHkJswDcurgnr5L6juEnAaNRR</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>90.58</td>\n", "      <td>2022-04-16T20:00:00.000Z</td>\n", "      <td>magic eden v2</td>\n", "      <td>...</td>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>[{'trait_type': 'Role', 'value': 'Chimpion'}, {'trait_type': 'Background', 'value': 'Lightbrown'}, {'trait_type': 'Ears', 'value': 'Double Piercing'}, {'trait_type': 'Eyes', 'value': 'Black'}, {'trait_type': 'Form', 'value': 'Light Blonde'}, {'trait_type': 'Hats', 'value': 'Samo Hairband'}, {'trait_type': 'Mouth', 'value': 'Biting'}, {'trait_type': 'Hair', 'value': 'Facon Style Light Blonde'}, {'trait_type': 'Hand with Items', 'value': 'Joint Light Blonde Fur'}, {'trait_type': 'Upper Part', 'value': '420 Da Vinci Tee'}]</td>\n", "      <td>https://www.arweave.net/oBmFJu0cPlxCnc3E8R7DaWQqK-ZPnKCwIWLdQS63xhM?ext=png</td>\n", "      <td>https://arweave.net/WrzInzifWBe0S0woLvscFcDJax-wdKy0inf7TnaSwu8</td>\n", "      <td>68e60b51bd559e21eeb123083ca04105</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>9765</td>\n", "      <td>2022-04-16</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15564</th>\n", "      <td>4z5BoqAodWsbNvSZsxi8Mxn3NfiQcakBpuLSCdrizhddKuwg7mYhXrBfKfaSnaVcUTWTPxTaSEN7zKqwKjxdWTjt</td>\n", "      <td>3</td>\n", "      <td>sain87NazoHou2KH9kHjB5NLejYimf174PX9Cjy7PFW</td>\n", "      <td>26.67</td>\n", "      <td>23.78</td>\n", "      <td>2QUSBD5LmpQp94GPjynQ5khyKb5jGtLT7hBvVyVgCM86</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>1.22</td>\n", "      <td>2023-03-23T20:00:00.000Z</td>\n", "      <td>magic eden v2</td>\n", "      <td>...</td>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>[{'trait_type': 'Role', 'value': 'Businessman'}, {'trait_type': 'Background', 'value': 'Light Solana Gradient'}, {'trait_type': 'Companions', 'value': 'Serious Weedy'}, {'trait_type': 'Ears', 'value': 'Golden Earring'}, {'trait_type': 'Eyes', 'value': 'Awake Black'}, {'trait_type': 'Form', 'value': 'Light Blonde'}, {'trait_type': 'Mouth', 'value': 'Grinding'}, {'trait_type': 'Hair', 'value': 'Smooth Long Light Blonde'}, {'trait_type': 'Random Things', 'value': 'Pin on Suit'}, {'trait_type': 'Upper Part', 'value': 'Business Suit With Red Tie'}, {'trait_type': 'Genesis Role?', 'value': 'Yes'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]</td>\n", "      <td>https://arweave.net/9a8OXNsKc64PPI8u2dlew9ExIqsRsphcJc0rFt2_bfc?ext=webp</td>\n", "      <td>https://arweave.net/rFbE6JW5xwTDmvaZLtZgz6WASgxNRmgWxHaxeemoJWY</td>\n", "      <td>e681f6253181be6e2e20fe64a339a47d</td>\n", "      <td>2025-01-29T16:29:07.623Z</td>\n", "      <td>2025-01-29T16:29:07.623Z</td>\n", "      <td>15564</td>\n", "      <td>2023-03-23</td>\n", "      <td>2023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34927</th>\n", "      <td>KTeEnUBg288DsYvM7uqvnVfuAbBWMA6QGyFTDQ122eiGcQYzkcfWJTB5gQPPxWFE81FRZjoa9usJqUxjUdr4jrw</td>\n", "      <td>2.18</td>\n", "      <td>sastbeJnZR9aXnEG2P5eidDcVYGB8VNy3EiR3DwWSNg</td>\n", "      <td>1.71</td>\n", "      <td>0.80</td>\n", "      <td>7y178amCHA83Dxzptb7p42QDZ2wSSxr2Gy1kzB92EmfS</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>0.01</td>\n", "      <td>2025-06-12T05:00:00.000Z</td>\n", "      <td>magic eden v2</td>\n", "      <td>...</td>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>[{'trait_type': 'Role', 'value': 'Artist'}, {'trait_type': 'Background', 'value': 'Green Smoke'}, {'trait_type': 'Eyes', 'value': 'Blue'}, {'trait_type': 'Eyes Items', 'value': 'Tear'}, {'trait_type': 'Form', 'value': 'Light Blonde'}, {'trait_type': 'Mouth', 'value': 'Biting'}, {'trait_type': 'Hair', 'value': 'Yellow Beanie Light Blonde Fur'}, {'trait_type': 'Hand with Items', 'value': 'Grey Controller Light Blonde Fur'}, {'trait_type': 'Random Things', 'value': 'Questionmark'}, {'trait_type': 'Upper Part', 'value': 'Striped Shirt With Color Splashes'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]</td>\n", "      <td>https://arweave.net/r4Gu3HCQQ8CirgpKr0_z_Ku4IKOOurn0jartD89preg?ext=webp</td>\n", "      <td>https://arweave.net/sx4ek9DsBBEI_Jqk37SMVvpucRv4X6ORff87SYxzjQk</td>\n", "      <td>cb96ffd8fe591e1615c21e4af61575ca</td>\n", "      <td>2025-06-12T08:16:53.081Z</td>\n", "      <td>2025-06-12T08:16:53.081Z</td>\n", "      <td>34927</td>\n", "      <td>2025-06-12</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34928</th>\n", "      <td>UHWhjvdZxP1DNWrF4niXkCa5QxtNp2hzfNZgYajdZAVPmRnVeDRDZ5aP1EgJd5Hq4KChEnczDe3EjvR94WdGdRJ</td>\n", "      <td>2.18</td>\n", "      <td>sastbeJnZR9aXnEG2P5eidDcVYGB8VNy3EiR3DwWSNg</td>\n", "      <td>1.85</td>\n", "      <td>0.86</td>\n", "      <td>7y178amCHA83Dxzptb7p42QDZ2wSSxr2Gy1kzB92EmfS</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>0.01</td>\n", "      <td>2025-06-12T05:00:00.000Z</td>\n", "      <td>magic eden v2</td>\n", "      <td>...</td>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>[{'trait_type': 'Role', 'value': 'Artist'}, {'trait_type': 'Background', 'value': 'Space 3'}, {'trait_type': 'Eyes', 'value': 'Black'}, {'trait_type': 'Form', 'value': 'Red Gradient'}, {'trait_type': 'Hats', 'value': 'Red Hairband'}, {'trait_type': 'Mouth', 'value': 'Rolled Joint'}, {'trait_type': 'Hair', 'value': 'Facon Style Red'}, {'trait_type': 'Hand with Items', 'value': 'Metal Controller Red Fur'}, {'trait_type': 'Upper Part', 'value': '420 Da Vinci Tee'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]</td>\n", "      <td>https://arweave.net/SHzZIgHoKv8_wrsV75s27S-OWq_gk2kMEJxm_LEKrIk?ext=webp</td>\n", "      <td>https://arweave.net/zWKZ33IX1Z_6DoyC1Z7XiZfSnd1OBF4fXoUhqjYYdTg</td>\n", "      <td>ac0ebc351ad923455f23403075023fc6</td>\n", "      <td>2025-06-12T08:16:53.081Z</td>\n", "      <td>2025-06-12T08:16:53.081Z</td>\n", "      <td>34928</td>\n", "      <td>2025-06-12</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34929</th>\n", "      <td>UHWhjvdZxP1DNWrF4niXkCa5QxtNp2hzfNZgYajdZAVPmRnVeDRDZ5aP1EgJd5Hq4KChEnczDe3EjvR94WdGdRJ</td>\n", "      <td>2.17</td>\n", "      <td>sain87NazoHou2KH9kHjB5NLejYimf174PX9Cjy7PFW</td>\n", "      <td>4.32</td>\n", "      <td>0.86</td>\n", "      <td>7y178amCHA83Dxzptb7p42QDZ2wSSxr2Gy1kzB92EmfS</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>0.03</td>\n", "      <td>2025-06-12T05:00:00.000Z</td>\n", "      <td>magic eden v2</td>\n", "      <td>...</td>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>[{'trait_type': 'Role', 'value': 'Artist'}, {'trait_type': 'Background', 'value': 'Space 3'}, {'trait_type': 'Eyes', 'value': 'Black'}, {'trait_type': 'Form', 'value': 'Red Gradient'}, {'trait_type': 'Hats', 'value': 'Red Hairband'}, {'trait_type': 'Mouth', 'value': 'Rolled Joint'}, {'trait_type': 'Hair', 'value': 'Facon Style Red'}, {'trait_type': 'Hand with Items', 'value': 'Metal Controller Red Fur'}, {'trait_type': 'Upper Part', 'value': '420 Da Vinci Tee'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]</td>\n", "      <td>https://arweave.net/SHzZIgHoKv8_wrsV75s27S-OWq_gk2kMEJxm_LEKrIk?ext=webp</td>\n", "      <td>https://arweave.net/zWKZ33IX1Z_6DoyC1Z7XiZfSnd1OBF4fXoUhqjYYdTg</td>\n", "      <td>ac0ebc351ad923455f23403075023fc6</td>\n", "      <td>2025-06-12T08:16:53.081Z</td>\n", "      <td>2025-06-12T08:16:53.081Z</td>\n", "      <td>34929</td>\n", "      <td>2025-06-12</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34930</th>\n", "      <td>4Fihy2pA1f94bXLQ3QJbRGQBiP2ZDaBnRNWh7WJuNAScATNNWmtCxPX759mTXy33zXyyBBhCtwV83b7dF2fFCoDz</td>\n", "      <td>2.17</td>\n", "      <td>nuinTBZK45YybFaYypuBuJfYr6cq6v9ZtoRn1hmEr7V</td>\n", "      <td>1.39</td>\n", "      <td>0.28</td>\n", "      <td>7y178amCHA83Dxzptb7p42QDZ2wSSxr2Gy1kzB92EmfS</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>0.01</td>\n", "      <td>2025-06-12T05:00:00.000Z</td>\n", "      <td>magic eden v2</td>\n", "      <td>...</td>\n", "      <td>NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC</td>\n", "      <td>[{'trait_type': 'Species', 'value': 'Goringas'}, {'trait_type': 'Body', 'value': 'Green Fire'}, {'trait_type': 'Head', 'value': 'Green Fire'}, {'trait_type': 'Background', 'value': 'Sol Smoke'}, {'trait_type': 'Face', 'value': 'Basic Face'}, {'trait_type': 'Chest', 'value': 'Basic Back'}, {'trait_type': 'Mouth', 'value': 'Rainbow Mouth'}, {'trait_type': 'Eyes', 'value': 'DMT Blue'}, {'trait_type': 'Lungs', 'value': 'Green Diamond'}, {'trait_type': 'Ear Item', 'value': 'Double Piercing'}, {'trait_type': 'Rarity Rank', 'value': 'Common'}, {'trait_type': 'Type', 'value': 'Awakened'}, {'trait_type': 'Animated', 'value': 'Yes'}, {'trait_type': 'Awakened', 'value': 'Yes'}]</td>\n", "      <td>https://arweave.net/NzQv7d1fH9Yn4tIPI0PtuKiP1KiyhGjURsLYdKi67Ew?ext=webp</td>\n", "      <td>https://arweave.net/8I6v2_OOwnJX3itqLUoPJEmwWIHaLqcZk2Ycy9T4wzo</td>\n", "      <td>4287ddac406a41dad882d124947c909c</td>\n", "      <td>2025-06-12T08:16:53.081Z</td>\n", "      <td>2025-06-12T08:16:53.081Z</td>\n", "      <td>34930</td>\n", "      <td>2025-06-12</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34931</th>\n", "      <td>4Fihy2pA1f94bXLQ3QJbRGQBiP2ZDaBnRNWh7WJuNAScATNNWmtCxPX759mTXy33zXyyBBhCtwV83b7dF2fFCoDz</td>\n", "      <td>2.18</td>\n", "      <td>nustrwYoBqryuctcdV7H4R8suJLER7UFfZHZWvAtrvd</td>\n", "      <td>0.60</td>\n", "      <td>0.28</td>\n", "      <td>7y178amCHA83Dxzptb7p42QDZ2wSSxr2Gy1kzB92EmfS</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>0.00</td>\n", "      <td>2025-06-12T05:00:00.000Z</td>\n", "      <td>magic eden v2</td>\n", "      <td>...</td>\n", "      <td>NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC</td>\n", "      <td>[{'trait_type': 'Species', 'value': 'Goringas'}, {'trait_type': 'Body', 'value': 'Green Fire'}, {'trait_type': 'Head', 'value': 'Green Fire'}, {'trait_type': 'Background', 'value': 'Sol Smoke'}, {'trait_type': 'Face', 'value': 'Basic Face'}, {'trait_type': 'Chest', 'value': 'Basic Back'}, {'trait_type': 'Mouth', 'value': 'Rainbow Mouth'}, {'trait_type': 'Eyes', 'value': 'DMT Blue'}, {'trait_type': 'Lungs', 'value': 'Green Diamond'}, {'trait_type': 'Ear Item', 'value': 'Double Piercing'}, {'trait_type': 'Rarity Rank', 'value': 'Common'}, {'trait_type': 'Type', 'value': 'Awakened'}, {'trait_type': 'Animated', 'value': 'Yes'}, {'trait_type': 'Awakened', 'value': 'Yes'}]</td>\n", "      <td>https://arweave.net/NzQv7d1fH9Yn4tIPI0PtuKiP1KiyhGjURsLYdKi67Ew?ext=webp</td>\n", "      <td>https://arweave.net/8I6v2_OOwnJX3itqLUoPJEmwWIHaLqcZk2Ycy9T4wzo</td>\n", "      <td>4287ddac406a41dad882d124947c909c</td>\n", "      <td>2025-06-12T08:16:53.081Z</td>\n", "      <td>2025-06-12T08:16:53.081Z</td>\n", "      <td>34931</td>\n", "      <td>2025-06-12</td>\n", "      <td>2025</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>19366 rows × 41 columns</p>\n", "</div>"], "text/plain": ["                                                                                          tx_id transfer_index                                  transfer_to  transfer_usd  price                                 transfer_from                                transfer_mint  transfer_amount      block_timestamp_hour    marketplace  ...                                    authority                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             metadata                                                                    image_url                                                     metadata_uri                   ez_nft_sales_id        inserted_timestamp        modified_timestamp __row_index         day accounting_period\n", "9390   4Aj25CYnm7AukesHtuGJZWTJ6UPx57Y2XKb8tQP8jwfzNoiKx3fk6Q4vgLSYtaCXvk1CUk8BoQpx9aJYgMVoV2fA            6.0  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb      1,126.99 127.00  HyFaXRPjhKy7eNzw9p6afz3u1o9EXJGskTHUn9AAQ6v1  So11111111111111111111111111111111111111111             9.42  2022-04-06T15:00:00.000Z        opensea  ...  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb  [{'trait_type': 'Role', 'value': 'Farmer'}, {'trait_type': 'Background', 'value': 'Brown Plain'}, {'trait_type': 'Eyes', 'value': 'Red'}, {'trait_type': 'Form', 'value': 'Rainbow'}, {'trait_type': 'Mouth', 'value': 'Laughing'}, {'trait_type': 'Hair', 'value': 'Long Wavy Rainbow'}, {'trait_type': 'Neck', 'value': 'Weed Chain'}, {'trait_type': 'Nose', 'value': 'Nose Piercing'}, {'trait_type': 'Hand with Items', 'value': 'Pink Donut Rainbow Fur'}, {'trait_type': 'Random Things', 'value': 'Cannabis Plant'}, {'trait_type': 'Upper Part', 'value': 'Violet Canna Jacket'}, {'trait_type': 'Genesis Role?', 'value': 'Yes'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]     https://arweave.net/jdbAvzJhYwMyn4PmP2kfWb3mGc0regYCL1yvv6Rze5c?ext=webp  https://arweave.net/KwDX2o6yddqFyieT6pCNp5bpTd837JzWcturx7gfECA  04a0a0180eaaa1a4317a551b1ad32e6c  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z        9390  2022-04-06              2022\n", "9391   4Aj25CYnm7AukesHtuGJZWTJ6UPx57Y2XKb8tQP8jwfzNoiKx3fk6Q4vgLSYtaCXvk1CUk8BoQpx9aJYgMVoV2fA              3  NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC          0.11 127.00   pAHAKoTJsAAe2ZcvTZUxoYzuygVAFAmbYmJYdWT886r  So11111111111111111111111111111111111111111             0.00  2022-04-06T15:00:00.000Z        opensea  ...  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb  [{'trait_type': 'Role', 'value': 'Farmer'}, {'trait_type': 'Background', 'value': 'Brown Plain'}, {'trait_type': 'Eyes', 'value': 'Red'}, {'trait_type': 'Form', 'value': 'Rainbow'}, {'trait_type': 'Mouth', 'value': 'Laughing'}, {'trait_type': 'Hair', 'value': 'Long Wavy Rainbow'}, {'trait_type': 'Neck', 'value': 'Weed Chain'}, {'trait_type': 'Nose', 'value': 'Nose Piercing'}, {'trait_type': 'Hand with Items', 'value': 'Pink Donut Rainbow Fur'}, {'trait_type': 'Random Things', 'value': 'Cannabis Plant'}, {'trait_type': 'Upper Part', 'value': 'Violet Canna Jacket'}, {'trait_type': 'Genesis Role?', 'value': 'Yes'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]     https://arweave.net/jdbAvzJhYwMyn4PmP2kfWb3mGc0regYCL1yvv6Rze5c?ext=webp  https://arweave.net/KwDX2o6yddqFyieT6pCNp5bpTd837JzWcturx7gfECA  04a0a0180eaaa1a4317a551b1ad32e6c  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z        9391  2022-04-06              2022\n", "9764   4y4GMTQu7BT4mF6TeGqZvnN3X35SakNMEFGv5cNozfdGzvJTpkYAhusWtvBPTjHk77wKLd75mHhRNNvddzLY9Tgs            2.0  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb        756.79 100.00  4xxXpsYQJbPQBTSbaZkoHkJswDcurgnr5L6juEnAaNRR  So11111111111111111111111111111111111111111             7.42  2022-04-16T20:00:00.000Z  magic eden v2  ...  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb                                                                                                                                                                                        [{'trait_type': 'Role', 'value': 'Chimpion'}, {'trait_type': 'Background', 'value': 'Lightbrown'}, {'trait_type': 'Ears', 'value': 'Double Piercing'}, {'trait_type': 'Eyes', 'value': 'Black'}, {'trait_type': 'Form', 'value': 'Light Blonde'}, {'trait_type': 'Hats', 'value': 'Samo Hairband'}, {'trait_type': 'Mouth', 'value': 'Biting'}, {'trait_type': 'Hair', 'value': 'Facon Style Light Blonde'}, {'trait_type': 'Hand with Items', 'value': 'Joint Light Blonde Fur'}, {'trait_type': 'Upper Part', 'value': '420 Da Vinci Tee'}]  https://www.arweave.net/oBmFJu0cPlxCnc3E8R7DaWQqK-ZPnKCwIWLdQS63xhM?ext=png  https://arweave.net/WrzInzifWBe0S0woLvscFcDJax-wdKy0inf7TnaSwu8  68e60b51bd559e21eeb123083ca04105  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z        9764  2022-04-16              2022\n", "9765   4y4GMTQu7BT4mF6TeGqZvnN3X35SakNMEFGv5cNozfdGzvJTpkYAhusWtvBPTjHk77wKLd75mHhRNNvddzLY9Tgs            2.2  NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC      9,238.53 100.00  4xxXpsYQJbPQBTSbaZkoHkJswDcurgnr5L6juEnAaNRR  So11111111111111111111111111111111111111111            90.58  2022-04-16T20:00:00.000Z  magic eden v2  ...  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb                                                                                                                                                                                        [{'trait_type': 'Role', 'value': 'Chimpion'}, {'trait_type': 'Background', 'value': 'Lightbrown'}, {'trait_type': 'Ears', 'value': 'Double Piercing'}, {'trait_type': 'Eyes', 'value': 'Black'}, {'trait_type': 'Form', 'value': 'Light Blonde'}, {'trait_type': 'Hats', 'value': 'Samo Hairband'}, {'trait_type': 'Mouth', 'value': 'Biting'}, {'trait_type': 'Hair', 'value': 'Facon Style Light Blonde'}, {'trait_type': 'Hand with Items', 'value': 'Joint Light Blonde Fur'}, {'trait_type': 'Upper Part', 'value': '420 Da Vinci Tee'}]  https://www.arweave.net/oBmFJu0cPlxCnc3E8R7DaWQqK-ZPnKCwIWLdQS63xhM?ext=png  https://arweave.net/WrzInzifWBe0S0woLvscFcDJax-wdKy0inf7TnaSwu8  68e60b51bd559e21eeb123083ca04105  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z        9765  2022-04-16              2022\n", "15564  4z5BoqAodWsbNvSZsxi8Mxn3NfiQcakBpuLSCdrizhddKuwg7mYhXrBfKfaSnaVcUTWTPxTaSEN7zKqwKjxdWTjt              3  sain87NazoHou2KH9kHjB5NLejYimf174PX9Cjy7PFW         26.67  23.78  2QUSBD5LmpQp94GPjynQ5khyKb5jGtLT7hBvVyVgCM86  So11111111111111111111111111111111111111111             1.22  2023-03-23T20:00:00.000Z  magic eden v2  ...  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb                       [{'trait_type': 'Role', 'value': 'Businessman'}, {'trait_type': 'Background', 'value': 'Light Solana Gradient'}, {'trait_type': 'Companions', 'value': 'Serious Weedy'}, {'trait_type': 'Ears', 'value': 'Golden Earring'}, {'trait_type': 'Eyes', 'value': 'Awake Black'}, {'trait_type': 'Form', 'value': 'Light Blonde'}, {'trait_type': 'Mouth', 'value': 'Grinding'}, {'trait_type': 'Hair', 'value': 'Smooth Long Light Blonde'}, {'trait_type': 'Random Things', 'value': 'Pin on Suit'}, {'trait_type': 'Upper Part', 'value': 'Business Suit With Red Tie'}, {'trait_type': 'Genesis Role?', 'value': 'Yes'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]     https://arweave.net/9a8OXNsKc64PPI8u2dlew9ExIqsRsphcJc0rFt2_bfc?ext=webp  https://arweave.net/rFbE6JW5xwTDmvaZLtZgz6WASgxNRmgWxHaxeemoJWY  e681f6253181be6e2e20fe64a339a47d  2025-01-29T16:29:07.623Z  2025-01-29T16:29:07.623Z       15564  2023-03-23              2023\n", "...                                                                                         ...            ...                                          ...           ...    ...                                           ...                                          ...              ...                       ...            ...  ...                                          ...                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  ...                                                                          ...                                                              ...                               ...                       ...                       ...         ...         ...               ...\n", "34927   KTeEnUBg288DsYvM7uqvnVfuAbBWMA6QGyFTDQ122eiGcQYzkcfWJTB5gQPPxWFE81FRZjoa9usJqUxjUdr4jrw           2.18  sastbeJnZR9aXnEG2P5eidDcVYGB8VNy3EiR3DwWSNg          1.71   0.80  7y178amCHA83Dxzptb7p42QDZ2wSSxr2Gy1kzB92EmfS  So11111111111111111111111111111111111111111             0.01  2025-06-12T05:00:00.000Z  magic eden v2  ...  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb                                                              [{'trait_type': 'Role', 'value': 'Artist'}, {'trait_type': 'Background', 'value': 'Green Smoke'}, {'trait_type': 'Eyes', 'value': 'Blue'}, {'trait_type': 'Eyes Items', 'value': 'Tear'}, {'trait_type': 'Form', 'value': 'Light Blonde'}, {'trait_type': 'Mouth', 'value': 'Biting'}, {'trait_type': 'Hair', 'value': 'Yellow Beanie Light Blonde Fur'}, {'trait_type': 'Hand with Items', 'value': 'Grey Controller Light Blonde Fur'}, {'trait_type': 'Random Things', 'value': 'Questionmark'}, {'trait_type': 'Upper Part', 'value': 'Striped Shirt With Color Splashes'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]     https://arweave.net/r4Gu3HCQQ8CirgpKr0_z_Ku4IKOOurn0jartD89preg?ext=webp  https://arweave.net/sx4ek9DsBBEI_Jqk37SMVvpucRv4X6ORff87SYxzjQk  cb96ffd8fe591e1615c21e4af61575ca  2025-06-12T08:16:53.081Z  2025-06-12T08:16:53.081Z       34927  2025-06-12              2025\n", "34928   UHWhjvdZxP1DNWrF4niXkCa5QxtNp2hzfNZgYajdZAVPmRnVeDRDZ5aP1EgJd5Hq4KChEnczDe3EjvR94WdGdRJ           2.18  sastbeJnZR9aXnEG2P5eidDcVYGB8VNy3EiR3DwWSNg          1.85   0.86  7y178amCHA83Dxzptb7p42QDZ2wSSxr2Gy1kzB92EmfS  So11111111111111111111111111111111111111111             0.01  2025-06-12T05:00:00.000Z  magic eden v2  ...  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb                                                                                                                                                           [{'trait_type': 'Role', 'value': 'Artist'}, {'trait_type': 'Background', 'value': 'Space 3'}, {'trait_type': 'Eyes', 'value': 'Black'}, {'trait_type': 'Form', 'value': 'Red Gradient'}, {'trait_type': 'Hats', 'value': 'Red Hairband'}, {'trait_type': 'Mouth', 'value': 'Rolled Joint'}, {'trait_type': 'Hair', 'value': 'Facon Style Red'}, {'trait_type': 'Hand with Items', 'value': 'Metal Controller Red Fur'}, {'trait_type': 'Upper Part', 'value': '420 Da Vinci Tee'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]     https://arweave.net/SHzZIgHoKv8_wrsV75s27S-OWq_gk2kMEJxm_LEKrIk?ext=webp  https://arweave.net/zWKZ33IX1Z_6DoyC1Z7XiZfSnd1OBF4fXoUhqjYYdTg  ac0ebc351ad923455f23403075023fc6  2025-06-12T08:16:53.081Z  2025-06-12T08:16:53.081Z       34928  2025-06-12              2025\n", "34929   UHWhjvdZxP1DNWrF4niXkCa5QxtNp2hzfNZgYajdZAVPmRnVeDRDZ5aP1EgJd5Hq4KChEnczDe3EjvR94WdGdRJ           2.17  sain87NazoHou2KH9kHjB5NLejYimf174PX9Cjy7PFW          4.32   0.86  7y178amCHA83Dxzptb7p42QDZ2wSSxr2Gy1kzB92EmfS  So11111111111111111111111111111111111111111             0.03  2025-06-12T05:00:00.000Z  magic eden v2  ...  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb                                                                                                                                                           [{'trait_type': 'Role', 'value': 'Artist'}, {'trait_type': 'Background', 'value': 'Space 3'}, {'trait_type': 'Eyes', 'value': 'Black'}, {'trait_type': 'Form', 'value': 'Red Gradient'}, {'trait_type': 'Hats', 'value': 'Red Hairband'}, {'trait_type': 'Mouth', 'value': 'Rolled Joint'}, {'trait_type': 'Hair', 'value': 'Facon Style Red'}, {'trait_type': 'Hand with Items', 'value': 'Metal Controller Red Fur'}, {'trait_type': 'Upper Part', 'value': '420 Da Vinci Tee'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]     https://arweave.net/SHzZIgHoKv8_wrsV75s27S-OWq_gk2kMEJxm_LEKrIk?ext=webp  https://arweave.net/zWKZ33IX1Z_6DoyC1Z7XiZfSnd1OBF4fXoUhqjYYdTg  ac0ebc351ad923455f23403075023fc6  2025-06-12T08:16:53.081Z  2025-06-12T08:16:53.081Z       34929  2025-06-12              2025\n", "34930  4Fihy2pA1f94bXLQ3QJbRGQBiP2ZDaBnRNWh7WJuNAScATNNWmtCxPX759mTXy33zXyyBBhCtwV83b7dF2fFCoDz           2.17  nuinTBZK45YybFaYypuBuJfYr6cq6v9ZtoRn1hmEr7V          1.39   0.28  7y178amCHA83Dxzptb7p42QDZ2wSSxr2Gy1kzB92EmfS  So11111111111111111111111111111111111111111             0.01  2025-06-12T05:00:00.000Z  magic eden v2  ...  NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC                                  [{'trait_type': 'Species', 'value': 'Goringas'}, {'trait_type': 'Body', 'value': 'Green Fire'}, {'trait_type': 'Head', 'value': 'Green Fire'}, {'trait_type': 'Background', 'value': 'Sol Smoke'}, {'trait_type': 'Face', 'value': 'Basic Face'}, {'trait_type': 'Chest', 'value': 'Basic Back'}, {'trait_type': 'Mouth', 'value': 'Rainbow Mouth'}, {'trait_type': 'Eyes', 'value': 'DMT Blue'}, {'trait_type': 'Lungs', 'value': 'Green Diamond'}, {'trait_type': 'Ear Item', 'value': 'Double Piercing'}, {'trait_type': 'Rarity Rank', 'value': 'Common'}, {'trait_type': 'Type', 'value': 'Awakened'}, {'trait_type': 'Animated', 'value': 'Yes'}, {'trait_type': 'Awakened', 'value': 'Yes'}]     https://arweave.net/NzQv7d1fH9Yn4tIPI0PtuKiP1KiyhGjURsLYdKi67Ew?ext=webp  https://arweave.net/8I6v2_OOwnJX3itqLUoPJEmwWIHaLqcZk2Ycy9T4wzo  4287ddac406a41dad882d124947c909c  2025-06-12T08:16:53.081Z  2025-06-12T08:16:53.081Z       34930  2025-06-12              2025\n", "34931  4Fihy2pA1f94bXLQ3QJbRGQBiP2ZDaBnRNWh7WJuNAScATNNWmtCxPX759mTXy33zXyyBBhCtwV83b7dF2fFCoDz           2.18  nustrwYoBqryuctcdV7H4R8suJLER7UFfZHZWvAtrvd          0.60   0.28  7y178amCHA83Dxzptb7p42QDZ2wSSxr2Gy1kzB92EmfS  So11111111111111111111111111111111111111111             0.00  2025-06-12T05:00:00.000Z  magic eden v2  ...  NUKE6VXDcfyb51yvFwU67hDxj2qMgRdkdtUPKy6D3hC                                  [{'trait_type': 'Species', 'value': 'Goringas'}, {'trait_type': 'Body', 'value': 'Green Fire'}, {'trait_type': 'Head', 'value': 'Green Fire'}, {'trait_type': 'Background', 'value': 'Sol Smoke'}, {'trait_type': 'Face', 'value': 'Basic Face'}, {'trait_type': 'Chest', 'value': 'Basic Back'}, {'trait_type': 'Mouth', 'value': 'Rainbow Mouth'}, {'trait_type': 'Eyes', 'value': 'DMT Blue'}, {'trait_type': 'Lungs', 'value': 'Green Diamond'}, {'trait_type': 'Ear Item', 'value': 'Double Piercing'}, {'trait_type': 'Rarity Rank', 'value': 'Common'}, {'trait_type': 'Type', 'value': 'Awakened'}, {'trait_type': 'Animated', 'value': 'Yes'}, {'trait_type': 'Awakened', 'value': 'Yes'}]     https://arweave.net/NzQv7d1fH9Yn4tIPI0PtuKiP1KiyhGjURsLYdKi67Ew?ext=webp  https://arweave.net/8I6v2_OOwnJX3itqLUoPJEmwWIHaLqcZk2Ycy9T4wzo  4287ddac406a41dad882d124947c909c  2025-06-12T08:16:53.081Z  2025-06-12T08:16:53.081Z       34931  2025-06-12              2025\n", "\n", "[19366 rows x 41 columns]"]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["# Check for duplicate tx_ids in royalties\n", "duplicate_tx_ids = royalties[royalties.duplicated(subset=['tx_id'], keep=False)]\n", "if len(duplicate_tx_ids) > 0:\n", "    print(f\"Found {len(duplicate_tx_ids)} rows with duplicate tx_ids\")\n", "    print(duplicate_tx_ids[['tx_id', 'transfer_to', 'transfer_usd']].sort_values('tx_id'))\n", "else:\n", "    print(\"No duplicate tx_ids found in royalties\")\n", "duplicate_tx_ids = utils.move_columns_to_front(duplicate_tx_ids, [\"tx_id\", \"transfer_index\", \"transfer_to\", \"transfer_usd\", \"price\"])\n", "duplicate_tx_ids"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"data": {"text/plain": ["accounting_period\n", "2022   2,101,562.94\n", "2023      59,368.45\n", "2024      55,482.46\n", "2025      12,064.20\n", "Name: transfer_usd, dtype: float64"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["royalties.groupby(\"accounting_period\")[\"transfer_usd\"].sum()"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"text/plain": ["304101.*********"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["floor_sweeps.to_csv(\"sac_floor_sweeps.csv\")\n", "floor_sweeps[\"price_usd\"].sum()"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["found 1 rows\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>min(block_timestamp)</th>\n", "      <th>__row_index</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2021-02-14T03:54:02.000Z</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       min(block_timestamp)  __row_index\n", "0  2021-02-14T03:54:02.000Z            0"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# see first\n", "\n", "query_df(\"select min(block_timestamp) from solana.defi.ez_dex_swaps;\")"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["found 3247 rows\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3247 entries, 0 to 3246\n", "Data columns (total 21 columns):\n", " #   Column                Non-Null Count  Dtype              \n", "---  ------                --------------  -----              \n", " 0   swap_program          3247 non-null   object             \n", " 1   block_id              3247 non-null   int64              \n", " 2   block_timestamp       3247 non-null   datetime64[ns, UTC]\n", " 3   tx_id                 3247 non-null   object             \n", " 4   program_id            3247 non-null   object             \n", " 5   swapper               3247 non-null   object             \n", " 6   swap_from_mint        3247 non-null   object             \n", " 7   swap_from_symbol      3247 non-null   object             \n", " 8   swap_from_amount      3247 non-null   float64            \n", " 9   swap_from_amount_usd  3247 non-null   float64            \n", " 10  swap_to_mint          3247 non-null   object             \n", " 11  swap_to_symbol        2887 non-null   object             \n", " 12  swap_to_amount        3247 non-null   float64            \n", " 13  swap_to_amount_usd    2887 non-null   float64            \n", " 14  _log_id               3247 non-null   object             \n", " 15  inserted_timestamp    3247 non-null   object             \n", " 16  modified_timestamp    3247 non-null   object             \n", " 17  ez_swaps_id           3247 non-null   object             \n", " 18  __row_index           3247 non-null   int64              \n", " 19  day                   3247 non-null   object             \n", " 20  accounting_period     3247 non-null   int64              \n", "dtypes: datetime64[ns, UTC](1), float64(4), int64(3), object(13)\n", "memory usage: 532.8+ KB\n"]}], "source": ["all_big_buys = utils.query_df(f\"\"\"\n", "    SELECT\n", "        *\n", "    FROM\n", "        solana.defi.ez_dex_swaps\n", "    WHERE\n", "        swap_to_mint in ('{mint_list}')\n", "        and swap_from_amount_usd > 1000\n", "    ORDER BY\n", "        block_timestamp;\"\"\")\n", "all_big_buys.info()"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["all_big_buys  = utils.merge_company_wallets(all_big_buys, company_wallets, \"swapper\")\n", "all_big_buys = utils.move_columns_to_front(all_big_buys, [\"block_timestamp\", \"accounting_period\", \"tx_id\", \"by_sac\", \"swapper\", \"swap_from_symbol\", \"swap_to_symbol\", \"swap_from_amount_usd\", \"swap_to_amount\", \"swap_from_amount_usd\", \"swap_to_amount_usd\"])\n", "all_big_buys.to_csv(\"sac_big_buys.csv\")"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['block_timestamp', 'accounting_period', 'tx_id', 'by_sac', 'swapper',\n", "       'swap_from_symbol', 'swap_to_symbol', 'swap_from_amount_usd',\n", "       'swap_to_amount', 'swap_from_amount_usd', 'swap_to_amount_usd',\n", "       'swap_program', 'block_id', 'program_id', 'swap_from_mint',\n", "       'swap_from_amount', 'swap_to_mint', '_log_id', 'inserted_timestamp',\n", "       'modified_timestamp', 'ez_swaps_id', '__row_index', 'day', 'sac_pubkey',\n", "       'sac_name', 'sac_category', 'sac_tag', 'sac_Spalte 2', 'sac_Spalte 3',\n", "       'sac_Spalte 4'],\n", "      dtype='object')"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["all_big_buys.columns"]}], "metadata": {"deepnote_notebook_id": "e0ea8a814de143c7bafe54a8a3ffb5b9", "deepnote_persisted_session": {"createdAt": "2025-05-22T09:12:20.551Z"}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 0}