{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {"cell_id": "e1bd6f1893e44e798d8a9a818a2f4b54", "deepnote_cell_type": "code", "execution_context_id": "5ba8fb5a-825a-485f-81db-f13eaf5a5225", "execution_millis": 7447, "execution_start": *************, "source_hash": "70f387e9"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["flipside api key: 17a07c09-6182-4ee2-97b8-ce30e51ba62b\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Documents/stoned-apes/finance/crypto-accounting/utils.py:78: FutureWarning: The frame.append method is deprecated and will be removed from pandas in a future version. Use pandas.concat instead.\n", "  company_wallets = company_wallets.append({\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sac_pubkey</th>\n", "      <th>sac_name</th>\n", "      <th>sac_category</th>\n", "      <th>sac_tag</th>\n", "      <th>sac_Spalte 2</th>\n", "      <th>sac_Spalte 3</th>\n", "      <th>sac_Spalte 4</th>\n", "      <th>sac_email</th>\n", "      <th>sac_phone</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>8vQeeeyg8VjH7ZFCTa2Fk6JcjEnPCwoBjXT3NsbfqUSD</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>**********************************</td>\n", "      <td>BTC Wirex</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>4ifAYTxnxwDr6cyDDcZdWewRQJqmpszo43iiK6stzvG9</td>\n", "      <td>C Binance</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>CiFNkttVWHUj3LVtjbh8mADcaysXmSpnqdN7NB8RNmcj</td>\n", "      <td>NaN</td>\n", "      <td>lucky dip</td>\n", "      <td>NaN</td>\n", "      <td>authority of the lucky dip #2 raffle</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>CcmkU1vLCyjXnH46BfVA1623XkEnau3dSUfFm6rc5Upg</td>\n", "      <td>Luck Dip #1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>77</th>\n", "      <td>********************************************</td>\n", "      <td><PERSON></td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>78</th>\n", "      <td>F8F7q5WCtaRiYoH7aQGutRhDsXFzJdiZLGgA8A1PDDHX</td>\n", "      <td>Investment-Wallet</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>79</th>\n", "      <td>treZeLEpyJaJ9xDuHC4sytyPkMWmQMC1dn8iNNUvZG1</td>\n", "      <td>Treasury 1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>80</th>\n", "      <td>strfkDDF2oMXoiYWhY4aa2LKuEHNuLTrimJh6ngrQBN</td>\n", "      <td>Treasury 2</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81</th>\n", "      <td>ABGLAB5UQ4oeyVH61RkFkxP9R5LY3iDQFBrDSiP86Tzc</td>\n", "      <td>Stoned Apes</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td><EMAIL></td>\n", "      <td>0000000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>82 rows × 9 columns</p>\n", "</div>"], "text/plain": ["                                      sac_pubkey           sac_name sac_category  sac_tag                          sac_Spalte 2  sac_Spalte 3  sac_Spalte 4                sac_email   sac_phone\n", "0   8vQeeeyg8VjH7ZFCTa2Fk6JcjEnPCwoBjXT3NsbfqUSD                NaN          NaN      NaN                                   NaN           NaN           NaN                      NaN         NaN\n", "1             **********************************          BTC Wirex          NaN      NaN                                   NaN           NaN           NaN                      NaN         NaN\n", "2   4ifAYTxnxwDr6cyDDcZdWewRQJqmpszo43iiK6stzvG9          C Binance          NaN      NaN                                   NaN           NaN           NaN                      NaN         NaN\n", "3   CiFNkttVWHUj3LVtjbh8mADcaysXmSpnqdN7NB8RNmcj                NaN    lucky dip      NaN  authority of the lucky dip #2 raffle           NaN           NaN                      NaN         NaN\n", "4   CcmkU1vLCyjXnH46BfVA1623XkEnau3dSUfFm6rc5Upg        Luck Dip #1          NaN      NaN                                   NaN           NaN           NaN                      NaN         NaN\n", "..                                           ...                ...          ...      ...                                   ...           ...           ...                      ...         ...\n", "77  ********************************************   Matthias Binance          NaN      NaN                                   NaN           NaN           NaN                      NaN         NaN\n", "78  F8F7q5WCtaRiYoH7aQGutRhDsXFzJdiZLGgA8A1PDDHX  Investment-Wallet          NaN      NaN                                   NaN           NaN           NaN                      NaN         NaN\n", "79   treZeLEpyJaJ9xDuHC4sytyPkMWmQMC1dn8iNNUvZG1         Treasury 1          NaN      NaN                                   NaN           NaN           NaN                      NaN         NaN\n", "80   strfkDDF2oMXoiYWhY4aa2LKuEHNuLTrimJh6ngrQBN         Treasury 2          NaN      NaN                                   NaN           NaN           NaN                      NaN         NaN\n", "81  ABGLAB5UQ4oeyVH61RkFkxP9R5LY3iDQFBrDSiP86Tzc        Stoned Apes          NaN      NaN                                   NaN           NaN           NaN  <EMAIL>  0000000000\n", "\n", "[82 rows x 9 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "from dotenv import load_dotenv\n", "import pandas as pd\n", "import importlib\n", "import utils\n", "importlib.reload(utils)\n", "\n", "pd.set_option('display.float_format', '{:,.2f}'.format)\n", "\n", "company_wallets = utils.load_company_wallets()\n", "company_wallets"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["found 181 rows\n", "Buybacks sum: 637424.**********\n"]}, {"data": {"text/plain": ["Index(['block_timestamp', 'accounting_period', 'tx_id', 'by_sac', 'swapper',\n", "       'swap_from_symbol', 'swap_to_symbol', 'swap_from_amount_usd',\n", "       'swap_to_amount', 'swap_to_amount_usd', 'amount_usd', 'swap_program',\n", "       'block_id', 'program_id', 'swap_from_mint', 'swap_from_amount',\n", "       'swap_to_mint', '_log_id', 'inserted_timestamp', 'modified_timestamp',\n", "       'ez_swaps_id', '__row_index', 'day', 'sac_pubkey', 'sac_name',\n", "       'sac_category', 'sac_tag', 'sac_Spalte 2', 'sac_Spalte 3',\n", "       'sac_Spalte 4', 'sac_email', 'sac_phone'],\n", "      dtype='object')"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["mint_list = \"', '\".join(utils.mints)\n", "wallet_list = \"', '\".join(company_wallets[\"sac_pubkey\"].to_list())\n", "        \n", "buybacks = utils.query_df(f\"\"\"\n", "    SELECT\n", "    swap_from_amount_usd as amount_usd,\n", "        *\n", "    FROM\n", "        solana.defi.ez_dex_swaps\n", "    WHERE\n", "      swapper = 'EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB'\n", "    ORDER BY\n", "        block_timestamp;\"\"\")\n", "\n", "buybacks = utils.merge_company_wallets(buybacks, company_wallets, \"swapper\")\n", "buybacks = utils.move_columns_to_front(buybacks, [\"block_timestamp\", \"accounting_period\", \"tx_id\", \"by_sac\", \"swapper\", \"swap_from_symbol\", \"swap_to_symbol\", \"swap_from_amount_usd\", \"swap_to_amount\", \"swap_from_amount_usd\", \"swap_to_amount_usd\"])\n", "print(f\"Buybacks sum: {buybacks['amount_usd'].sum()}\")\n", "buybacks.columns"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["found 184 rows\n"]}, {"data": {"text/plain": ["Index(['block_timestamp', 'block_id', 'tx_id', 'succeeded', 'swapper',\n", "       'swap_from_amount', 'swap_from_mint', 'swap_to_amount', 'swap_to_mint',\n", "       'program_id', 'swap_program', '_log_id', 'fact_swaps_id',\n", "       'inserted_timestamp', 'modified_timestamp', '__row_index', 'day',\n", "       'accounting_period'],\n", "      dtype='object')"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["mint_list = \"', '\".join(utils.mints)\n", "wallet_list = \"', '\".join(company_wallets[\"sac_pubkey\"].to_list())\n", "        \n", "swaps = utils.query_df(f\"\"\"\n", "    SELECT\n", "    -- swap_from_amount_usd as amount_usd,\n", "        *,\n", "        \n", "    FROM\n", "        solana.defi.fact_swaps\n", "    WHERE\n", "      swapper = 'EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB'\n", "    ORDER BY\n", "        block_timestamp;\"\"\")\n", "\n", "swaps = utils.merge_company_wallets(swaps, company_wallets, \"swapper\")\n", "#swaps = utils.move_columns_to_front(swaps, [\"block_timestamp\", \"accounting_period\", \"tx_id\", \"by_sac\", \"swapper\", \"swap_from_symbol\", \"swap_to_symbol\", \"swap_from_amount_usd\", \"swap_to_amount\", \"swap_from_amount_usd\", \"swap_to_amount_usd\"])\n", "swaps.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["found 776 rows\n"]}], "source": ["buybacks = utils.query_df(f\"\"\"\n", "    SELECT\n", "    swap_from_amount_usd as amount_usd,\n", "        swaps.*,\n", "        transfers.amount,\n", "        transfers.mint,\n", "        transfers.index as swap_tx_index,\n", "        swaps.\n", "        transfers.tx_from,\n", "        transfers.tx_to,\n", "    FROM\n", "        solana.defi.ez_dex_swaps swaps\n", "        join solana.core.fact_transfers transfers on transfers.tx_id = swaps.tx_id\n", "    WHERE\n", "      swapper = 'EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB'\n", "    ORDER BY\n", "        block_timestamp;\"\"\")"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["found 5767 rows\n"]}, {"data": {"text/plain": ["Index(['block_timestamp', 'block_id', 'tx_id', 'wallet', 'amount', 'mint',\n", "       'type', '__row_index', 'day', 'accounting_period'],\n", "      dtype='object')"]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["wallet = utils.eu_wallet\n", "all_transactions = utils.query_df(f\"\"\"WITH wallet_swaps AS (\n", "    SELECT *\n", "    FROM   solana.defi.fact_swaps\n", "    WHERE  swapper = '{wallet}'\n", "      AND  succeeded\n", "),\n", "\n", "-- 2️⃣  Build the two \"legs\" (transfer patterns) for every swap\n", "swap_transfer_legs AS (\n", "    SELECT\n", "        tx_id,\n", "        /* leg that LEAVES the wallet */\n", "        swapper                 AS wallet_addr,\n", "        swap_from_mint          AS mint,\n", "        swap_from_amount        AS amount,\n", "        'out'                   AS direction\n", "    FROM wallet_swaps\n", "\n", "    UNION ALL\n", "\n", "    SELECT\n", "        tx_id,\n", "        swapper                 AS wallet_addr,\n", "        swap_to_mint            AS mint,\n", "        swap_to_amount          AS amount,\n", "        'in'                    AS direction\n", "    FROM wallet_swaps\n", "),\n", "\n", "-- 3️⃣  Pull every transfer involving the wallet\n", "--     and discard those that match a swap-leg pattern\n", "wallet_transfers AS (\n", "    SELECT\n", "        t.block_timestamp,\n", "        t.block_id,\n", "        t.tx_id,\n", "        CASE WHEN t.tx_from = '{wallet}' \n", "             THEN t.tx_from ELSE t.tx_to END            AS wallet,\n", "        /* signed amount for convenience (+in , –out) */\n", "        CASE WHEN t.tx_from = '{wallet}'\n", "             THEN -1 * t.amount ELSE t.amount END       AS amount,\n", "        t.mint,\n", "        'transfer'                                      AS type\n", "    FROM solana.core.fact_transfers AS t\n", "    LEFT JOIN swap_transfer_legs s\n", "      ON  t.tx_id        = s.tx_id\n", "      AND t.mint         = s.mint\n", "      AND t.amount       = s.amount\n", "      AND (   /* match the correct side of the leg */\n", "           (s.direction = 'out' AND t.tx_from = s.wallet_addr)\n", "        OR (s.direction = 'in'  AND t.tx_to   = s.wallet_addr)\n", "      )\n", "    WHERE (t.tx_from = '{wallet}'\n", "        OR t.tx_to   = '{wallet}' )\n", "      AND s.tx_id IS NULL          -- ➟ drop only the true swap legs\n", "),\n", "\n", "-- 4️⃣  Shape swap rows to union with transfers\n", "wallet_swaps_shaped AS (\n", "    SELECT\n", "        block_timestamp,\n", "        block_id,\n", "        tx_id,\n", "        swapper                                  AS wallet,\n", "        -swap_from_amount                        AS amount,      -- outgoing (signed)\n", "        swap_from_mint                           AS mint,\n", "        'swap'                                   AS type\n", "    FROM wallet_swaps\n", "    UNION ALL\n", "    SELECT\n", "        block_timestamp,\n", "        block_id,\n", "        tx_id,\n", "        swapper,\n", "        swap_to_amount                           AS amount,      -- incoming (signed)\n", "        swap_to_mint                             AS mint,\n", "        'swap'                                   AS type\n", "    FROM wallet_swaps\n", ")\n", "\n", "-- 5️⃣  Final ledger\n", "SELECT *\n", "FROM wallet_swaps_shaped\n", "UNION ALL\n", "SELECT *\n", "FROM wallet_transfers\n", "ORDER BY block_timestamp;\n", "\"\"\")\n", "all_transactions.columns"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of distinct transactions: 3893\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>block_timestamp</th>\n", "      <th>block_id</th>\n", "      <th>tx_id</th>\n", "      <th>wallet</th>\n", "      <th>amount</th>\n", "      <th>mint</th>\n", "      <th>type</th>\n", "      <th>__row_index</th>\n", "      <th>day</th>\n", "      <th>accounting_period</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>2022-01-27 07:41:44+00:00</td>\n", "      <td>*********</td>\n", "      <td>2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>15,194.49</td>\n", "      <td>EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v</td>\n", "      <td>swap</td>\n", "      <td>24</td>\n", "      <td>2022-01-27</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>2022-01-27 07:41:44+00:00</td>\n", "      <td>*********</td>\n", "      <td>2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>-170.00</td>\n", "      <td>So11111111111111111111111111111111111111112</td>\n", "      <td>swap</td>\n", "      <td>25</td>\n", "      <td>2022-01-27</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>2022-01-30 09:27:11+00:00</td>\n", "      <td>*********</td>\n", "      <td>3NjM1utGcAcZzPqt9gEACY9uMrn6dx3n8YpjFT5VUsK2xrXMCekzi4bz1ydAqyonY8w4ENat9DiLsCo383WeHn8u</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>18,064.35</td>\n", "      <td>EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v</td>\n", "      <td>swap</td>\n", "      <td>29</td>\n", "      <td>2022-01-30</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>2022-01-30 09:27:11+00:00</td>\n", "      <td>*********</td>\n", "      <td>3NjM1utGcAcZzPqt9gEACY9uMrn6dx3n8YpjFT5VUsK2xrXMCekzi4bz1ydAqyonY8w4ENat9DiLsCo383WeHn8u</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>-190.00</td>\n", "      <td>So11111111111111111111111111111111111111112</td>\n", "      <td>swap</td>\n", "      <td>30</td>\n", "      <td>2022-01-30</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>2022-01-30 09:39:58+00:00</td>\n", "      <td>118516499</td>\n", "      <td>2YPbdLHxuyUuDqESC2C8gsWkwJEFWPnaLfmscT6vGLs9fMN4hvjcJhrjGEzEaoBnJHHRz74YEMXTbdfjNkULYzuM</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>20,491.36</td>\n", "      <td>EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v</td>\n", "      <td>swap</td>\n", "      <td>31</td>\n", "      <td>2022-01-30</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5291</th>\n", "      <td>2025-05-11 10:48:16+00:00</td>\n", "      <td>339289930</td>\n", "      <td>3T1t4XT3bgfBomkEmj5EuAd4bYawxbusAmj9sRtdcxiofAACj4gYa8BvwrDxj96S8f1Cuq9tVNsBaU5iCjqaoENs</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>173.84</td>\n", "      <td>EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v</td>\n", "      <td>swap</td>\n", "      <td>5291</td>\n", "      <td>2025-05-11</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5686</th>\n", "      <td>2025-06-19 20:58:13+00:00</td>\n", "      <td>347893245</td>\n", "      <td>5XPy2QVMRhgZaPeLkdUAXPC1hMPnZS68G1nvC235ddfiFNfoiUdFoQLfwvMFPuymmeR3G24GXiVgng7saEWVGjPg</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>218.02</td>\n", "      <td>EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v</td>\n", "      <td>swap</td>\n", "      <td>5686</td>\n", "      <td>2025-06-19</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5687</th>\n", "      <td>2025-06-19 20:58:13+00:00</td>\n", "      <td>347893245</td>\n", "      <td>5XPy2QVMRhgZaPeLkdUAXPC1hMPnZS68G1nvC235ddfiFNfoiUdFoQLfwvMFPuymmeR3G24GXiVgng7saEWVGjPg</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>-1.50</td>\n", "      <td>So11111111111111111111111111111111111111112</td>\n", "      <td>swap</td>\n", "      <td>5687</td>\n", "      <td>2025-06-19</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5707</th>\n", "      <td>2025-06-19 21:01:15+00:00</td>\n", "      <td>347893698</td>\n", "      <td>Rr59X3mSy8yhC21fWDDXQvr9RWb29ZEUXeUUUedcLWA4QrAtcMFh4phRCAabJC8G31JG1SL4ifxK7a4dLPKx65p</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>42,218.09</td>\n", "      <td>G9tt98aYSznRk7jWsfuz9FnTdokxS6Brohdo9hSmjTRB</td>\n", "      <td>swap</td>\n", "      <td>5707</td>\n", "      <td>2025-06-19</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5708</th>\n", "      <td>2025-06-19 21:01:15+00:00</td>\n", "      <td>347893698</td>\n", "      <td>Rr59X3mSy8yhC21fWDDXQvr9RWb29ZEUXeUUUedcLWA4QrAtcMFh4phRCAabJC8G31JG1SL4ifxK7a4dLPKx65p</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>-56.70</td>\n", "      <td>EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v</td>\n", "      <td>swap</td>\n", "      <td>5708</td>\n", "      <td>2025-06-19</td>\n", "      <td>2025</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>366 rows × 10 columns</p>\n", "</div>"], "text/plain": ["               block_timestamp   block_id                                                                                     tx_id                                        wallet    amount                                          mint  type  __row_index         day  accounting_period\n", "24   2022-01-27 07:41:44+00:00  *********  2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB 15,194.49  EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v  swap           24  2022-01-27               2022\n", "25   2022-01-27 07:41:44+00:00  *********  2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB   -170.00   So11111111111111111111111111111111111111112  swap           25  2022-01-27               2022\n", "29   2022-01-30 09:27:11+00:00  *********  3NjM1utGcAcZzPqt9gEACY9uMrn6dx3n8YpjFT5VUsK2xrXMCekzi4bz1ydAqyonY8w4ENat9DiLsCo383WeHn8u  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB 18,064.35  EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v  swap           29  2022-01-30               2022\n", "30   2022-01-30 09:27:11+00:00  *********  3NjM1utGcAcZzPqt9gEACY9uMrn6dx3n8YpjFT5VUsK2xrXMCekzi4bz1ydAqyonY8w4ENat9DiLsCo383WeHn8u  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB   -190.00   So11111111111111111111111111111111111111112  swap           30  2022-01-30               2022\n", "31   2022-01-30 09:39:58+00:00  118516499  2YPbdLHxuyUuDqESC2C8gsWkwJEFWPnaLfmscT6vGLs9fMN4hvjcJhrjGEzEaoBnJHHRz74YEMXTbdfjNkULYzuM  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB 20,491.36  EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v  swap           31  2022-01-30               2022\n", "...                        ...        ...                                                                                       ...                                           ...       ...                                           ...   ...          ...         ...                ...\n", "5291 2025-05-11 10:48:16+00:00  339289930  3T1t4XT3bgfBomkEmj5EuAd4bYawxbusAmj9sRtdcxiofAACj4gYa8BvwrDxj96S8f1Cuq9tVNsBaU5iCjqaoENs  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB    173.84  EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v  swap         5291  2025-05-11               2025\n", "5686 2025-06-19 20:58:13+00:00  347893245  5XPy2QVMRhgZaPeLkdUAXPC1hMPnZS68G1nvC235ddfiFNfoiUdFoQLfwvMFPuymmeR3G24GXiVgng7saEWVGjPg  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB    218.02  EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v  swap         5686  2025-06-19               2025\n", "5687 2025-06-19 20:58:13+00:00  347893245  5XPy2QVMRhgZaPeLkdUAXPC1hMPnZS68G1nvC235ddfiFNfoiUdFoQLfwvMFPuymmeR3G24GXiVgng7saEWVGjPg  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB     -1.50   So11111111111111111111111111111111111111112  swap         5687  2025-06-19               2025\n", "5707 2025-06-19 21:01:15+00:00  347893698   Rr59X3mSy8yhC21fWDDXQvr9RWb29ZEUXeUUUedcLWA4QrAtcMFh4phRCAabJC8G31JG1SL4ifxK7a4dLPKx65p  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB 42,218.09  G9tt98aYSznRk7jWsfuz9FnTdokxS6Brohdo9hSmjTRB  swap         5707  2025-06-19               2025\n", "5708 2025-06-19 21:01:15+00:00  347893698   Rr59X3mSy8yhC21fWDDXQvr9RWb29ZEUXeUUUedcLWA4QrAtcMFh4phRCAabJC8G31JG1SL4ifxK7a4dLPKx65p  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB    -56.70  EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v  swap         5708  2025-06-19               2025\n", "\n", "[366 rows x 10 columns]"]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["# Count distinct transactions\n", "distinct_tx_count = all_transactions['tx_id'].nunique()\n", "print(f\"Number of distinct transactions: {distinct_tx_count}\")\n", "swaps = all_transactions[all_transactions[\"type\"] == \"swap\"]\n", "swaps\n"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>block_timestamp</th>\n", "      <th>block_id</th>\n", "      <th>tx_id</th>\n", "      <th>wallet</th>\n", "      <th>amount</th>\n", "      <th>mint</th>\n", "      <th>type</th>\n", "      <th>__row_index</th>\n", "      <th>day</th>\n", "      <th>accounting_period</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>2022-01-27 07:41:44+00:00</td>\n", "      <td>*********</td>\n", "      <td>2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>-127.85</td>\n", "      <td>EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v</td>\n", "      <td>transfer</td>\n", "      <td>23</td>\n", "      <td>2022-01-27</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>2022-01-27 07:41:44+00:00</td>\n", "      <td>*********</td>\n", "      <td>2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>15,194.49</td>\n", "      <td>EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v</td>\n", "      <td>swap</td>\n", "      <td>24</td>\n", "      <td>2022-01-27</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>2022-01-27 07:41:44+00:00</td>\n", "      <td>*********</td>\n", "      <td>2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>-170.00</td>\n", "      <td>So11111111111111111111111111111111111111112</td>\n", "      <td>swap</td>\n", "      <td>25</td>\n", "      <td>2022-01-27</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>2022-01-30 09:27:11+00:00</td>\n", "      <td>*********</td>\n", "      <td>3NjM1utGcAcZzPqt9gEACY9uMrn6dx3n8YpjFT5VUsK2xrXMCekzi4bz1ydAqyonY8w4ENat9DiLsCo383WeHn8u</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>-152.01</td>\n", "      <td>EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v</td>\n", "      <td>transfer</td>\n", "      <td>28</td>\n", "      <td>2022-01-30</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>2022-01-30 09:27:11+00:00</td>\n", "      <td>*********</td>\n", "      <td>3NjM1utGcAcZzPqt9gEACY9uMrn6dx3n8YpjFT5VUsK2xrXMCekzi4bz1ydAqyonY8w4ENat9DiLsCo383WeHn8u</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>18,064.35</td>\n", "      <td>EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v</td>\n", "      <td>swap</td>\n", "      <td>29</td>\n", "      <td>2022-01-30</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5705</th>\n", "      <td>2025-06-19 21:01:15+00:00</td>\n", "      <td>347893698</td>\n", "      <td>Rr59X3mSy8yhC21fWDDXQvr9RWb29ZEUXeUUUedcLWA4QrAtcMFh4phRCAabJC8G31JG1SL4ifxK7a4dLPKx65p</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>98,243.96</td>\n", "      <td>G9tt98aYSznRk7jWsfuz9FnTdokxS6Brohdo9hSmjTRB</td>\n", "      <td>transfer</td>\n", "      <td>5705</td>\n", "      <td>2025-06-19</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5706</th>\n", "      <td>2025-06-19 21:01:15+00:00</td>\n", "      <td>347893698</td>\n", "      <td>Rr59X3mSy8yhC21fWDDXQvr9RWb29ZEUXeUUUedcLWA4QrAtcMFh4phRCAabJC8G31JG1SL4ifxK7a4dLPKx65p</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>-0.13</td>\n", "      <td>EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v</td>\n", "      <td>transfer</td>\n", "      <td>5706</td>\n", "      <td>2025-06-19</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5707</th>\n", "      <td>2025-06-19 21:01:15+00:00</td>\n", "      <td>347893698</td>\n", "      <td>Rr59X3mSy8yhC21fWDDXQvr9RWb29ZEUXeUUUedcLWA4QrAtcMFh4phRCAabJC8G31JG1SL4ifxK7a4dLPKx65p</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>42,218.09</td>\n", "      <td>G9tt98aYSznRk7jWsfuz9FnTdokxS6Brohdo9hSmjTRB</td>\n", "      <td>swap</td>\n", "      <td>5707</td>\n", "      <td>2025-06-19</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5708</th>\n", "      <td>2025-06-19 21:01:15+00:00</td>\n", "      <td>347893698</td>\n", "      <td>Rr59X3mSy8yhC21fWDDXQvr9RWb29ZEUXeUUUedcLWA4QrAtcMFh4phRCAabJC8G31JG1SL4ifxK7a4dLPKx65p</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>-56.70</td>\n", "      <td>EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v</td>\n", "      <td>swap</td>\n", "      <td>5708</td>\n", "      <td>2025-06-19</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5709</th>\n", "      <td>2025-06-19 21:01:15+00:00</td>\n", "      <td>347893698</td>\n", "      <td>Rr59X3mSy8yhC21fWDDXQvr9RWb29ZEUXeUUUedcLWA4QrAtcMFh4phRCAabJC8G31JG1SL4ifxK7a4dLPKx65p</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>-131.87</td>\n", "      <td>EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v</td>\n", "      <td>transfer</td>\n", "      <td>5709</td>\n", "      <td>2025-06-19</td>\n", "      <td>2025</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>544 rows × 10 columns</p>\n", "</div>"], "text/plain": ["               block_timestamp   block_id                                                                                     tx_id                                        wallet    amount                                          mint      type  __row_index         day  accounting_period\n", "23   2022-01-27 07:41:44+00:00  *********  2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB   -127.85  EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v  transfer           23  2022-01-27               2022\n", "24   2022-01-27 07:41:44+00:00  *********  2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB 15,194.49  EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v      swap           24  2022-01-27               2022\n", "25   2022-01-27 07:41:44+00:00  *********  2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB   -170.00   So11111111111111111111111111111111111111112      swap           25  2022-01-27               2022\n", "28   2022-01-30 09:27:11+00:00  *********  3NjM1utGcAcZzPqt9gEACY9uMrn6dx3n8YpjFT5VUsK2xrXMCekzi4bz1ydAqyonY8w4ENat9DiLsCo383WeHn8u  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB   -152.01  EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v  transfer           28  2022-01-30               2022\n", "29   2022-01-30 09:27:11+00:00  *********  3NjM1utGcAcZzPqt9gEACY9uMrn6dx3n8YpjFT5VUsK2xrXMCekzi4bz1ydAqyonY8w4ENat9DiLsCo383WeHn8u  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB 18,064.35  EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v      swap           29  2022-01-30               2022\n", "...                        ...        ...                                                                                       ...                                           ...       ...                                           ...       ...          ...         ...                ...\n", "5705 2025-06-19 21:01:15+00:00  347893698   Rr59X3mSy8yhC21fWDDXQvr9RWb29ZEUXeUUUedcLWA4QrAtcMFh4phRCAabJC8G31JG1SL4ifxK7a4dLPKx65p  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB 98,243.96  G9tt98aYSznRk7jWsfuz9FnTdokxS6Brohdo9hSmjTRB  transfer         5705  2025-06-19               2025\n", "5706 2025-06-19 21:01:15+00:00  347893698   Rr59X3mSy8yhC21fWDDXQvr9RWb29ZEUXeUUUedcLWA4QrAtcMFh4phRCAabJC8G31JG1SL4ifxK7a4dLPKx65p  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB     -0.13  EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v  transfer         5706  2025-06-19               2025\n", "5707 2025-06-19 21:01:15+00:00  347893698   Rr59X3mSy8yhC21fWDDXQvr9RWb29ZEUXeUUUedcLWA4QrAtcMFh4phRCAabJC8G31JG1SL4ifxK7a4dLPKx65p  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB 42,218.09  G9tt98aYSznRk7jWsfuz9FnTdokxS6Brohdo9hSmjTRB      swap         5707  2025-06-19               2025\n", "5708 2025-06-19 21:01:15+00:00  347893698   Rr59X3mSy8yhC21fWDDXQvr9RWb29ZEUXeUUUedcLWA4QrAtcMFh4phRCAabJC8G31JG1SL4ifxK7a4dLPKx65p  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB    -56.70  EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v      swap         5708  2025-06-19               2025\n", "5709 2025-06-19 21:01:15+00:00  347893698   Rr59X3mSy8yhC21fWDDXQvr9RWb29ZEUXeUUUedcLWA4QrAtcMFh4phRCAabJC8G31JG1SL4ifxK7a4dLPKx65p  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB   -131.87  EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v  transfer         5709  2025-06-19               2025\n", "\n", "[544 rows x 10 columns]"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["# Find swap transactions that have other transactions with the same tx_id\n", "swap_txs = all_transactions[all_transactions[\"type\"] == \"swap\"]\n", "swap_tx_ids = swap_txs[\"tx_id\"].unique()\n", "\n", "# Find transactions that share tx_id with swaps\n", "transactions_with_swap_tx_ids = all_transactions[all_transactions[\"tx_id\"].isin(swap_tx_ids)]\n", "\n", "# Group by tx_id and check which have multiple transactions\n", "tx_id_counts = transactions_with_swap_tx_ids[\"tx_id\"].value_counts()\n", "multi_transaction_tx_ids = tx_id_counts[tx_id_counts > 1].index\n", "\n", "# Filter swap transactions that have other transactions in the same tx_id\n", "swap_txs_with_other_transactions = all_transactions[all_transactions[\"tx_id\"].isin(multi_transaction_tx_ids)]\n", "swap_txs_with_other_transactions"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["found 3 rows\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>block_timestamp</th>\n", "      <th>block_id</th>\n", "      <th>tx_id</th>\n", "      <th>index</th>\n", "      <th>tx_from</th>\n", "      <th>tx_to</th>\n", "      <th>amount</th>\n", "      <th>mint</th>\n", "      <th>fact_transfers_id</th>\n", "      <th>inserted_timestamp</th>\n", "      <th>modified_timestamp</th>\n", "      <th>__row_index</th>\n", "      <th>day</th>\n", "      <th>accounting_period</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2022-01-27 07:41:44+00:00</td>\n", "      <td>*********</td>\n", "      <td>2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm</td>\n", "      <td>2.0</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1</td>\n", "      <td>170.00</td>\n", "      <td>So11111111111111111111111111111111111111112</td>\n", "      <td>a014ad40de0dbfaa4eed5d357da00b8a</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>0</td>\n", "      <td>2022-01-27</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2022-01-27 07:41:44+00:00</td>\n", "      <td>*********</td>\n", "      <td>2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm</td>\n", "      <td>3</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>tzvXws1qhmfdPkPcprezULCDQPAJqzPhbZ3SMrqRPNE</td>\n", "      <td>127.85</td>\n", "      <td>EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v</td>\n", "      <td>42ca2a62513c300ca35568947c663e03</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>1</td>\n", "      <td>2022-01-27</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2022-01-27 07:41:44+00:00</td>\n", "      <td>*********</td>\n", "      <td>2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm</td>\n", "      <td>2.1</td>\n", "      <td>5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>15,194.49</td>\n", "      <td>EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v</td>\n", "      <td>d21bb99a0660c99af523286dab3de289</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2</td>\n", "      <td>2022-01-27</td>\n", "      <td>2022</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            block_timestamp   block_id                                                                                     tx_id index                                       tx_from                                         tx_to    amount                                          mint                 fact_transfers_id        inserted_timestamp        modified_timestamp  __row_index         day  accounting_period\n", "0 2022-01-27 07:41:44+00:00  *********  2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm   2.0  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB  5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1    170.00   So11111111111111111111111111111111111111112  a014ad40de0dbfaa4eed5d357da00b8a  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z            0  2022-01-27               2022\n", "1 2022-01-27 07:41:44+00:00  *********  2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm     3  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB   tzvXws1qhmfdPkPcprezULCDQPAJqzPhbZ3SMrqRPNE    127.85  EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v  42ca2a62513c300ca35568947c663e03  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z            1  2022-01-27               2022\n", "2 2022-01-27 07:41:44+00:00  *********  2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm   2.1  5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB 15,194.49  EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v  d21bb99a0660c99af523286dab3de289  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z            2  2022-01-27               2022"]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["df = utils.query_df(f\"\"\"\n", "    SELECT\n", "        *\n", "    FROM\n", "        solana.core.fact_transfers\n", "    WHERE\n", "        tx_id = '2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm'\n", "\"\"\")\n", "df"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["found 1 rows\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>block_timestamp</th>\n", "      <th>block_id</th>\n", "      <th>tx_id</th>\n", "      <th>succeeded</th>\n", "      <th>swapper</th>\n", "      <th>swap_from_amount</th>\n", "      <th>swap_from_mint</th>\n", "      <th>swap_to_amount</th>\n", "      <th>swap_to_mint</th>\n", "      <th>program_id</th>\n", "      <th>swap_program</th>\n", "      <th>_log_id</th>\n", "      <th>fact_swaps_id</th>\n", "      <th>inserted_timestamp</th>\n", "      <th>modified_timestamp</th>\n", "      <th>__row_index</th>\n", "      <th>day</th>\n", "      <th>accounting_period</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2022-01-27 07:41:44+00:00</td>\n", "      <td>*********</td>\n", "      <td>2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm</td>\n", "      <td>True</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>170</td>\n", "      <td>So11111111111111111111111111111111111111112</td>\n", "      <td>15,194.49</td>\n", "      <td>EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v</td>\n", "      <td>675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8</td>\n", "      <td>Raydium Liquidity Pool V4</td>\n", "      <td>2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm-1-Raydium Liquidity Pool V4</td>\n", "      <td>bf14181c03cb145d97dfdc88d6662132</td>\n", "      <td>2024-05-30T20:16:41.727Z</td>\n", "      <td>2024-05-30T20:16:41.727Z</td>\n", "      <td>0</td>\n", "      <td>2022-01-27</td>\n", "      <td>2022</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            block_timestamp   block_id                                                                                     tx_id  succeeded                                       swapper  swap_from_amount                               swap_from_mint  swap_to_amount                                  swap_to_mint                                    program_id               swap_program                                                                                                               _log_id                     fact_swaps_id        inserted_timestamp        modified_timestamp  __row_index         day  accounting_period\n", "0 2022-01-27 07:41:44+00:00  *********  2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm       True  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB               170  So11111111111111111111111111111111111111112       15,194.49  EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v  675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8  Raydium Liquidity Pool V4  2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm-1-Raydium Liquidity Pool V4  bf14181c03cb145d97dfdc88d6662132  2024-05-30T20:16:41.727Z  2024-05-30T20:16:41.727Z            0  2022-01-27               2022"]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["df = utils.query_df(f\"\"\"\n", "    SELECT\n", "        *\n", "    FROM\n", "        solana.defi.fact_swaps\n", "    WHERE\n", "        tx_id = '2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm'\n", "\"\"\")\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["found 5411 rows\n"]}, {"data": {"text/plain": ["Index(['block_timestamp', 'block_id', 'tx_id', 'wallet', 'swap_from_amount',\n", "       'swap_from_mint', 'swap_to_amount', 'swap_to_mint', 'swap_program',\n", "       'action_type', 'token_a_mint', 'token_a_amount', 'token_b_mint',\n", "       'token_b_amount', 'platform', 'pool_name', 'amount', 'mint', 'type',\n", "       '__row_index', 'day', 'accounting_period'],\n", "      dtype='object')"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["wallet = utils.eu_wallet\n", "transactions = utils.query_df(f\"\"\"\n", "WITH wallet_swaps AS (\n", "    SELECT\n", "        block_timestamp,\n", "        block_id,\n", "        tx_id,\n", "        swapper                     AS wallet,\n", "        from_amount,\n", "        from_mint,\n", "        to_amount,\n", "        to_mint,\n", "        program,\n", "        'swap'                      AS type\n", "    FROM solana.defi.fact_swaps\n", "    WHERE succeeded\n", "      AND swapper = '{wallet}'\n", "),\n", "\n", "/* 2️⃣  All liquidity pool actions by the wallet */\n", "wallet_lp_actions AS (\n", "    SELECT\n", "        block_timestamp,\n", "        block_id,\n", "        tx_id,\n", "        provider_address            AS wallet,\n", "        action_type,\n", "        token_a_mint,\n", "        token_a_amount,\n", "        token_b_mint,\n", "        token_b_amount,\n", "        platform,\n", "        pool_name,\n", "        'liquidity_action'          AS type\n", "    FROM solana.defi.ez_liquidity_pool_actions\n", "    WHERE provider_address = '{wallet}'\n", "),\n", "\n", "/* 3️⃣  Transaction IDs that contain swaps or LP actions */\n", "defi_tx_ids AS (\n", "    SELECT DISTINCT tx_id FROM wallet_swaps\n", "    UNION\n", "    SELECT DISTINCT tx_id FROM wallet_lp_actions\n", "),\n", "\n", "/* 4️⃣  Transfers involving the wallet, EXCEPT those in DeFi transactions */\n", "wallet_transfers AS (\n", "    SELECT\n", "        t.block_timestamp,\n", "        t.block_id,\n", "        t.tx_id,\n", "        t.amount,\n", "        t.mint,\n", "        'transfer'                  AS type\n", "    FROM solana.core.fact_transfers t\n", "    LEFT JOIN defi_tx_ids d\n", "      ON t.tx_id = d.tx_id\n", "    WHERE (t.tx_from = 'EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB'\n", "        OR t.tx_to   = 'EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB')\n", "      AND d.tx_id IS NULL           -- ← exclude any tx with swaps/LP actions\n", ")\n", "\n", "/* 5️⃣  Final result: swaps + LP actions + non-DeFi transfers */\n", "SELECT \n", "    block_timestamp,\n", "    block_id,\n", "    tx_id,\n", "    wallet,\n", "    swap_from_amount,\n", "    swap_from_mint,\n", "    swap_to_amount,\n", "    swap_to_mint,\n", "    swap_program,\n", "    NULL AS action_type,\n", "    NULL AS token_a_mint,\n", "    NULL AS token_a_amount,\n", "    NULL AS token_b_mint,\n", "    NULL AS token_b_amount,\n", "    NULL AS platform,\n", "    NULL AS pool_name,\n", "    NULL AS amount,\n", "    NULL AS mint,\n", "    type\n", "FROM wallet_swaps\n", "\n", "UNION ALL\n", "\n", "SELECT \n", "    block_timestamp,\n", "    block_id,\n", "    tx_id,\n", "    wallet,\n", "    NULL AS swap_from_amount,\n", "    NULL AS swap_from_mint,\n", "    NULL AS swap_to_amount,\n", "    NULL AS swap_to_mint,\n", "    NULL AS swap_program,\n", "    action_type,\n", "    token_a_mint,\n", "    token_a_amount,\n", "    token_b_mint,\n", "    token_b_amount,\n", "    platform,\n", "    pool_name,\n", "    NULL AS amount,\n", "    NULL AS mint,\n", "    type\n", "FROM wallet_lp_actions\n", "\n", "UNION ALL\n", "\n", "SELECT \n", "    block_timestamp,\n", "    block_id,\n", "    tx_id,\n", "    wallet,\n", "    NULL AS swap_from_amount,\n", "    NULL AS swap_from_mint,\n", "    NULL AS swap_to_amount,\n", "    NULL AS swap_to_mint,\n", "    NULL AS swap_program,\n", "    NULL AS action_type,\n", "    NULL AS token_a_mint,\n", "    NULL AS token_a_amount,\n", "    NULL AS token_b_mint,\n", "    NULL AS token_b_amount,\n", "    NULL AS platform,\n", "    NULL AS pool_name,\n", "    amount,\n", "    mint,\n", "    type\n", "FROM wallet_transfers\n", "\n", "ORDER BY block_timestamp;\"\"\")\n", "transactions.columns"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found 2336 rows with duplicated tx_id values:\n", "Number of unique duplicated tx_ids: 826\n", "\n", "Duplicated tx_ids:\n", "                                                                                        tx_id      type           block_timestamp\n", "5000  185qR9RfTqBjA4MSLXynwtS3N5dAP1htsuyWB9x7kNc1ZGKQZBS592RprY2BFfgNcRFpkGqJqKTMVZLitx79Um8  transfer 2025-05-15 23:00:44+00:00\n", "4998  185qR9RfTqBjA4MSLXynwtS3N5dAP1htsuyWB9x7kNc1ZGKQZBS592RprY2BFfgNcRFpkGqJqKTMVZLitx79Um8  transfer 2025-05-15 23:00:44+00:00\n", "5217  1EDck2bnFLLXNVE6rMxq2Zgyg3GZ7WPeby1wmxWPAbwSDsxdmy88g1KmetL4qaCLNdJk1txXu9p9vkZVRurEt6e  transfer 2025-06-05 10:58:51+00:00\n", "5219  1EDck2bnFLLXNVE6rMxq2Zgyg3GZ7WPeby1wmxWPAbwSDsxdmy88g1KmetL4qaCLNdJk1txXu9p9vkZVRurEt6e  transfer 2025-06-05 10:58:51+00:00\n", "3988  1nwHahyaFQP6sCN5Vs6A3LgFeqnxepRQ3PyvpUrgYGGbJuUKhvpCwyrt4KBEpD5T5Ey1MqunB8keFikkgZQRsMB  transfer 2025-03-02 18:53:26+00:00\n", "...                                                                                       ...       ...                       ...\n", "3532  yWyUHVgSoFUTkQbHjKL5SHDTGDZYrW2Re7XtPu5HmfCUhYTtpoeabKgeUuz4iH979u1tbkQt8ZuEmyVkEu5sdRu  transfer 2025-01-14 13:32:04+00:00\n", "3531  yWyUHVgSoFUTkQbHjKL5SHDTGDZYrW2Re7XtPu5HmfCUhYTtpoeabKgeUuz4iH979u1tbkQt8ZuEmyVkEu5sdRu  transfer 2025-01-14 13:32:04+00:00\n", "3530  yWyUHVgSoFUTkQbHjKL5SHDTGDZYrW2Re7XtPu5HmfCUhYTtpoeabKgeUuz4iH979u1tbkQt8ZuEmyVkEu5sdRu  transfer 2025-01-14 13:32:04+00:00\n", "789   zXMxA7Jy6dhL7uFfsTcrbf7k8zgoPrbY7NtM9vBcCfqHCWLNDz4PNYezBRXRHEKDid6T81GgxMX3tsQy8Pbd1fn  transfer 2023-06-07 14:06:21+00:00\n", "790   zXMxA7Jy6dhL7uFfsTcrbf7k8zgoPrbY7NtM9vBcCfqHCWLNDz4PNYezBRXRHEKDid6T81GgxMX3tsQy8Pbd1fn  transfer 2023-06-07 14:06:21+00:00\n", "\n", "[2336 rows x 3 columns]\n"]}], "source": ["# Check for duplicated tx_id values in transactions dataframe\n", "duplicated_tx_ids = transactions[transactions.duplicated(subset=['tx_id'], keep=False)]\n", "\n", "if len(duplicated_tx_ids) > 0:\n", "    print(f\"Found {len(duplicated_tx_ids)} rows with duplicated tx_id values:\")\n", "    print(f\"Number of unique duplicated tx_ids: {duplicated_tx_ids['tx_id'].nunique()}\")\n", "    print(\"\\nDuplicated tx_ids:\")\n", "    print(duplicated_tx_ids[['tx_id', 'type', 'block_timestamp']].sort_values('tx_id'))\n", "else:\n", "    print(\"No duplicated tx_id values found in transactions dataframe\")\n"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["found 5 rows\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>block_timestamp</th>\n", "      <th>block_id</th>\n", "      <th>tx_id</th>\n", "      <th>signers</th>\n", "      <th>succeeded</th>\n", "      <th>index</th>\n", "      <th>event_type</th>\n", "      <th>program_id</th>\n", "      <th>instruction</th>\n", "      <th>inner_instruction</th>\n", "      <th>fact_events_id</th>\n", "      <th>inserted_timestamp</th>\n", "      <th>modified_timestamp</th>\n", "      <th>__row_index</th>\n", "      <th>day</th>\n", "      <th>accounting_period</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2022-01-27 07:41:44+00:00</td>\n", "      <td>*********</td>\n", "      <td>2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm</td>\n", "      <td>[EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB, BpJhxEDciCaiFAcjHEFoe3Tjs7Uyz5rtjS69dLV47t71]</td>\n", "      <td>True</td>\n", "      <td>0</td>\n", "      <td>createAccount</td>\n", "      <td>11111111111111111111111111111111</td>\n", "      <td>{'parsed': {'info': {'lamports': ************, 'newAccount': 'BpJhxEDciCaiFAcjHEFoe3Tjs7Uyz5rtjS69dLV47t71', 'owner': 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA', 'source': 'EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB', 'space': 165}, 'type': 'createAccount'}, 'program': 'system', 'programId': '11111111111111111111111111111111'}</td>\n", "      <td>None</td>\n", "      <td>7dad32636b34a6ea323a340f369d8259</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>3</td>\n", "      <td>2022-01-27</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2022-01-27 07:41:44+00:00</td>\n", "      <td>*********</td>\n", "      <td>2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm</td>\n", "      <td>[EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB, BpJhxEDciCaiFAcjHEFoe3Tjs7Uyz5rtjS69dLV47t71]</td>\n", "      <td>True</td>\n", "      <td>1</td>\n", "      <td>initializeAccount</td>\n", "      <td>TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA</td>\n", "      <td>{'parsed': {'info': {'account': 'BpJhxEDciCaiFAcjHEFoe3Tjs7Uyz5rtjS69dLV47t71', 'mint': 'So11111111111111111111111111111111111111112', 'owner': 'EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB', 'rentSysvar': 'SysvarRent111111111111111111111111111111111'}, 'type': 'initializeAccount'}, 'program': 'spl-token', 'programId': 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'}</td>\n", "      <td>None</td>\n", "      <td>310c13b32b904c23e6dadc579822dc5d</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>0</td>\n", "      <td>2022-01-27</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2022-01-27 07:41:44+00:00</td>\n", "      <td>*********</td>\n", "      <td>2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm</td>\n", "      <td>[EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB, BpJhxEDciCaiFAcjHEFoe3Tjs7Uyz5rtjS69dLV47t71]</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>None</td>\n", "      <td>675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8</td>\n", "      <td>{'accounts': ['TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA', '58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2', '5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1', 'HRk9CMrpq7Jn9sh7mzxE8CChHG8dneX9p475QKz4Fsfc', 'CZza3Ej4Mc58MnxWA385itCC9jCo3L1D7zc3LKy1bZMR', 'DQyrAcCrDXQ7NeoqGgDCZwBvWDcYmFCjSb9JtteuvPpz', 'HLmqeL62xR1QoZ1HKKbXRrdN1p3phKpxRMb2VVopvBBz', '9xQeWvG816bUx9EPjHmaT23yvVM2ZWbrrpZb9PusVFin', '9wFFyRfZBsuAha4YcuxcXLKwMxJR43S7fPfQLusDBzvT', '14ivtgssEBoBjuZJtSAPKYgpUK7DmnSwuPMqJoVTSgKJ', 'CEQdAFKdycHugujQg9k2wbmxjcpdYZyVLfV9WerTnafJ', '5KKsLVU6TcbVDK4BS6K1DGDxnh4Q9xjYJ8XaDCG5t8ht', '36c6YqAwyGKQG66XEp2dJc5JqjaBNv7sVghEtJv4c7u6', '8CFo8bL8mZQK8abbFyypFMwEDd8tVJjHTTojMLgQTUSZ', 'F8Vyqk3unwxkXukZFQeYyGmFfTG3CAX4v24iyrjEYBJV', 'BpJhxEDciCaiFAcjHEFoe3Tjs7Uyz5rtjS69dLV47t71', '9gsRD2mjw4jne4YMj9uCM7WszRuJTnxWPc2HSPivMyy9', 'EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB'], 'data': '5uXUkEwX8chSwBbr3Gut49m', 'programId': '675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8'}</td>\n", "      <td>{'index': 2, 'instructions': [{'parsed': {'info': {'amount': '170000000000', 'authority': 'EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB', 'destination': 'DQyrAcCrDXQ7NeoqGgDCZwBvWDcYmFCjSb9JtteuvPpz', 'source': 'BpJhxEDciCaiFAcjHEFoe3Tjs7Uyz5rtjS69dLV47t71'}, 'type': 'transfer'}, 'program': 'spl-token', 'programId': 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'}, {'parsed': {'info': {'amount': '***********', 'authority': '5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1', 'destination': '9gsRD2mjw4jne4YMj9uCM7WszRuJTnxWPc2HSPivMyy9', 'source': 'HLmqeL62xR1QoZ1HKKbXRrdN1p3phKpxRMb2VVopvBBz'}, 'type': 'transfer'}, 'program': 'spl-token', 'programId': 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'}]}</td>\n", "      <td>94b60142803b531cbbef783f59a040f8</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>4</td>\n", "      <td>2022-01-27</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2022-01-27 07:41:44+00:00</td>\n", "      <td>*********</td>\n", "      <td>2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm</td>\n", "      <td>[EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB, BpJhxEDciCaiFAcjHEFoe3Tjs7Uyz5rtjS69dLV47t71]</td>\n", "      <td>True</td>\n", "      <td>3</td>\n", "      <td>transferChecked</td>\n", "      <td>TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA</td>\n", "      <td>{'parsed': {'info': {'authority': 'EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB', 'destination': 'APBH71knizgDfsY3kYkv9PDhTizaJXywC1XNNRLwdKHQ', 'mint': 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', 'source': '9gsRD2mjw4jne4YMj9uCM7WszRuJTnxWPc2HSPivMyy9', 'tokenAmount': {'amount': '127848396', 'decimals': 6, 'uiAmount': 127.848396, 'uiAmountString': '127.848396'}}, 'type': 'transferChecked'}, 'program': 'spl-token', 'programId': 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'}</td>\n", "      <td>None</td>\n", "      <td>42ca2a62513c300ca35568947c663e03</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>1</td>\n", "      <td>2022-01-27</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2022-01-27 07:41:44+00:00</td>\n", "      <td>*********</td>\n", "      <td>2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm</td>\n", "      <td>[EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB, BpJhxEDciCaiFAcjHEFoe3Tjs7Uyz5rtjS69dLV47t71]</td>\n", "      <td>True</td>\n", "      <td>4</td>\n", "      <td>closeAccount</td>\n", "      <td>TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA</td>\n", "      <td>{'parsed': {'info': {'account': 'BpJhxEDciCaiFAcjHEFoe3Tjs7Uyz5rtjS69dLV47t71', 'destination': 'EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB', 'owner': 'EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB'}, 'type': 'closeAccount'}, 'program': 'spl-token', 'programId': 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'}</td>\n", "      <td>None</td>\n", "      <td>75af89281122cfe3492988a0b302e87b</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2</td>\n", "      <td>2022-01-27</td>\n", "      <td>2022</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            block_timestamp   block_id                                                                                     tx_id                                                                                       signers  succeeded  index         event_type                                    program_id                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    instruction                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              inner_instruction                    fact_events_id        inserted_timestamp        modified_timestamp  __row_index         day  accounting_period\n", "3 2022-01-27 07:41:44+00:00  *********  2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm  [EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB, BpJhxEDciCaiFAcjHEFoe3Tjs7Uyz5rtjS69dLV47t71]       True      0      createAccount              11111111111111111111111111111111                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  {'parsed': {'info': {'lamports': ************, 'newAccount': 'BpJhxEDciCaiFAcjHEFoe3Tjs7Uyz5rtjS69dLV47t71', 'owner': 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA', 'source': 'EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB', 'space': 165}, 'type': 'createAccount'}, 'program': 'system', 'programId': '11111111111111111111111111111111'}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           None  7dad32636b34a6ea323a340f369d8259  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z            3  2022-01-27               2022\n", "0 2022-01-27 07:41:44+00:00  *********  2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm  [EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB, BpJhxEDciCaiFAcjHEFoe3Tjs7Uyz5rtjS69dLV47t71]       True      1  initializeAccount   TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                {'parsed': {'info': {'account': 'BpJhxEDciCaiFAcjHEFoe3Tjs7Uyz5rtjS69dLV47t71', 'mint': 'So11111111111111111111111111111111111111112', 'owner': 'EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB', 'rentSysvar': 'SysvarRent111111111111111111111111111111111'}, 'type': 'initializeAccount'}, 'program': 'spl-token', 'programId': 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           None  310c13b32b904c23e6dadc579822dc5d  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z            0  2022-01-27               2022\n", "4 2022-01-27 07:41:44+00:00  *********  2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm  [EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB, BpJhxEDciCaiFAcjHEFoe3Tjs7Uyz5rtjS69dLV47t71]       True      2               None  675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8  {'accounts': ['TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA', '58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2', '5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1', 'HRk9CMrpq7Jn9sh7mzxE8CChHG8dneX9p475QKz4Fsfc', 'CZza3Ej4Mc58MnxWA385itCC9jCo3L1D7zc3LKy1bZMR', 'DQyrAcCrDXQ7NeoqGgDCZwBvWDcYmFCjSb9JtteuvPpz', 'HLmqeL62xR1QoZ1HKKbXRrdN1p3phKpxRMb2VVopvBBz', '9xQeWvG816bUx9EPjHmaT23yvVM2ZWbrrpZb9PusVFin', '9wFFyRfZBsuAha4YcuxcXLKwMxJR43S7fPfQLusDBzvT', '14ivtgssEBoBjuZJtSAPKYgpUK7DmnSwuPMqJoVTSgKJ', 'CEQdAFKdycHugujQg9k2wbmxjcpdYZyVLfV9WerTnafJ', '5KKsLVU6TcbVDK4BS6K1DGDxnh4Q9xjYJ8XaDCG5t8ht', '36c6YqAwyGKQG66XEp2dJc5JqjaBNv7sVghEtJv4c7u6', '8CFo8bL8mZQK8abbFyypFMwEDd8tVJjHTTojMLgQTUSZ', 'F8Vyqk3unwxkXukZFQeYyGmFfTG3CAX4v24iyrjEYBJV', 'BpJhxEDciCaiFAcjHEFoe3Tjs7Uyz5rtjS69dLV47t71', '9gsRD2mjw4jne4YMj9uCM7WszRuJTnxWPc2HSPivMyy9', 'EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB'], 'data': '5uXUkEwX8chSwBbr3Gut49m', 'programId': '675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8'}  {'index': 2, 'instructions': [{'parsed': {'info': {'amount': '170000000000', 'authority': 'EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB', 'destination': 'DQyrAcCrDXQ7NeoqGgDCZwBvWDcYmFCjSb9JtteuvPpz', 'source': 'BpJhxEDciCaiFAcjHEFoe3Tjs7Uyz5rtjS69dLV47t71'}, 'type': 'transfer'}, 'program': 'spl-token', 'programId': 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'}, {'parsed': {'info': {'amount': '***********', 'authority': '5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1', 'destination': '9gsRD2mjw4jne4YMj9uCM7WszRuJTnxWPc2HSPivMyy9', 'source': 'HLmqeL62xR1QoZ1HKKbXRrdN1p3phKpxRMb2VVopvBBz'}, 'type': 'transfer'}, 'program': 'spl-token', 'programId': 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'}]}  94b60142803b531cbbef783f59a040f8  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z            4  2022-01-27               2022\n", "1 2022-01-27 07:41:44+00:00  *********  2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm  [EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB, BpJhxEDciCaiFAcjHEFoe3Tjs7Uyz5rtjS69dLV47t71]       True      3    transferChecked   TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             {'parsed': {'info': {'authority': 'EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB', 'destination': 'APBH71knizgDfsY3kYkv9PDhTizaJXywC1XNNRLwdKHQ', 'mint': 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', 'source': '9gsRD2mjw4jne4YMj9uCM7WszRuJTnxWPc2HSPivMyy9', 'tokenAmount': {'amount': '127848396', 'decimals': 6, 'uiAmount': 127.848396, 'uiAmountString': '127.848396'}}, 'type': 'transferChecked'}, 'program': 'spl-token', 'programId': 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           None  42ca2a62513c300ca35568947c663e03  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z            1  2022-01-27               2022\n", "2 2022-01-27 07:41:44+00:00  *********  2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm  [EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB, BpJhxEDciCaiFAcjHEFoe3Tjs7Uyz5rtjS69dLV47t71]       True      4       closeAccount   TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          {'parsed': {'info': {'account': 'BpJhxEDciCaiFAcjHEFoe3Tjs7Uyz5rtjS69dLV47t71', 'destination': 'EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB', 'owner': 'EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB'}, 'type': 'closeAccount'}, 'program': 'spl-token', 'programId': 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           None  75af89281122cfe3492988a0b302e87b  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z            2  2022-01-27               2022"]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["df = utils.query_df(f\"\"\"\n", "    SELECT\n", "        *\n", "    FROM\n", "        solana.core.fact_events\n", "    WHERE\n", "        tx_id = '2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm'\n", "        \n", "\"\"\")\n", "df = df.sort_values(by=\"index\")\n", "df"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["found 5679 rows\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>block_timestamp</th>\n", "      <th>block_id</th>\n", "      <th>tx_id</th>\n", "      <th>index</th>\n", "      <th>tx_from</th>\n", "      <th>tx_to</th>\n", "      <th>amount</th>\n", "      <th>mint</th>\n", "      <th>fact_transfers_id</th>\n", "      <th>inserted_timestamp</th>\n", "      <th>modified_timestamp</th>\n", "      <th>__row_index</th>\n", "      <th>day</th>\n", "      <th>accounting_period</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2021-11-04 22:12:58+00:00</td>\n", "      <td>*********</td>\n", "      <td>2nD74pbBFWNe1zcsUL6i9wuvEnS5B8o8jCjuU3gPgEtcZcnwKzEyQFdX8kx4mg6JeW2MCw4jKH9U4uvzB3b3NgsM</td>\n", "      <td>0</td>\n", "      <td>HvwRk8tX4ctHoKbnFETtekH9rVgoS3ib923MBRSyWF5M</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>0.01</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>9c47ce3a708aff5b1f8f99286b4c1a4b</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>0</td>\n", "      <td>2021-11-04</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2021-11-04 22:18:42+00:00</td>\n", "      <td>*********</td>\n", "      <td>5yPDecdnqQQC8fARJT4uxihPJiG9dv3QgJy3TSnnymhsVsoJ5KjRMcT2imVMaRi1in6t79TC3eK32ByFgQEpk9Su</td>\n", "      <td>0</td>\n", "      <td>AhFJ17wRz92LuJhmFNJrDXDZe1X9sekVfrUdpcbGZJjm</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>0.05</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>ead66654a054da6dc31f9544e62313d7</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>1</td>\n", "      <td>2021-11-04</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2021-11-11 14:09:00+00:00</td>\n", "      <td>*********</td>\n", "      <td>51SbRRZqYHyDn1xann2ZFcGPpGyUSdZEnhh9otrjAkDH6L8yndgT5DRjD9GTjszJKsrnpnTtaVJANCoucGnVM3j4</td>\n", "      <td>0</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>AhFJ17wRz92LuJhmFNJrDXDZe1X9sekVfrUdpcbGZJjm</td>\n", "      <td>0.04</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>3c7dec807e58c545054dd5013cb67b2e</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2</td>\n", "      <td>2021-11-11</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2021-11-26 05:41:17+00:00</td>\n", "      <td>108751233</td>\n", "      <td>556ehfYErPkf3UGx3dy5pACXo3EYW3wNrnjrQENa8XM4JDCMeDfsWMxbdsyQtfm66nkxmsGebjaCiDcwtdaKHmcp</td>\n", "      <td>0</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>HvwRk8tX4ctHoKbnFETtekH9rVgoS3ib923MBRSyWF5M</td>\n", "      <td>0.02</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>b9ba2cd34f3dfdaeb241d94782300712</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>3</td>\n", "      <td>2021-11-26</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2022-01-19 05:41:53+00:00</td>\n", "      <td>116897782</td>\n", "      <td>2T2cNYtAoLJXDw6KBfzVh4QNC9aUovBNxrBR8A91Shf9DFz1H9WGMdNC5tMzgFhAqk3yJ7TtcgPtvU5hLchBVYLe</td>\n", "      <td>2.1</td>\n", "      <td>HeJvFsGJFxGRQgpPjfaGXYt2tTkJ6Hd54wg4yFoAEGUV</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>0.00</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>89514f6750052d35bf52d7c4859bd101</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>4</td>\n", "      <td>2022-01-19</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5674</th>\n", "      <td>2025-06-24 15:21:00+00:00</td>\n", "      <td>348916532</td>\n", "      <td>46td3PQq3yzmEbnWvnEMZbP1j7ty2Lgh4NvVKYhi3YchFMU6GufhjKq3DoYmt6E2b3kRMhMsv45xqzA4KcCTvV7w</td>\n", "      <td>5</td>\n", "      <td>sain87NazoHou2KH9kHjB5NLejYimf174PX9Cjy7PFW</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>0.05</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>41d61a3b82b9d9ce5fb2c9af71a121d1</td>\n", "      <td>2025-06-24T15:56:06.419Z</td>\n", "      <td>2025-06-24T15:56:06.419Z</td>\n", "      <td>5674</td>\n", "      <td>2025-06-24</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5675</th>\n", "      <td>2025-06-24 15:21:00+00:00</td>\n", "      <td>348916532</td>\n", "      <td>46td3PQq3yzmEbnWvnEMZbP1j7ty2Lgh4NvVKYhi3YchFMU6GufhjKq3DoYmt6E2b3kRMhMsv45xqzA4KcCTvV7w</td>\n", "      <td>6</td>\n", "      <td>nuinTBZK45YybFaYypuBuJfYr6cq6v9ZtoRn1hmEr7V</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>0.01</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>31c05c10de18c8df9d46d251025c06de</td>\n", "      <td>2025-06-24T15:56:06.419Z</td>\n", "      <td>2025-06-24T15:56:06.419Z</td>\n", "      <td>5675</td>\n", "      <td>2025-06-24</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5676</th>\n", "      <td>2025-06-24 15:21:00+00:00</td>\n", "      <td>348916532</td>\n", "      <td>46td3PQq3yzmEbnWvnEMZbP1j7ty2Lgh4NvVKYhi3YchFMU6GufhjKq3DoYmt6E2b3kRMhMsv45xqzA4KcCTvV7w</td>\n", "      <td>2</td>\n", "      <td>spaLEShbJT3vHnBcQ8eV9oxWDs5Xy5b6cpUUXYXrpZh</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>0.30</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>af947761ecd6affd429ad12b0d7ae7f2</td>\n", "      <td>2025-06-24T15:56:06.419Z</td>\n", "      <td>2025-06-24T15:56:06.419Z</td>\n", "      <td>5676</td>\n", "      <td>2025-06-24</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5677</th>\n", "      <td>2025-06-24 15:21:00+00:00</td>\n", "      <td>348916532</td>\n", "      <td>46td3PQq3yzmEbnWvnEMZbP1j7ty2Lgh4NvVKYhi3YchFMU6GufhjKq3DoYmt6E2b3kRMhMsv45xqzA4KcCTvV7w</td>\n", "      <td>3</td>\n", "      <td>spaKwcgPN9N3Ab3eUeYBKyzCrFqM6t6b7137yAQR2cQ</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>0.26</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>ca62b8a8e3b1d4f64eb8d60d95d9352c</td>\n", "      <td>2025-06-24T15:56:06.419Z</td>\n", "      <td>2025-06-24T15:56:06.419Z</td>\n", "      <td>5677</td>\n", "      <td>2025-06-24</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5678</th>\n", "      <td>2025-06-24 15:21:00+00:00</td>\n", "      <td>348916532</td>\n", "      <td>46td3PQq3yzmEbnWvnEMZbP1j7ty2Lgh4NvVKYhi3YchFMU6GufhjKq3DoYmt6E2b3kRMhMsv45xqzA4KcCTvV7w</td>\n", "      <td>4</td>\n", "      <td>spad6uPeF5RRxUE2DZExHkihzhoqLQ4oA9u7Mvc6xKL</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>0.18</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>d0b1efbd8a7ba7ce1a70cc7ea5dc4592</td>\n", "      <td>2025-06-24T15:56:06.419Z</td>\n", "      <td>2025-06-24T15:56:06.419Z</td>\n", "      <td>5678</td>\n", "      <td>2025-06-24</td>\n", "      <td>2025</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5679 rows × 14 columns</p>\n", "</div>"], "text/plain": ["               block_timestamp   block_id                                                                                     tx_id index                                       tx_from                                         tx_to  amount                                         mint                 fact_transfers_id        inserted_timestamp        modified_timestamp  __row_index         day  accounting_period\n", "0    2021-11-04 22:12:58+00:00  *********  2nD74pbBFWNe1zcsUL6i9wuvEnS5B8o8jCjuU3gPgEtcZcnwKzEyQFdX8kx4mg6JeW2MCw4jKH9U4uvzB3b3NgsM     0  HvwRk8tX4ctHoKbnFETtekH9rVgoS3ib923MBRSyWF5M  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB    0.01  So11111111111111111111111111111111111111111  9c47ce3a708aff5b1f8f99286b4c1a4b  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z            0  2021-11-04               2022\n", "1    2021-11-04 22:18:42+00:00  *********  5yPDecdnqQQC8fARJT4uxihPJiG9dv3QgJy3TSnnymhsVsoJ5KjRMcT2imVMaRi1in6t79TC3eK32ByFgQEpk9Su     0  AhFJ17wRz92LuJhmFNJrDXDZe1X9sekVfrUdpcbGZJjm  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB    0.05  So11111111111111111111111111111111111111111  ead66654a054da6dc31f9544e62313d7  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z            1  2021-11-04               2022\n", "2    2021-11-11 14:09:00+00:00  *********  51SbRRZqYHyDn1xann2ZFcGPpGyUSdZEnhh9otrjAkDH6L8yndgT5DRjD9GTjszJKsrnpnTtaVJANCoucGnVM3j4     0  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB  AhFJ17wRz92LuJhmFNJrDXDZe1X9sekVfrUdpcbGZJjm    0.04  So11111111111111111111111111111111111111111  3c7dec807e58c545054dd5013cb67b2e  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z            2  2021-11-11               2022\n", "3    2021-11-26 05:41:17+00:00  108751233  556ehfYErPkf3UGx3dy5pACXo3EYW3wNrnjrQENa8XM4JDCMeDfsWMxbdsyQtfm66nkxmsGebjaCiDcwtdaKHmcp     0  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB  HvwRk8tX4ctHoKbnFETtekH9rVgoS3ib923MBRSyWF5M    0.02  So11111111111111111111111111111111111111111  b9ba2cd34f3dfdaeb241d94782300712  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z            3  2021-11-26               2022\n", "4    2022-01-19 05:41:53+00:00  116897782  2T2cNYtAoLJXDw6KBfzVh4QNC9aUovBNxrBR8A91Shf9DFz1H9WGMdNC5tMzgFhAqk3yJ7TtcgPtvU5hLchBVYLe   2.1  HeJvFsGJFxGRQgpPjfaGXYt2tTkJ6Hd54wg4yFoAEGUV  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB    0.00  So11111111111111111111111111111111111111111  89514f6750052d35bf52d7c4859bd101  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z            4  2022-01-19               2022\n", "...                        ...        ...                                                                                       ...   ...                                           ...                                           ...     ...                                          ...                               ...                       ...                       ...          ...         ...                ...\n", "5674 2025-06-24 15:21:00+00:00  348916532  46td3PQq3yzmEbnWvnEMZbP1j7ty2Lgh4NvVKYhi3YchFMU6GufhjKq3DoYmt6E2b3kRMhMsv45xqzA4KcCTvV7w     5   sain87NazoHou2KH9kHjB5NLejYimf174PX9Cjy7PFW  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB    0.05  So11111111111111111111111111111111111111111  41d61a3b82b9d9ce5fb2c9af71a121d1  2025-06-24T15:56:06.419Z  2025-06-24T15:56:06.419Z         5674  2025-06-24               2025\n", "5675 2025-06-24 15:21:00+00:00  348916532  46td3PQq3yzmEbnWvnEMZbP1j7ty2Lgh4NvVKYhi3YchFMU6GufhjKq3DoYmt6E2b3kRMhMsv45xqzA4KcCTvV7w     6   nuinTBZK45YybFaYypuBuJfYr6cq6v9ZtoRn1hmEr7V  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB    0.01  So11111111111111111111111111111111111111111  31c05c10de18c8df9d46d251025c06de  2025-06-24T15:56:06.419Z  2025-06-24T15:56:06.419Z         5675  2025-06-24               2025\n", "5676 2025-06-24 15:21:00+00:00  348916532  46td3PQq3yzmEbnWvnEMZbP1j7ty2Lgh4NvVKYhi3YchFMU6GufhjKq3DoYmt6E2b3kRMhMsv45xqzA4KcCTvV7w     2   spaLEShbJT3vHnBcQ8eV9oxWDs5Xy5b6cpUUXYXrpZh  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB    0.30  So11111111111111111111111111111111111111111  af947761ecd6affd429ad12b0d7ae7f2  2025-06-24T15:56:06.419Z  2025-06-24T15:56:06.419Z         5676  2025-06-24               2025\n", "5677 2025-06-24 15:21:00+00:00  348916532  46td3PQq3yzmEbnWvnEMZbP1j7ty2Lgh4NvVKYhi3YchFMU6GufhjKq3DoYmt6E2b3kRMhMsv45xqzA4KcCTvV7w     3   spaKwcgPN9N3Ab3eUeYBKyzCrFqM6t6b7137yAQR2cQ  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB    0.26  So11111111111111111111111111111111111111111  ca62b8a8e3b1d4f64eb8d60d95d9352c  2025-06-24T15:56:06.419Z  2025-06-24T15:56:06.419Z         5677  2025-06-24               2025\n", "5678 2025-06-24 15:21:00+00:00  348916532  46td3PQq3yzmEbnWvnEMZbP1j7ty2Lgh4NvVKYhi3YchFMU6GufhjKq3DoYmt6E2b3kRMhMsv45xqzA4KcCTvV7w     4   spad6uPeF5RRxUE2DZExHkihzhoqLQ4oA9u7Mvc6xKL  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB    0.18  So11111111111111111111111111111111111111111  d0b1efbd8a7ba7ce1a70cc7ea5dc4592  2025-06-24T15:56:06.419Z  2025-06-24T15:56:06.419Z         5678  2025-06-24               2025\n", "\n", "[5679 rows x 14 columns]"]}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["wallet = utils.eu_wallet\n", "# Get all transfers for the wallet\n", "wallet_transfers = utils.query_df(f\"\"\"\n", "    SELECT\n", "        *\n", "    FROM solana.core.fact_transfers AS t\n", "    WHERE (t.tx_from = '{wallet}'\n", "        OR t.tx_to   = '{wallet}' )\n", "    ORDER BY block_timestamp\n", "\"\"\")\n", "wallet_transfers\n", "\n", "# Get all swaps for the wallet\n"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["found 184 rows\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>block_timestamp</th>\n", "      <th>block_id</th>\n", "      <th>tx_id</th>\n", "      <th>succeeded</th>\n", "      <th>swapper</th>\n", "      <th>swap_from_amount</th>\n", "      <th>swap_from_mint</th>\n", "      <th>swap_to_amount</th>\n", "      <th>swap_to_mint</th>\n", "      <th>program_id</th>\n", "      <th>swap_program</th>\n", "      <th>_log_id</th>\n", "      <th>fact_swaps_id</th>\n", "      <th>inserted_timestamp</th>\n", "      <th>modified_timestamp</th>\n", "      <th>__row_index</th>\n", "      <th>day</th>\n", "      <th>accounting_period</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2022-01-27 07:41:44+00:00</td>\n", "      <td>*********</td>\n", "      <td>2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm</td>\n", "      <td>True</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>170.00</td>\n", "      <td>So11111111111111111111111111111111111111112</td>\n", "      <td>15,194.49</td>\n", "      <td>EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v</td>\n", "      <td>675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8</td>\n", "      <td>Raydium Liquidity Pool V4</td>\n", "      <td>2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm-1-Raydium Liquidity Pool V4</td>\n", "      <td>bf14181c03cb145d97dfdc88d6662132</td>\n", "      <td>2024-05-30T20:16:41.727Z</td>\n", "      <td>2024-05-30T20:16:41.727Z</td>\n", "      <td>0</td>\n", "      <td>2022-01-27</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2022-01-30 09:27:11+00:00</td>\n", "      <td>*********</td>\n", "      <td>3NjM1utGcAcZzPqt9gEACY9uMrn6dx3n8YpjFT5VUsK2xrXMCekzi4bz1ydAqyonY8w4ENat9DiLsCo383WeHn8u</td>\n", "      <td>True</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>190.00</td>\n", "      <td>So11111111111111111111111111111111111111112</td>\n", "      <td>18,064.35</td>\n", "      <td>EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v</td>\n", "      <td>675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8</td>\n", "      <td>Raydium Liquidity Pool V4</td>\n", "      <td>3NjM1utGcAcZzPqt9gEACY9uMrn6dx3n8YpjFT5VUsK2xrXMCekzi4bz1ydAqyonY8w4ENat9DiLsCo383WeHn8u-1-Raydium Liquidity Pool V4</td>\n", "      <td>d693627c1cec0a786aaba4642014cea4</td>\n", "      <td>2024-05-30T20:16:41.727Z</td>\n", "      <td>2024-05-30T20:16:41.727Z</td>\n", "      <td>1</td>\n", "      <td>2022-01-30</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2022-01-30 09:39:58+00:00</td>\n", "      <td>118516499</td>\n", "      <td>2YPbdLHxuyUuDqESC2C8gsWkwJEFWPnaLfmscT6vGLs9fMN4hvjcJhrjGEzEaoBnJHHRz74YEMXTbdfjNkULYzuM</td>\n", "      <td>True</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>215.00</td>\n", "      <td>So11111111111111111111111111111111111111112</td>\n", "      <td>20,491.36</td>\n", "      <td>EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v</td>\n", "      <td>675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8</td>\n", "      <td>Raydium Liquidity Pool V4</td>\n", "      <td>2YPbdLHxuyUuDqESC2C8gsWkwJEFWPnaLfmscT6vGLs9fMN4hvjcJhrjGEzEaoBnJHHRz74YEMXTbdfjNkULYzuM-1-Raydium Liquidity Pool V4</td>\n", "      <td>62bb74d3a228046fadd42ee72f892068</td>\n", "      <td>2024-05-30T20:16:41.727Z</td>\n", "      <td>2024-05-30T20:16:41.727Z</td>\n", "      <td>2</td>\n", "      <td>2022-01-30</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2022-02-01 10:05:37+00:00</td>\n", "      <td>118825926</td>\n", "      <td>261RiKLSCrGBEQNoZfGPqfJfbxRpHoU4P4QdDcqcUXiQnsULNbK14dGMB8YQQEZoiJwpoubgMe8saniwXmZgRUgz</td>\n", "      <td>True</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>15,000.00</td>\n", "      <td>EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v</td>\n", "      <td>143.12</td>\n", "      <td>So11111111111111111111111111111111111111112</td>\n", "      <td>675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8</td>\n", "      <td>Raydium Liquidity Pool V4</td>\n", "      <td>261RiKLSCrGBEQNoZfGPqfJfbxRpHoU4P4QdDcqcUXiQnsULNbK14dGMB8YQQEZoiJwpoubgMe8saniwXmZgRUgz-1-Raydium Liquidity Pool V4</td>\n", "      <td>426c55ac72cfc5a4ca65d940b1339e0b</td>\n", "      <td>2024-05-30T20:16:41.727Z</td>\n", "      <td>2024-05-30T20:16:41.727Z</td>\n", "      <td>3</td>\n", "      <td>2022-02-01</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2022-02-11 08:38:11+00:00</td>\n", "      <td>120325130</td>\n", "      <td>4QVYsKi811rM3dt5F2BEmJETmXhFDL8oYncCyEqwx3q5v2hS8dQQ7LhARyg4quTxjbobX3Pa2rrjGJWU9T7oMDh9</td>\n", "      <td>True</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>250.00</td>\n", "      <td>So11111111111111111111111111111111111111112</td>\n", "      <td>26,247.81</td>\n", "      <td>EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v</td>\n", "      <td>675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8</td>\n", "      <td>Raydium Liquidity Pool V4</td>\n", "      <td>4QVYsKi811rM3dt5F2BEmJETmXhFDL8oYncCyEqwx3q5v2hS8dQQ7LhARyg4quTxjbobX3Pa2rrjGJWU9T7oMDh9-1-Raydium Liquidity Pool V4</td>\n", "      <td>fc275d9979b7311f245090f0f0c3e6a6</td>\n", "      <td>2024-05-30T20:16:41.727Z</td>\n", "      <td>2024-05-30T20:16:41.727Z</td>\n", "      <td>4</td>\n", "      <td>2022-02-11</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>179</th>\n", "      <td>2025-04-28 10:52:29+00:00</td>\n", "      <td>336445757</td>\n", "      <td>52SoLAwkwYiJho1st4KjLZ8C53VJQh7HZnwePNqbCAcgvy8AJVhxeEzHJo4P2bCZCrMwWJpWy7pSxSMi4Rhxuy5x</td>\n", "      <td>True</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>0.00</td>\n", "      <td>So11111111111111111111111111111111111111112</td>\n", "      <td>0.61</td>\n", "      <td>EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v</td>\n", "      <td>LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo</td>\n", "      <td>meteora dlmm pools program</td>\n", "      <td>52SoLAwkwYiJho1st4KjLZ8C53VJQh7HZnwePNqbCAcgvy8AJVhxeEzHJo4P2bCZCrMwWJpWy7pSxSMi4Rhxuy5x-1-meteora dlmm pools program</td>\n", "      <td>a9a9958d4042dd5236208b1869709445</td>\n", "      <td>2025-04-28T12:11:45.094Z</td>\n", "      <td>2025-04-28T14:05:36.872Z</td>\n", "      <td>179</td>\n", "      <td>2025-04-28</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>180</th>\n", "      <td>2025-05-08 17:09:52+00:00</td>\n", "      <td>338690811</td>\n", "      <td>2HB3tZmaUm727bsshre1Z19q8TCjtSX9LjzQmELKNorfaDKRpXSdmEN9JazDKEgqpD4QqtUArpQtiFs688KmpseB</td>\n", "      <td>True</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>0.50</td>\n", "      <td>So11111111111111111111111111111111111111112</td>\n", "      <td>79.64</td>\n", "      <td>EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v</td>\n", "      <td>LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo</td>\n", "      <td>meteora dlmm pools program</td>\n", "      <td>2HB3tZmaUm727bsshre1Z19q8TCjtSX9LjzQmELKNorfaDKRpXSdmEN9JazDKEgqpD4QqtUArpQtiFs688KmpseB-1-meteora dlmm pools program</td>\n", "      <td>be56525fac368a2ae184f16ab1e11936</td>\n", "      <td>2025-05-08T19:07:05.787Z</td>\n", "      <td>2025-05-08T20:15:58.358Z</td>\n", "      <td>180</td>\n", "      <td>2025-05-08</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>181</th>\n", "      <td>2025-05-11 10:48:16+00:00</td>\n", "      <td>339289930</td>\n", "      <td>3T1t4XT3bgfBomkEmj5EuAd4bYawxbusAmj9sRtdcxiofAACj4gYa8BvwrDxj96S8f1Cuq9tVNsBaU5iCjqaoENs</td>\n", "      <td>True</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>1.00</td>\n", "      <td>So11111111111111111111111111111111111111112</td>\n", "      <td>173.84</td>\n", "      <td>EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v</td>\n", "      <td>LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo</td>\n", "      <td>meteora dlmm pools program</td>\n", "      <td>3T1t4XT3bgfBomkEmj5EuAd4bYawxbusAmj9sRtdcxiofAACj4gYa8BvwrDxj96S8f1Cuq9tVNsBaU5iCjqaoENs-1-meteora dlmm pools program</td>\n", "      <td>2e5e31808111c1e638f06ef4b5675cf0</td>\n", "      <td>2025-05-11T12:18:42.411Z</td>\n", "      <td>2025-05-11T13:05:16.853Z</td>\n", "      <td>181</td>\n", "      <td>2025-05-11</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>182</th>\n", "      <td>2025-06-19 20:58:13+00:00</td>\n", "      <td>347893245</td>\n", "      <td>5XPy2QVMRhgZaPeLkdUAXPC1hMPnZS68G1nvC235ddfiFNfoiUdFoQLfwvMFPuymmeR3G24GXiVgng7saEWVGjPg</td>\n", "      <td>True</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>1.50</td>\n", "      <td>So11111111111111111111111111111111111111112</td>\n", "      <td>218.02</td>\n", "      <td>EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v</td>\n", "      <td>LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo</td>\n", "      <td>meteora dlmm pools program</td>\n", "      <td>5XPy2QVMRhgZaPeLkdUAXPC1hMPnZS68G1nvC235ddfiFNfoiUdFoQLfwvMFPuymmeR3G24GXiVgng7saEWVGjPg-1-meteora dlmm pools program</td>\n", "      <td>4557a63b92c00db8fe73bce496395d8d</td>\n", "      <td>2025-06-19T22:06:33.503Z</td>\n", "      <td>2025-06-19T23:07:40.919Z</td>\n", "      <td>182</td>\n", "      <td>2025-06-19</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>183</th>\n", "      <td>2025-06-19 21:01:15+00:00</td>\n", "      <td>347893698</td>\n", "      <td>Rr59X3mSy8yhC21fWDDXQvr9RWb29ZEUXeUUUedcLWA4QrAtcMFh4phRCAabJC8G31JG1SL4ifxK7a4dLPKx65p</td>\n", "      <td>True</td>\n", "      <td>EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB</td>\n", "      <td>56.70</td>\n", "      <td>EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v</td>\n", "      <td>42,218.09</td>\n", "      <td>G9tt98aYSznRk7jWsfuz9FnTdokxS6Brohdo9hSmjTRB</td>\n", "      <td>675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8</td>\n", "      <td>Raydium Liquidity Pool V4</td>\n", "      <td>Rr59X3mSy8yhC21fWDDXQvr9RWb29ZEUXeUUUedcLWA4QrAtcMFh4phRCAabJC8G31JG1SL4ifxK7a4dLPKx65p-1-Raydium Liquidity Pool V4</td>\n", "      <td>53eeb38c06b7c75261fe681a08257acd</td>\n", "      <td>2025-06-19T22:07:14.622Z</td>\n", "      <td>2025-06-20T00:15:46.774Z</td>\n", "      <td>183</td>\n", "      <td>2025-06-19</td>\n", "      <td>2025</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>184 rows × 18 columns</p>\n", "</div>"], "text/plain": ["              block_timestamp   block_id                                                                                     tx_id  succeeded                                       swapper  swap_from_amount                                swap_from_mint  swap_to_amount                                  swap_to_mint                                    program_id                swap_program                                                                                                                _log_id                     fact_swaps_id        inserted_timestamp        modified_timestamp  __row_index         day  accounting_period\n", "0   2022-01-27 07:41:44+00:00  *********  2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm       True  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB            170.00   So11111111111111111111111111111111111111112       15,194.49  EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v  675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8   Raydium Liquidity Pool V4   2utTRRR9uZB4E5YWejzDVUW4xrZGiQYKkfE5gJyxoWjwWUS3t71hKpbYeeTEoSfT6x8x2fMYLTHbtUNbV165mgUm-1-Raydium Liquidity Pool V4  bf14181c03cb145d97dfdc88d6662132  2024-05-30T20:16:41.727Z  2024-05-30T20:16:41.727Z            0  2022-01-27               2022\n", "1   2022-01-30 09:27:11+00:00  *********  3NjM1utGcAcZzPqt9gEACY9uMrn6dx3n8YpjFT5VUsK2xrXMCekzi4bz1ydAqyonY8w4ENat9DiLsCo383WeHn8u       True  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB            190.00   So11111111111111111111111111111111111111112       18,064.35  EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v  675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8   Raydium Liquidity Pool V4   3NjM1utGcAcZzPqt9gEACY9uMrn6dx3n8YpjFT5VUsK2xrXMCekzi4bz1ydAqyonY8w4ENat9DiLsCo383WeHn8u-1-Raydium Liquidity Pool V4  d693627c1cec0a786aaba4642014cea4  2024-05-30T20:16:41.727Z  2024-05-30T20:16:41.727Z            1  2022-01-30               2022\n", "2   2022-01-30 09:39:58+00:00  118516499  2YPbdLHxuyUuDqESC2C8gsWkwJEFWPnaLfmscT6vGLs9fMN4hvjcJhrjGEzEaoBnJHHRz74YEMXTbdfjNkULYzuM       True  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB            215.00   So11111111111111111111111111111111111111112       20,491.36  EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v  675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8   Raydium Liquidity Pool V4   2YPbdLHxuyUuDqESC2C8gsWkwJEFWPnaLfmscT6vGLs9fMN4hvjcJhrjGEzEaoBnJHHRz74YEMXTbdfjNkULYzuM-1-Raydium Liquidity Pool V4  62bb74d3a228046fadd42ee72f892068  2024-05-30T20:16:41.727Z  2024-05-30T20:16:41.727Z            2  2022-01-30               2022\n", "3   2022-02-01 10:05:37+00:00  118825926  261RiKLSCrGBEQNoZfGPqfJfbxRpHoU4P4QdDcqcUXiQnsULNbK14dGMB8YQQEZoiJwpoubgMe8saniwXmZgRUgz       True  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB         15,000.00  EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v          143.12   So11111111111111111111111111111111111111112  675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8   Raydium Liquidity Pool V4   261RiKLSCrGBEQNoZfGPqfJfbxRpHoU4P4QdDcqcUXiQnsULNbK14dGMB8YQQEZoiJwpoubgMe8saniwXmZgRUgz-1-Raydium Liquidity Pool V4  426c55ac72cfc5a4ca65d940b1339e0b  2024-05-30T20:16:41.727Z  2024-05-30T20:16:41.727Z            3  2022-02-01               2022\n", "4   2022-02-11 08:38:11+00:00  120325130  4QVYsKi811rM3dt5F2BEmJETmXhFDL8oYncCyEqwx3q5v2hS8dQQ7LhARyg4quTxjbobX3Pa2rrjGJWU9T7oMDh9       True  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB            250.00   So11111111111111111111111111111111111111112       26,247.81  EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v  675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8   Raydium Liquidity Pool V4   4QVYsKi811rM3dt5F2BEmJETmXhFDL8oYncCyEqwx3q5v2hS8dQQ7LhARyg4quTxjbobX3Pa2rrjGJWU9T7oMDh9-1-Raydium Liquidity Pool V4  fc275d9979b7311f245090f0f0c3e6a6  2024-05-30T20:16:41.727Z  2024-05-30T20:16:41.727Z            4  2022-02-11               2022\n", "..                        ...        ...                                                                                       ...        ...                                           ...               ...                                           ...             ...                                           ...                                           ...                         ...                                                                                                                    ...                               ...                       ...                       ...          ...         ...                ...\n", "179 2025-04-28 10:52:29+00:00  336445757  52SoLAwkwYiJho1st4KjLZ8C53VJQh7HZnwePNqbCAcgvy8AJVhxeEzHJo4P2bCZCrMwWJpWy7pSxSMi4Rhxuy5x       True  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB              0.00   So11111111111111111111111111111111111111112            0.61  EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v   LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo  meteora dlmm pools program  52SoLAwkwYiJho1st4KjLZ8C53VJQh7HZnwePNqbCAcgvy8AJVhxeEzHJo4P2bCZCrMwWJpWy7pSxSMi4Rhxuy5x-1-meteora dlmm pools program  a9a9958d4042dd5236208b1869709445  2025-04-28T12:11:45.094Z  2025-04-28T14:05:36.872Z          179  2025-04-28               2025\n", "180 2025-05-08 17:09:52+00:00  338690811  2HB3tZmaUm727bsshre1Z19q8TCjtSX9LjzQmELKNorfaDKRpXSdmEN9JazDKEgqpD4QqtUArpQtiFs688KmpseB       True  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB              0.50   So11111111111111111111111111111111111111112           79.64  EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v   LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo  meteora dlmm pools program  2HB3tZmaUm727bsshre1Z19q8TCjtSX9LjzQmELKNorfaDKRpXSdmEN9JazDKEgqpD4QqtUArpQtiFs688KmpseB-1-meteora dlmm pools program  be56525fac368a2ae184f16ab1e11936  2025-05-08T19:07:05.787Z  2025-05-08T20:15:58.358Z          180  2025-05-08               2025\n", "181 2025-05-11 10:48:16+00:00  339289930  3T1t4XT3bgfBomkEmj5EuAd4bYawxbusAmj9sRtdcxiofAACj4gYa8BvwrDxj96S8f1Cuq9tVNsBaU5iCjqaoENs       True  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB              1.00   So11111111111111111111111111111111111111112          173.84  EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v   LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo  meteora dlmm pools program  3T1t4XT3bgfBomkEmj5EuAd4bYawxbusAmj9sRtdcxiofAACj4gYa8BvwrDxj96S8f1Cuq9tVNsBaU5iCjqaoENs-1-meteora dlmm pools program  2e5e31808111c1e638f06ef4b5675cf0  2025-05-11T12:18:42.411Z  2025-05-11T13:05:16.853Z          181  2025-05-11               2025\n", "182 2025-06-19 20:58:13+00:00  347893245  5XPy2QVMRhgZaPeLkdUAXPC1hMPnZS68G1nvC235ddfiFNfoiUdFoQLfwvMFPuymmeR3G24GXiVgng7saEWVGjPg       True  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB              1.50   So11111111111111111111111111111111111111112          218.02  EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v   LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo  meteora dlmm pools program  5XPy2QVMRhgZaPeLkdUAXPC1hMPnZS68G1nvC235ddfiFNfoiUdFoQLfwvMFPuymmeR3G24GXiVgng7saEWVGjPg-1-meteora dlmm pools program  4557a63b92c00db8fe73bce496395d8d  2025-06-19T22:06:33.503Z  2025-06-19T23:07:40.919Z          182  2025-06-19               2025\n", "183 2025-06-19 21:01:15+00:00  347893698   Rr59X3mSy8yhC21fWDDXQvr9RWb29ZEUXeUUUedcLWA4QrAtcMFh4phRCAabJC8G31JG1SL4ifxK7a4dLPKx65p       True  EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB             56.70  EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v       42,218.09  G9tt98aYSznRk7jWsfuz9FnTdokxS6Brohdo9hSmjTRB  675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8   Raydium Liquidity Pool V4    Rr59X3mSy8yhC21fWDDXQvr9RWb29ZEUXeUUUedcLWA4QrAtcMFh4phRCAabJC8G31JG1SL4ifxK7a4dLPKx65p-1-Raydium Liquidity Pool V4  53eeb38c06b7c75261fe681a08257acd  2025-06-19T22:07:14.622Z  2025-06-20T00:15:46.774Z          183  2025-06-19               2025\n", "\n", "[184 rows x 18 columns]"]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}], "source": ["wallet_swaps = utils.query_df(f\"\"\"\n", "      SELECT\n", "        *\n", "      FROM solana.defi.fact_swaps\n", "      WHERE swapper = '{wallet}'\n", "    ORDER BY block_timestamp\n", "\"\"\")\n", "wallet_swaps"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["buybacks.to_csv(\"sac_buybacks.csv\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["found 410 rows\n", "Floor sweeps sum: 375190.*********\n"]}, {"data": {"text/plain": ["Index(['block_timestamp', 'accounting_period', 'tx_id', 'by_sac',\n", "       'buyer_address', 'price_usd', 'amount_usd', 'marketplace',\n", "       'marketplace_version', 'block_id', 'succeeded', 'index', 'inner_index',\n", "       'program_id', 'seller_address', 'mint', 'nft_name', 'price',\n", "       'currency_address', 'currency_symbol', 'tree_authority', 'merkle_tree',\n", "       'leaf_index', 'is_compressed', 'nft_collection_name', 'collection_id',\n", "       'creators', 'authority', 'metadata', 'image_url', 'metadata_uri',\n", "       'ez_nft_sales_id', 'inserted_timestamp', 'modified_timestamp',\n", "       '__row_index', 'day', 'sac_pubkey', 'sac_name', 'sac_category',\n", "       'sac_tag', 'sac_Spalte 2', 'sac_Spalte 3', 'sac_Spalte 4', 'sac_email',\n", "       'sac_phone'],\n", "      dtype='object')"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["collections = \"', '\".join(utils.collections)\n", "wallet_list = \"', '\".join(company_wallets[\"sac_pubkey\"].to_list())\n", "        \n", "floor_sweeps = utils.query_df(f\"\"\"\n", "    SELECT\n", "        price_usd as amount_usd,\n", "        *\n", "    FROM\n", "        solana.nft.ez_nft_sales\n", "    WHERE\n", "        collection_id in ('{collections}')\n", "        and buyer_address in ('{wallet_list}')\n", "    ORDER BY\n", "        block_timestamp;\"\"\")\n", "\n", "floor_sweeps = utils.merge_company_wallets(floor_sweeps, company_wallets, \"buyer_address\")\n", "floor_sweeps = utils.move_columns_to_front(floor_sweeps, [\"block_timestamp\", \"accounting_period\", \"tx_id\", \"by_sac\", \"buyer_address\", \"price_usd\"])\n", "print(f\"Floor sweeps sum: {floor_sweeps['amount_usd'].sum()}\")\n", "floor_sweeps.columns"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/0z/9qx916gs2qj55k2s6t7rm6ch0000gn/T/ipykernel_2568/*********.py:7: UserWarning: Converting to PeriodArray/Index representation will drop timezone information.\n", "  grouped_data = df_filtered.groupby(df_filtered[datetime_column].dt.to_period(period))[amount_column].sum()\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Total price_usd: 375118.125419226\n"]}], "source": ["import matplotlib.pyplot as plt\n", "\n", "def plot_amount_by_period(df, amount_column, datetime_column='block_timestamp', period='M', title=None):\n", "    # Filter data to end of 2022\n", "    df_filtered = df[df[datetime_column] <= '2022-12-31']\n", "    \n", "    grouped_data = df_filtered.groupby(df_filtered[datetime_column].dt.to_period(period))[amount_column].sum()\n", "    \n", "    # Create the plot\n", "    plt.figure(figsize=(12, 6))\n", "    plt.plot(grouped_data.index.astype(str), grouped_data.values, marker='o')\n", "    \n", "    # Set title\n", "    if title is None:\n", "        title = f'{amount_column.title()} Sum by {period.upper()}'\n", "    plt.title(title)\n", "    \n", "    plt.xlabel('Period')\n", "    plt.ylabel(f'{amount_column.title()} Sum')\n", "    plt.xticks(rotation=45)\n", "    plt.grid(True, alpha=0.3)\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Also display the total sum\n", "    print(f\"Total {amount_column}: {df[amount_column].sum()}\")\n", "\n", "# Use the function to plot floor sweeps\n", "plot_amount_by_period(floor_sweeps, 'price_usd', title='Floor Sweeps Price USD Sum by Month')"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Combined transactions shape: (892, 58)\n", "Total amount USD: 498825.635419226\n"]}, {"data": {"text/plain": ["0        NaN\n", "1        NaN\n", "2        NaN\n", "3        NaN\n", "4        NaN\n", "       ...  \n", "887    35.78\n", "888    34.76\n", "889    44.56\n", "890   198.25\n", "891    41.79\n", "Name: amount_usd, Length: 892, dtype: float64"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["# Combine floor_sweeps and buybacks into one dataframe\n", "# First, let's standardize the column names and add a type identifier\n", "\n", "# Add type identifier to each dataframe\n", "all_transactions = pd.concat([\n", "    floor_sweeps.assign(tx_type='floor_sweep'),\n", "    buybacks.assign(tx_type='buyback')\n", "], ignore_index=True)\n", "\n", "# Sort by timestamp\n", "all_transactions = all_transactions.sort_values('block_timestamp').reset_index(drop=True)\n", "\n", "print(f\"Combined transactions shape: {all_transactions.shape}\")\n", "print(f\"Total amount USD: {all_transactions['amount_usd'].sum()}\")\n", "\n", "all_transactions[\"amount_usd\"]\n"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/0z/9qx916gs2qj55k2s6t7rm6ch0000gn/T/ipykernel_2568/*********.py:7: UserWarning: Converting to PeriodArray/Index representation will drop timezone information.\n", "  grouped_data = df_filtered.groupby(df_filtered[datetime_column].dt.to_period(period))[amount_column].sum()\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Total amount_usd: 498825.635419226\n"]}], "source": ["plot_amount_by_period(all_transactions, 'amount_usd', title='Floor Sweeps Price USD Sum by Month')"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>transfer_to</th>\n", "      <th>transfer_from</th>\n", "      <th>transfer_mint</th>\n", "      <th>transfer_amount</th>\n", "      <th>transfer_index</th>\n", "      <th>block_timestamp_hour</th>\n", "      <th>transfer_usd</th>\n", "      <th>marketplace</th>\n", "      <th>marketplace_version</th>\n", "      <th>block_timestamp</th>\n", "      <th>...</th>\n", "      <th>metadata</th>\n", "      <th>image_url</th>\n", "      <th>metadata_uri</th>\n", "      <th>ez_nft_sales_id</th>\n", "      <th>inserted_timestamp</th>\n", "      <th>modified_timestamp</th>\n", "      <th>__row_index</th>\n", "      <th>day</th>\n", "      <th>accounting_period</th>\n", "      <th>amount_usd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>A9gRAYuYF3fzUd91N948vZZyd7Rf5nvEGKhutAz4vgVf</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>0.11</td>\n", "      <td>0.20</td>\n", "      <td>2021-11-28T20:00:00.000Z</td>\n", "      <td>20.63</td>\n", "      <td>magic eden v1</td>\n", "      <td>v1</td>\n", "      <td>2021-11-28 20:29:04+00:00</td>\n", "      <td>...</td>\n", "      <td>[{'trait_type': 'Role', 'value': 'Farmer'}, {'trait_type': 'Background', 'value': 'Blue'}, {'trait_type': 'Eyes', 'value': 'Angry'}, {'trait_type': 'Form', 'value': 'White'}, {'trait_type': 'Hats', 'value': 'Brown Flat Cap'}, {'trait_type': 'Mouth', 'value': 'Smoking Joint'}, {'trait_type': 'Hair', 'value': 'Smooth Long White'}, {'trait_type': 'Hand with Items', 'value': 'Brownie White Fur'}, {'trait_type': 'Random Things', 'value': 'Cannabis Plant'}, {'trait_type': 'Upper Part', 'value': 'Brown Farmer Overall'}, {'trait_type': 'Genesis Role?', 'value': 'Yes'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]</td>\n", "      <td>https://arweave.net/yUoHYmtBw9e5-q_-RBV4UINm-VHdNZtChkDZDfcsQ7E?ext=webp</td>\n", "      <td>https://arweave.net/qdL8zHFwEtv_AA1cOMMojDfWlwyhpG1-K9-LoyxbfzM</td>\n", "      <td>57582d85662a0b7b78d55b44863eeae0</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>0</td>\n", "      <td>2021-11-28</td>\n", "      <td>2022</td>\n", "      <td>20.63</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>A9gRAYuYF3fzUd91N948vZZyd7Rf5nvEGKhutAz4vgVf</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>0.09</td>\n", "      <td>0.20</td>\n", "      <td>2021-11-28T20:00:00.000Z</td>\n", "      <td>16.51</td>\n", "      <td>magic eden v1</td>\n", "      <td>v1</td>\n", "      <td>2021-11-28 20:29:48+00:00</td>\n", "      <td>...</td>\n", "      <td>[{'trait_type': 'Role', 'value': 'Businessman'}, {'trait_type': 'Background', 'value': 'Blue Gradient'}, {'trait_type': 'Ears', 'value': 'Double Piercing'}, {'trait_type': 'Facetats', 'value': 'Tiny SAC'}, {'trait_type': 'Eyes', 'value': 'Red'}, {'trait_type': 'Eyes Items', 'value': 'Red John <PERSON> Glasses'}, {'trait_type': 'Form', 'value': 'Light Blonde'}, {'trait_type': 'Mouth', 'value': 'Grinding'}, {'trait_type': 'Hand with Items', 'value': 'Business Lighter Light Blonde Fur'}, {'trait_type': 'Upper Part', 'value': 'Brown Business Suit With Bow Tie &amp; Hat'}, {'trait_type': 'Genesis Role?', 'value': 'Yes'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]</td>\n", "      <td>https://arweave.net/2xkvo0nDtD8-myprcq4WxdQiAhQVgzrzlGORV-6vybw?ext=webp</td>\n", "      <td>https://arweave.net/UW5zRS9V_AfHY4S4yl6GBkLBoog8m9c_NijsxPXQJV8</td>\n", "      <td>85e9a3ce561bc59144ccdc2693a188c4</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>1</td>\n", "      <td>2021-11-28</td>\n", "      <td>2022</td>\n", "      <td>16.51</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>HevD7LwzN2GFEqbLxziwVFWVS1VGN4hLopCzbVFE9gDW</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>0.07</td>\n", "      <td>0.20</td>\n", "      <td>2021-11-28T20:00:00.000Z</td>\n", "      <td>12.38</td>\n", "      <td>magic eden v1</td>\n", "      <td>v1</td>\n", "      <td>2021-11-28 20:29:50+00:00</td>\n", "      <td>...</td>\n", "      <td>[{'trait_type': 'Role', 'value': 'Businessman'}, {'trait_type': 'Background', 'value': 'Hippie 3'}, {'trait_type': 'Companions', 'value': 'Blue Parrot'}, {'trait_type': 'Ears', 'value': 'Golden Earring'}, {'trait_type': 'Eyes', 'value': 'Red'}, {'trait_type': 'Eyes Items', 'value': 'Gold Monocle'}, {'trait_type': 'Form', 'value': 'White'}, {'trait_type': 'Mouth', 'value': 'Open'}, {'trait_type': 'Hair', 'value': 'Backstreetboy Style White'}, {'trait_type': 'Hand with Items', 'value': 'Joint White Fur'}, {'trait_type': 'Random Things', 'value': 'Questionmark'}, {'trait_type': 'Upper Part', 'value': 'Just Hit It Hoodie'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]</td>\n", "      <td>https://arweave.net/gA0nwFqylzdosOGIkwvkJAW8ZXA_wlcW5ouHPD_pXec?ext=webp</td>\n", "      <td>https://arweave.net/gREYlKDwMtHcHQd3dPAgPKD4t-9B_Ug6m7qYWxuNMsI</td>\n", "      <td>d2733e23db79f4046e8e2baf5940f9b0</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2</td>\n", "      <td>2021-11-28</td>\n", "      <td>2022</td>\n", "      <td>12.38</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>Byhp5fvBUteNZZLMdFYTbix6Kv89Xi7KEys7xruqrquu</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>0.07</td>\n", "      <td>0.20</td>\n", "      <td>2021-11-28T20:00:00.000Z</td>\n", "      <td>13.76</td>\n", "      <td>magic eden v1</td>\n", "      <td>v1</td>\n", "      <td>2021-11-28 20:29:58+00:00</td>\n", "      <td>...</td>\n", "      <td>[{'trait_type': 'Role', 'value': 'Farmer'}, {'trait_type': 'Background', 'value': '<PERSON>b<PERSON> Gradient'}, {'trait_type': 'Eyes', 'value': 'Angry'}, {'trait_type': 'Eyes Items', 'value': 'Flashing Eyes'}, {'trait_type': 'Form', 'value': 'Light Blonde'}, {'trait_type': 'Hats', 'value': 'Pothat'}, {'trait_type': 'Mouth', 'value': 'Puffin'}, {'trait_type': 'Hair', 'value': 'Long Wavy Light Blonde'}, {'trait_type': 'Hand with Items', 'value': 'Watering Can Light Blonde Fur'}, {'trait_type': 'Upper Part', 'value': 'Brown Hippie Top'}, {'trait_type': 'Genesis Role?', 'value': 'Yes'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]</td>\n", "      <td>https://arweave.net/ZgeavQqfLnvk7tYhd3keCpMAZXTLbNZaCZdzRpq-g2E?ext=webp</td>\n", "      <td>https://arweave.net/RpZ3kbheRn2EPLNVHNMSdW4o_n8kgryv3Dw1O1j_CQM</td>\n", "      <td>b8a239bec0cbb24fe59d4305d6c5cb86</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>3</td>\n", "      <td>2021-11-28</td>\n", "      <td>2022</td>\n", "      <td>13.76</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>8V98j4xGca9CDC4H8uYiTyvZEuYkSjA4VLX9mytYPHc2</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>0.06</td>\n", "      <td>0.20</td>\n", "      <td>2021-11-28T20:00:00.000Z</td>\n", "      <td>11.69</td>\n", "      <td>magic eden v1</td>\n", "      <td>v1</td>\n", "      <td>2021-11-28 20:30:28+00:00</td>\n", "      <td>...</td>\n", "      <td>[{'trait_type': 'Role', 'value': 'Artist'}, {'trait_type': 'Background', 'value': 'Blue'}, {'trait_type': 'Ears', 'value': 'Double Piercing'}, {'trait_type': 'Eyes', 'value': 'Red'}, {'trait_type': 'Eyes Items', 'value': 'Red John <PERSON>'}, {'trait_type': 'Form', 'value': 'White'}, {'trait_type': 'Mouth', 'value': 'Grinding'}, {'trait_type': 'Hair', 'value': 'Yellow Beanie White Fur'}, {'trait_type': 'Hand with Items', 'value': 'Brownie &amp; Joint White Fur'}, {'trait_type': 'Upper Part', 'value': 'Green Poloshirt'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]</td>\n", "      <td>https://arweave.net/fzYog-MyUHni083HllvMngVySxy0DMkUXAMtv8mipTQ?ext=webp</td>\n", "      <td>https://arweave.net/Y8r6vQR4CsDeRAPR2X4zhDQv0l6UG6OCGLUj3lYWawY</td>\n", "      <td>a172ab4dc669fc698f83761f05aded10</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>4</td>\n", "      <td>2021-11-28</td>\n", "      <td>2022</td>\n", "      <td>11.69</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35019</th>\n", "      <td>nustrwYoBqryuctcdV7H4R8suJLER7UFfZHZWvAtrvd</td>\n", "      <td>7QEr5uj51wD3xSemtQWsjWMzAxBwhBMxGgjq69qyuHwX</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>0.00</td>\n", "      <td>2.16</td>\n", "      <td>2025-06-17T15:00:00.000Z</td>\n", "      <td>0.44</td>\n", "      <td>magic eden v2</td>\n", "      <td>v2</td>\n", "      <td>2025-06-17 15:29:39+00:00</td>\n", "      <td>...</td>\n", "      <td>[{'trait_type': 'Species', 'value': 'Skellies'}, {'trait_type': 'Body', 'value': 'Skellie Body Space'}, {'trait_type': 'Background', 'value': 'Wasted Land Bright'}, {'trait_type': 'Head', 'value': 'DMT Green'}, {'trait_type': 'Face', 'value': 'Basic Face'}, {'trait_type': 'Chest', 'value': 'Basic Back'}, {'trait_type': 'Mouth', 'value': 'Mouth Closed J'}, {'trait_type': 'Eyes', 'value': 'White'}, {'trait_type': 'Brain', 'value': 'Brain Yellow'}, {'trait_type': 'Upper Part', 'value': 'Shirt <PERSON>'}, {'trait_type': 'Ear Item', 'value': 'Golden Piercing'}, {'trait_type': 'Back Items', 'value': 'Rescue Gun Yellow'}, {'trait_type': 'Rarity Rank', 'value': 'Common'}, {'trait_type': 'Type', 'value': 'Awakened'}, {'trait_type': 'Animated', 'value': 'Yes'}, {'trait_type': 'Awakened', 'value': 'Yes'}]</td>\n", "      <td>https://arweave.net/VeFY0oik6w8QUJK7oDpJMSn1tu-QS5xkuqZXn1LJYQA?ext=webp</td>\n", "      <td>https://arweave.net/Q3tm-MaTs_OMueOR5Jh3HxsGmsJaju4JPSVJCyF4qVE</td>\n", "      <td>1e2b78d613d6a8e83ac63ffc9239a2aa</td>\n", "      <td>2025-06-17T20:15:28.531Z</td>\n", "      <td>2025-06-17T20:15:28.531Z</td>\n", "      <td>35019</td>\n", "      <td>2025-06-17</td>\n", "      <td>2025</td>\n", "      <td>0.44</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35020</th>\n", "      <td>nustrwYoBqryuctcdV7H4R8suJLER7UFfZHZWvAtrvd</td>\n", "      <td>H4YLjZpL9TTrPqYaWJ59H1kwxRiKMvVMxQvDcKV1oNBF</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>0.00</td>\n", "      <td>4.14</td>\n", "      <td>2025-06-17T16:00:00.000Z</td>\n", "      <td>0.52</td>\n", "      <td>magic eden v2</td>\n", "      <td>v2</td>\n", "      <td>2025-06-17 16:22:22+00:00</td>\n", "      <td>...</td>\n", "      <td>[{'trait_type': 'Background', 'value': 'West World Light'}, {'trait_type': 'Species', 'value': 'Meltops'}, {'trait_type': 'Body', 'value': 'Pink Melting'}, {'trait_type': 'Head', 'value': 'Pink Melting'}, {'trait_type': 'Face', 'value': 'Basic Face'}, {'trait_type': 'Chest', 'value': 'DMT Back'}, {'trait_type': 'Mouth', 'value': 'Sol Beer Hat'}, {'trait_type': 'Eyes', 'value': 'Normal'}, {'trait_type': 'Rings', 'value': 'Championship Ring'}, {'trait_type': 'Eyes Items', 'value': 'Thug Glasses'}, {'trait_type': 'Chains', 'value': 'Nuked Chain Golden'}, {'trait_type': 'Ear Item', 'value': 'Golden Piercing'}, {'trait_type': 'Upper Part', 'value': '<PERSON>rt <PERSON>ana'}, {'trait_type': 'Hand', 'value': 'Skeleton Hand Nuked Brownie'}, {'trait_type': 'Back Items', 'value': 'Rescue Gun Yellow'}, {'trait_type': 'Rarity Rank', 'value': 'Common'}]</td>\n", "      <td>https://www.arweave.net/CuDBnMtwfRlsR5pUoOduPHtpqLYKTJwd1xcTTSNOZ9Y?ext=png</td>\n", "      <td>https://arweave.net/CI0EGEHjou8qDW96YwLpty5ZAFB0mMPTGMxzM98vaVA</td>\n", "      <td>4b96b068bd352d8cbbde9cda03fcd6a8</td>\n", "      <td>2025-06-17T20:15:41.283Z</td>\n", "      <td>2025-06-17T20:15:41.283Z</td>\n", "      <td>35020</td>\n", "      <td>2025-06-17</td>\n", "      <td>2025</td>\n", "      <td>0.52</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35021</th>\n", "      <td>nuinTBZK45YybFaYypuBuJfYr6cq6v9ZtoRn1hmEr7V</td>\n", "      <td>H4YLjZpL9TTrPqYaWJ59H1kwxRiKMvVMxQvDcKV1oNBF</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>0.01</td>\n", "      <td>4.13</td>\n", "      <td>2025-06-17T16:00:00.000Z</td>\n", "      <td>1.21</td>\n", "      <td>magic eden v2</td>\n", "      <td>v2</td>\n", "      <td>2025-06-17 16:22:22+00:00</td>\n", "      <td>...</td>\n", "      <td>[{'trait_type': 'Background', 'value': 'West World Light'}, {'trait_type': 'Species', 'value': 'Meltops'}, {'trait_type': 'Body', 'value': 'Pink Melting'}, {'trait_type': 'Head', 'value': 'Pink Melting'}, {'trait_type': 'Face', 'value': 'Basic Face'}, {'trait_type': 'Chest', 'value': 'DMT Back'}, {'trait_type': 'Mouth', 'value': 'Sol Beer Hat'}, {'trait_type': 'Eyes', 'value': 'Normal'}, {'trait_type': 'Rings', 'value': 'Championship Ring'}, {'trait_type': 'Eyes Items', 'value': 'Thug Glasses'}, {'trait_type': 'Chains', 'value': 'Nuked Chain Golden'}, {'trait_type': 'Ear Item', 'value': 'Golden Piercing'}, {'trait_type': 'Upper Part', 'value': '<PERSON>rt <PERSON>ana'}, {'trait_type': 'Hand', 'value': 'Skeleton Hand Nuked Brownie'}, {'trait_type': 'Back Items', 'value': 'Rescue Gun Yellow'}, {'trait_type': 'Rarity Rank', 'value': 'Common'}]</td>\n", "      <td>https://www.arweave.net/CuDBnMtwfRlsR5pUoOduPHtpqLYKTJwd1xcTTSNOZ9Y?ext=png</td>\n", "      <td>https://arweave.net/CI0EGEHjou8qDW96YwLpty5ZAFB0mMPTGMxzM98vaVA</td>\n", "      <td>4b96b068bd352d8cbbde9cda03fcd6a8</td>\n", "      <td>2025-06-17T20:15:41.283Z</td>\n", "      <td>2025-06-17T20:15:41.283Z</td>\n", "      <td>35021</td>\n", "      <td>2025-06-17</td>\n", "      <td>2025</td>\n", "      <td>1.21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35022</th>\n", "      <td>nuinTBZK45YybFaYypuBuJfYr6cq6v9ZtoRn1hmEr7V</td>\n", "      <td>A1qumR55jqcNfY6mcNxmpA9W82nWFFvzK29cdKb6fG8K</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>0.01</td>\n", "      <td>2.17</td>\n", "      <td>2025-06-18T08:00:00.000Z</td>\n", "      <td>1.12</td>\n", "      <td>magic eden v2</td>\n", "      <td>v2</td>\n", "      <td>2025-06-18 08:00:34+00:00</td>\n", "      <td>...</td>\n", "      <td>[{'trait_type': 'Species', 'value': 'Lucias'}, {'trait_type': 'Body', 'value': 'Basic'}, {'trait_type': 'Head', 'value': 'Basic'}, {'trait_type': 'Background', 'value': 'Wasted Land Dark'}, {'trait_type': 'Face', 'value': 'Basic Face'}, {'trait_type': 'Chest', 'value': 'Basic Back'}, {'trait_type': 'Mouth', 'value': 'Money Joint'}, {'trait_type': 'Eyes', 'value': 'White Out'}, {'trait_type': 'Brain', 'value': 'Brain Yellow'}, {'trait_type': 'Chains', 'value': 'Skull Chain'}, {'trait_type': 'On Face Parts', 'value': 'Meat Face Right'}, {'trait_type': 'Upper Part', 'value': 'Shirt <PERSON>ana'}, {'trait_type': 'Hair', 'value': 'Hippie Leo'}, {'trait_type': 'Light', 'value': 'Nuked Nimbus'}, {'trait_type': 'Back Items', 'value': 'Jetpack Gold'}, {'trait_type': 'Rarity Rank', 'value': 'Common'}, {'trait_type': 'Type', 'value': 'Awakened'}, {'trait_type': 'Animated', 'value': 'Yes'}, {'trait_type': 'Awakened', 'value': 'Yes'}]</td>\n", "      <td>https://arweave.net/b0CD-wGRb3gp17ls9AXkVA9dAQ7x_bd4X-0_DdSfxqY?ext=webp</td>\n", "      <td>https://arweave.net/JEb1AtGri3-F6ci2mxGmpMv5UVtkp86IV5K007gQEyQ</td>\n", "      <td>018edc7ac9d1ec9c672ecb1dbeae956b</td>\n", "      <td>2025-06-18T12:11:50.159Z</td>\n", "      <td>2025-06-18T12:11:50.159Z</td>\n", "      <td>35022</td>\n", "      <td>2025-06-18</td>\n", "      <td>2025</td>\n", "      <td>1.12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35023</th>\n", "      <td>nustrwYoBqryuctcdV7H4R8suJLER7UFfZHZWvAtrvd</td>\n", "      <td>A1qumR55jqcNfY6mcNxmpA9W82nWFFvzK29cdKb6fG8K</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>0.00</td>\n", "      <td>2.18</td>\n", "      <td>2025-06-18T08:00:00.000Z</td>\n", "      <td>0.48</td>\n", "      <td>magic eden v2</td>\n", "      <td>v2</td>\n", "      <td>2025-06-18 08:00:34+00:00</td>\n", "      <td>...</td>\n", "      <td>[{'trait_type': 'Species', 'value': 'Lucias'}, {'trait_type': 'Body', 'value': 'Basic'}, {'trait_type': 'Head', 'value': 'Basic'}, {'trait_type': 'Background', 'value': 'Wasted Land Dark'}, {'trait_type': 'Face', 'value': 'Basic Face'}, {'trait_type': 'Chest', 'value': 'Basic Back'}, {'trait_type': 'Mouth', 'value': 'Money Joint'}, {'trait_type': 'Eyes', 'value': 'White Out'}, {'trait_type': 'Brain', 'value': 'Brain Yellow'}, {'trait_type': 'Chains', 'value': 'Skull Chain'}, {'trait_type': 'On Face Parts', 'value': 'Meat Face Right'}, {'trait_type': 'Upper Part', 'value': 'Shirt <PERSON>ana'}, {'trait_type': 'Hair', 'value': 'Hippie Leo'}, {'trait_type': 'Light', 'value': 'Nuked Nimbus'}, {'trait_type': 'Back Items', 'value': 'Jetpack Gold'}, {'trait_type': 'Rarity Rank', 'value': 'Common'}, {'trait_type': 'Type', 'value': 'Awakened'}, {'trait_type': 'Animated', 'value': 'Yes'}, {'trait_type': 'Awakened', 'value': 'Yes'}]</td>\n", "      <td>https://arweave.net/b0CD-wGRb3gp17ls9AXkVA9dAQ7x_bd4X-0_DdSfxqY?ext=webp</td>\n", "      <td>https://arweave.net/JEb1AtGri3-F6ci2mxGmpMv5UVtkp86IV5K007gQEyQ</td>\n", "      <td>018edc7ac9d1ec9c672ecb1dbeae956b</td>\n", "      <td>2025-06-18T12:11:50.159Z</td>\n", "      <td>2025-06-18T12:11:50.159Z</td>\n", "      <td>35023</td>\n", "      <td>2025-06-18</td>\n", "      <td>2025</td>\n", "      <td>0.48</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>35024 rows × 42 columns</p>\n", "</div>"], "text/plain": ["                                       transfer_to                                 transfer_from                                transfer_mint  transfer_amount  transfer_index      block_timestamp_hour  transfer_usd    marketplace marketplace_version            block_timestamp  ...                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            metadata                                                                    image_url                                                     metadata_uri                   ez_nft_sales_id        inserted_timestamp        modified_timestamp __row_index         day accounting_period amount_usd\n", "0      PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb  A9gRAYuYF3fzUd91N948vZZyd7Rf5nvEGKhutAz4vgVf  So11111111111111111111111111111111111111111             0.11            0.20  2021-11-28T20:00:00.000Z         20.63  magic eden v1                  v1  2021-11-28 20:29:04+00:00  ...                                                                                                                                                                                                                                                                                     [{'trait_type': 'Role', 'value': 'Farmer'}, {'trait_type': 'Background', 'value': 'Blue'}, {'trait_type': 'Eyes', 'value': 'Angry'}, {'trait_type': 'Form', 'value': 'White'}, {'trait_type': 'Hats', 'value': 'Brown Flat Cap'}, {'trait_type': 'Mouth', 'value': 'Smoking Joint'}, {'trait_type': 'Hair', 'value': 'Smooth Long White'}, {'trait_type': 'Hand with Items', 'value': '<PERSON>ie White Fur'}, {'trait_type': 'Random Things', 'value': 'Cannabis Plant'}, {'trait_type': 'Upper Part', 'value': 'Brown Farmer Overall'}, {'trait_type': 'Genesis Role?', 'value': 'Yes'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]     https://arweave.net/yUoHYmtBw9e5-q_-RBV4UINm-VHdNZtChkDZDfcsQ7E?ext=webp  https://arweave.net/qdL8zHFwEtv_AA1cOMMojDfWlwyhpG1-K9-LoyxbfzM  57582d85662a0b7b78d55b44863eeae0  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z           0  2021-11-28              2022      20.63\n", "1      PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb  A9gRAYuYF3fzUd91N948vZZyd7Rf5nvEGKhutAz4vgVf  So11111111111111111111111111111111111111111             0.09            0.20  2021-11-28T20:00:00.000Z         16.51  magic eden v1                  v1  2021-11-28 20:29:48+00:00  ...                                                                                                                                                                                                                                   [{'trait_type': 'Role', 'value': 'Businessman'}, {'trait_type': 'Background', 'value': 'Blue Gradient'}, {'trait_type': 'Ears', 'value': 'Double Piercing'}, {'trait_type': 'Facetats', 'value': 'Tiny SAC'}, {'trait_type': 'Eyes', 'value': 'Red'}, {'trait_type': 'Eyes Items', 'value': 'Red John Lennon Glasses'}, {'trait_type': 'Form', 'value': 'Light Blonde'}, {'trait_type': 'Mouth', 'value': 'Grinding'}, {'trait_type': 'Hand with Items', 'value': 'Business Lighter Light Blonde Fur'}, {'trait_type': 'Upper Part', 'value': 'Brown Business Suit With Bow Tie & Hat'}, {'trait_type': 'Genesis Role?', 'value': 'Yes'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]     https://arweave.net/2xkvo0nDtD8-myprcq4WxdQiAhQVgzrzlGORV-6vybw?ext=webp  https://arweave.net/UW5zRS9V_AfHY4S4yl6GBkLBoog8m9c_NijsxPXQJV8  85e9a3ce561bc59144ccdc2693a188c4  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z           1  2021-11-28              2022      16.51\n", "2      PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb  HevD7LwzN2GFEqbLxziwVFWVS1VGN4hLopCzbVFE9gDW  So11111111111111111111111111111111111111111             0.07            0.20  2021-11-28T20:00:00.000Z         12.38  magic eden v1                  v1  2021-11-28 20:29:50+00:00  ...                                                                                                                                                                                                                         [{'trait_type': 'Role', 'value': 'Businessman'}, {'trait_type': 'Background', 'value': 'Hippie 3'}, {'trait_type': 'Companions', 'value': 'Blue Parrot'}, {'trait_type': 'Ears', 'value': 'Golden Earring'}, {'trait_type': 'Eyes', 'value': 'Red'}, {'trait_type': 'Eyes Items', 'value': 'Gold Monocle'}, {'trait_type': 'Form', 'value': 'White'}, {'trait_type': 'Mouth', 'value': 'Open'}, {'trait_type': 'Hair', 'value': 'Backstreetboy Style White'}, {'trait_type': 'Hand with Items', 'value': 'Joint White Fur'}, {'trait_type': 'Random Things', 'value': 'Questionmark'}, {'trait_type': 'Upper Part', 'value': 'Just Hit It Hoodie'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]     https://arweave.net/gA0nwFqylzdosOGIkwvkJAW8ZXA_wlcW5ouHPD_pXec?ext=webp  https://arweave.net/gREYlKDwMtHcHQd3dPAgPKD4t-9B_Ug6m7qYWxuNMsI  d2733e23db79f4046e8e2baf5940f9b0  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z           2  2021-11-28              2022      12.38\n", "3      PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb  Byhp5fvBUteNZZLMdFYTbix6Kv89Xi7KEys7xruqrquu  So11111111111111111111111111111111111111111             0.07            0.20  2021-11-28T20:00:00.000Z         13.76  magic eden v1                  v1  2021-11-28 20:29:58+00:00  ...                                                                                                                                                                                                                                                                       [{'trait_type': 'Role', 'value': 'Farmer'}, {'trait_type': 'Background', 'value': 'Darkblue Gradient'}, {'trait_type': 'Eyes', 'value': 'Angry'}, {'trait_type': 'Eyes Items', 'value': 'Flashing Eyes'}, {'trait_type': 'Form', 'value': 'Light Blonde'}, {'trait_type': 'Hats', 'value': 'Pothat'}, {'trait_type': 'Mouth', 'value': 'Puffin'}, {'trait_type': 'Hair', 'value': 'Long Wavy Light Blonde'}, {'trait_type': 'Hand with Items', 'value': 'Watering Can Light Blonde Fur'}, {'trait_type': 'Upper Part', 'value': 'Brown Hippie Top'}, {'trait_type': 'Genesis Role?', 'value': 'Yes'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]     https://arweave.net/ZgeavQqfLnvk7tYhd3keCpMAZXTLbNZaCZdzRpq-g2E?ext=webp  https://arweave.net/RpZ3kbheRn2EPLNVHNMSdW4o_n8kgryv3Dw1O1j_CQM  b8a239bec0cbb24fe59d4305d6c5cb86  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z           3  2021-11-28              2022      13.76\n", "4      PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb  8V98j4xGca9CDC4H8uYiTyvZEuYkSjA4VLX9mytYPHc2  So11111111111111111111111111111111111111111             0.06            0.20  2021-11-28T20:00:00.000Z         11.69  magic eden v1                  v1  2021-11-28 20:30:28+00:00  ...                                                                                                                                                                                                                                                                                                                             [{'trait_type': 'Role', 'value': 'Artist'}, {'trait_type': 'Background', 'value': 'Blue'}, {'trait_type': 'Ears', 'value': 'Double Piercing'}, {'trait_type': 'Eyes', 'value': 'Red'}, {'trait_type': 'Eyes Items', 'value': 'Red John Lennon Glasses'}, {'trait_type': 'Form', 'value': 'White'}, {'trait_type': 'Mouth', 'value': 'Grinding'}, {'trait_type': 'Hair', 'value': 'Yellow Beanie White Fur'}, {'trait_type': 'Hand with Items', 'value': 'Brownie & Joint White Fur'}, {'trait_type': 'Upper Part', 'value': 'Green Poloshirt'}, {'trait_type': 'Awakened', 'value': 'Yes'}, {'trait_type': 'Animated', 'value': 'Yes'}]     https://arweave.net/fzYog-MyUHni083HllvMngVySxy0DMkUXAMtv8mipTQ?ext=webp  https://arweave.net/Y8r6vQR4CsDeRAPR2X4zhDQv0l6UG6OCGLUj3lYWawY  a172ab4dc669fc698f83761f05aded10  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z           4  2021-11-28              2022      11.69\n", "...                                            ...                                           ...                                          ...              ...             ...                       ...           ...            ...                 ...                        ...  ...                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 ...                                                                          ...                                                              ...                               ...                       ...                       ...         ...         ...               ...        ...\n", "35019  nustrwYoBqryuctcdV7H4R8suJLER7UFfZHZWvAtrvd  7QEr5uj51wD3xSemtQWsjWMzAxBwhBMxGgjq69qyuHwX  So11111111111111111111111111111111111111111             0.00            2.16  2025-06-17T15:00:00.000Z          0.44  magic eden v2                  v2  2025-06-17 15:29:39+00:00  ...                                                                                                                                 [{'trait_type': 'Species', 'value': 'Skellies'}, {'trait_type': 'Body', 'value': 'Skellie Body Space'}, {'trait_type': 'Background', 'value': 'Wasted Land Bright'}, {'trait_type': 'Head', 'value': 'DMT Green'}, {'trait_type': 'Face', 'value': 'Basic Face'}, {'trait_type': 'Chest', 'value': 'Basic Back'}, {'trait_type': 'Mouth', 'value': 'Mouth Closed J'}, {'trait_type': 'Eyes', 'value': 'White'}, {'trait_type': 'Brain', 'value': 'Brain Yellow'}, {'trait_type': 'Upper Part', 'value': 'Shirt <PERSON>'}, {'trait_type': 'Ear Item', 'value': 'Golden Piercing'}, {'trait_type': 'Back Items', 'value': 'Rescue Gun Yellow'}, {'trait_type': 'Rarity Rank', 'value': 'Common'}, {'trait_type': 'Type', 'value': 'Awakened'}, {'trait_type': 'Animated', 'value': 'Yes'}, {'trait_type': 'Awakened', 'value': 'Yes'}]     https://arweave.net/VeFY0oik6w8QUJK7oDpJMSn1tu-QS5xkuqZXn1LJYQA?ext=webp  https://arweave.net/Q3tm-MaTs_OMueOR5Jh3HxsGmsJaju4JPSVJCyF4qVE  1e2b78d613d6a8e83ac63ffc9239a2aa  2025-06-17T20:15:28.531Z  2025-06-17T20:15:28.531Z       35019  2025-06-17              2025       0.44\n", "35020  nustrwYoBqryuctcdV7H4R8suJLER7UFfZHZWvAtrvd  H4YLjZpL9TTrPqYaWJ59H1kwxRiKMvVMxQvDcKV1oNBF  So11111111111111111111111111111111111111111             0.00            4.14  2025-06-17T16:00:00.000Z          0.52  magic eden v2                  v2  2025-06-17 16:22:22+00:00  ...                                                                                          [{'trait_type': 'Background', 'value': 'West World Light'}, {'trait_type': 'Species', 'value': 'Meltops'}, {'trait_type': 'Body', 'value': 'Pink Melting'}, {'trait_type': 'Head', 'value': 'Pink Melting'}, {'trait_type': 'Face', 'value': 'Basic Face'}, {'trait_type': 'Chest', 'value': 'DMT Back'}, {'trait_type': 'Mouth', 'value': 'Sol Beer Hat'}, {'trait_type': 'Eyes', 'value': 'Normal'}, {'trait_type': 'Rings', 'value': 'Championship Ring'}, {'trait_type': 'Eyes Items', 'value': 'Thug Glasses'}, {'trait_type': 'Chains', 'value': 'Nuked Chain Golden'}, {'trait_type': 'Ear Item', 'value': 'Golden Piercing'}, {'trait_type': 'Upper Part', 'value': 'Shirt Banana'}, {'trait_type': 'Hand', 'value': 'Skeleton Hand Nuked Brownie'}, {'trait_type': 'Back Items', 'value': 'Rescue Gun Yellow'}, {'trait_type': 'Rarity Rank', 'value': 'Common'}]  https://www.arweave.net/CuDBnMtwfRlsR5pUoOduPHtpqLYKTJwd1xcTTSNOZ9Y?ext=png  https://arweave.net/CI0EGEHjou8qDW96YwLpty5ZAFB0mMPTGMxzM98vaVA  4b96b068bd352d8cbbde9cda03fcd6a8  2025-06-17T20:15:41.283Z  2025-06-17T20:15:41.283Z       35020  2025-06-17              2025       0.52\n", "35021  nuinTBZK45YybFaYypuBuJfYr6cq6v9ZtoRn1hmEr7V  H4YLjZpL9TTrPqYaWJ59H1kwxRiKMvVMxQvDcKV1oNBF  So11111111111111111111111111111111111111111             0.01            4.13  2025-06-17T16:00:00.000Z          1.21  magic eden v2                  v2  2025-06-17 16:22:22+00:00  ...                                                                                          [{'trait_type': 'Background', 'value': 'West World Light'}, {'trait_type': 'Species', 'value': 'Meltops'}, {'trait_type': 'Body', 'value': 'Pink Melting'}, {'trait_type': 'Head', 'value': 'Pink Melting'}, {'trait_type': 'Face', 'value': 'Basic Face'}, {'trait_type': 'Chest', 'value': 'DMT Back'}, {'trait_type': 'Mouth', 'value': 'Sol Beer Hat'}, {'trait_type': 'Eyes', 'value': 'Normal'}, {'trait_type': 'Rings', 'value': 'Championship Ring'}, {'trait_type': 'Eyes Items', 'value': 'Thug Glasses'}, {'trait_type': 'Chains', 'value': 'Nuked Chain Golden'}, {'trait_type': 'Ear Item', 'value': 'Golden Piercing'}, {'trait_type': 'Upper Part', 'value': 'Shirt Banana'}, {'trait_type': 'Hand', 'value': 'Skeleton Hand Nuked Brownie'}, {'trait_type': 'Back Items', 'value': 'Rescue Gun Yellow'}, {'trait_type': 'Rarity Rank', 'value': 'Common'}]  https://www.arweave.net/CuDBnMtwfRlsR5pUoOduPHtpqLYKTJwd1xcTTSNOZ9Y?ext=png  https://arweave.net/CI0EGEHjou8qDW96YwLpty5ZAFB0mMPTGMxzM98vaVA  4b96b068bd352d8cbbde9cda03fcd6a8  2025-06-17T20:15:41.283Z  2025-06-17T20:15:41.283Z       35021  2025-06-17              2025       1.21\n", "35022  nuinTBZK45YybFaYypuBuJfYr6cq6v9ZtoRn1hmEr7V  A1qumR55jqcNfY6mcNxmpA9W82nWFFvzK29cdKb6fG8K  So11111111111111111111111111111111111111111             0.01            2.17  2025-06-18T08:00:00.000Z          1.12  magic eden v2                  v2  2025-06-18 08:00:34+00:00  ...  [{'trait_type': 'Species', 'value': 'Lucias'}, {'trait_type': 'Body', 'value': 'Basic'}, {'trait_type': 'Head', 'value': 'Basic'}, {'trait_type': 'Background', 'value': 'Wasted Land Dark'}, {'trait_type': 'Face', 'value': 'Basic Face'}, {'trait_type': 'Chest', 'value': 'Basic Back'}, {'trait_type': 'Mouth', 'value': 'Money Joint'}, {'trait_type': 'Eyes', 'value': 'White Out'}, {'trait_type': 'Brain', 'value': 'Brain Yellow'}, {'trait_type': 'Chains', 'value': 'Skull Chain'}, {'trait_type': 'On Face Parts', 'value': 'Meat Face Right'}, {'trait_type': 'Upper Part', 'value': 'Shirt Banana'}, {'trait_type': 'Hair', 'value': 'Hippie Leo'}, {'trait_type': 'Light', 'value': 'Nuked Nimbus'}, {'trait_type': 'Back Items', 'value': 'Jetpack Gold'}, {'trait_type': 'Rarity Rank', 'value': 'Common'}, {'trait_type': 'Type', 'value': 'Awakened'}, {'trait_type': 'Animated', 'value': 'Yes'}, {'trait_type': 'Awakened', 'value': 'Yes'}]     https://arweave.net/b0CD-wGRb3gp17ls9AXkVA9dAQ7x_bd4X-0_DdSfxqY?ext=webp  https://arweave.net/JEb1AtGri3-F6ci2mxGmpMv5UVtkp86IV5K007gQEyQ  018edc7ac9d1ec9c672ecb1dbeae956b  2025-06-18T12:11:50.159Z  2025-06-18T12:11:50.159Z       35022  2025-06-18              2025       1.12\n", "35023  nustrwYoBqryuctcdV7H4R8suJLER7UFfZHZWvAtrvd  A1qumR55jqcNfY6mcNxmpA9W82nWFFvzK29cdKb6fG8K  So11111111111111111111111111111111111111111             0.00            2.18  2025-06-18T08:00:00.000Z          0.48  magic eden v2                  v2  2025-06-18 08:00:34+00:00  ...  [{'trait_type': 'Species', 'value': 'Lucias'}, {'trait_type': 'Body', 'value': 'Basic'}, {'trait_type': 'Head', 'value': 'Basic'}, {'trait_type': 'Background', 'value': 'Wasted Land Dark'}, {'trait_type': 'Face', 'value': 'Basic Face'}, {'trait_type': 'Chest', 'value': 'Basic Back'}, {'trait_type': 'Mouth', 'value': 'Money Joint'}, {'trait_type': 'Eyes', 'value': 'White Out'}, {'trait_type': 'Brain', 'value': 'Brain Yellow'}, {'trait_type': 'Chains', 'value': 'Skull Chain'}, {'trait_type': 'On Face Parts', 'value': 'Meat Face Right'}, {'trait_type': 'Upper Part', 'value': 'Shirt Banana'}, {'trait_type': 'Hair', 'value': 'Hippie Leo'}, {'trait_type': 'Light', 'value': 'Nuked Nimbus'}, {'trait_type': 'Back Items', 'value': 'Jetpack Gold'}, {'trait_type': 'Rarity Rank', 'value': 'Common'}, {'trait_type': 'Type', 'value': 'Awakened'}, {'trait_type': 'Animated', 'value': 'Yes'}, {'trait_type': 'Awakened', 'value': 'Yes'}]     https://arweave.net/b0CD-wGRb3gp17ls9AXkVA9dAQ7x_bd4X-0_DdSfxqY?ext=webp  https://arweave.net/JEb1AtGri3-F6ci2mxGmpMv5UVtkp86IV5K007gQEyQ  018edc7ac9d1ec9c672ecb1dbeae956b  2025-06-18T12:11:50.159Z  2025-06-18T12:11:50.159Z       35023  2025-06-18              2025       0.48\n", "\n", "[35024 rows x 42 columns]"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["royalties = pd.read_csv(\"royalties.csv\")\n", "royalties"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/0z/9qx916gs2qj55k2s6t7rm6ch0000gn/T/ipykernel_2568/*********.py:15: UserWarning: Converting to PeriodArray/Index representation will drop timezone information.\n", "  royalties_grouped = royalties_plot.groupby(royalties_plot['block_timestamp'].dt.to_period('d'))['amount_usd'].sum()\n", "/var/folders/0z/9qx916gs2qj55k2s6t7rm6ch0000gn/T/ipykernel_2568/*********.py:19: UserWarning: Converting to PeriodArray/Index representation will drop timezone information.\n", "  expenses_grouped = expenses_plot.groupby(expenses_plot['block_timestamp'].dt.to_period('d'))['amount_usd'].sum()\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABKYAAAJOCAYAAACN2Q8zAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuMCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy88F64QAAAACXBIWXMAAA9hAAAPYQGoP6dpAAEAAElEQVR4nOzdd3xT9foH8E9Gkw46gJYWkCEOUHZxsKSyQRy4QMX7K3BRAVHmFRAR9eoFHIAXUVCkDK8KAoKiDBkiKqBQQJQho2w66R5pxvf3R8npORlt2ma1/bxfr/MiOTkk3yYnyclznuf5qgAIEBEREREREREReZna1wMgIiIiIiIiIqLaiYEpIiIiIiIiIiLyCQamiIiIiIiIiIjIJxiYIiIiIiIiIiIin2BgioiIiIiIiIiIfIKBKSIiIiIiIiIi8gkGpoiIiIiIiIiIyCcYmCIiIiIiIiIiIp9gYIqIiIiIiIiIiHyCgSkiIqoV4uPjIYRAs2bNyt02Li4OQgjExcV5YWRUHTVr1gxCCEyePNnXQ/ELQggsXLiwSvdx5513wmAwoGnTphX+vwkJCcjNza3S4/vSrFmzIIRA/fr1fT2UciUlJSEhIcFt96fRaDB37lxcuHABZrMZX3/9tdvu21eee+45nD9/HjqdztdDISKqFhiYIiKiKrMGfayL0WjEpUuXkJCQgEaNGvl6eE6NGTMG8fHxvh6GV1h/+DpboqOjfT1EckD+3urWrZvDbS5cuAAhBL799luPjqVLly6YNWsWwsPDPXL/b731Fr744gtcuHDB7jPF2ZKUlOSRsVQ3CQkJTp+jwsJCXw+vTCNHjsRLL72EtWvXIj4+HvPnz3e4nUqlQnx8PDZu3IgLFy4gLy8PR48exYwZM6DX653e97Fjx1BYWIi///4b48aNs9vm4YcfxpdffokzZ84gPz8fJ06cwLvvvutwP583bx4OHjyIjIwM5Ofn49ixY5g1axZCQkIU2y1fvhw6nQ7PPfdcJZ4RIqLaR+vrARARUc0xc+ZMJCUlITAwEJ07d8bw4cPRvXt3tGnTBgaDwdfDszN27Fikp6djxYoVivU//fQTAgMDUVxc7KORec7o0aORl5dntz4rK8v7gyGXFRYW4qmnnsIvv/yiWB8XF4cmTZqgqKjI42Po2rUrXnvtNSxfvhzZ2dluve/27dujb9++6NKlC4CS9+DTTz+t2Gbp0qX47bff8PHHH0vrHO3LtVVRURFGjRplt95sNvtgNK7r1asXLl26hEmTJpW5XXBwMJYvX469e/di8eLFSE1NRZcuXfD666+jd+/e6NWrl2L7Z599FkuWLMHatWsxb9483HPPPVi4cCGCg4Px9ttvS9t9/PHHuHLlCj777DNcuHABbdu2xbhx43DfffchNjZW8d668847sWfPHiQkJKCoqAgdO3bEtGnT0KdPH/To0QNCCACAwWDAihUrMGnSpCpnEhIR1RaCCxcuXLhwqcoSHx8vhBCiU6dOivWzZ88WQgjx+OOP+80YmzVrJq07evSo2LVrl8/H5o1l1qxZQggh6tev7/Ox1ISlWbNmQgghJk+e7NHHse63a9euFampqUKj0ShuX7Jkifj9999FUlKS+Pbbbz06lsmTJ9u9h6yLEEIsXLiw0ve9YMECce7cuTK3yc3NFQkJCQ5vS0hIELm5uZV6bI1GIwICAny6P1X1/VmVv7+iS1JSktPXoTLLjh07xNGjR8vdLiAgQHTp0sVu/cyZM4UQQvTu3VtaFxgYKNLS0uzeE6tWrRK5ubkiIiJCWhcXF2d3n//4xz+EEEL885//LHdckyZNEkIIcffddyvWx8bGCiGE6Nmzp0/3LS5cuHCpDgtL+YiIyGP27NkDALjpppsU63v27ImffvoJeXl5yMzMxIYNG9CqVSvp9nvvvRdCCAwePNjuPp988kkIIdC5c2cAQNu2bZGQkIAzZ86gsLAQV69exaeffop69eqVObakpCS0adNGeiwhBHbt2gXAeY+pu+66C5s3b0ZWVhby8/Px448/omvXropt6tSpg/nz5yMpKQlFRUVISUnBtm3b0LFjR6djefTRRyGEQI8ePexue/bZZyGEQOvWrQEA0dHRWLZsGS5evIiioiJcuXIFGzZscKl3liuWL1+OwsJCxesBAFu2bMG1a9fQsGFDAKUlZvfccw8WL16M9PR0ZGdnY8WKFYiIiLC73wEDBkiveU5ODjZt2oTbb79dsY21T1CjRo3w9ddfIzc3F6mpqXjnnXegVisPWYYOHYoDBw4gJycH2dnZ+OOPP/Diiy8qtgkPD8f8+fNx4cIFFBUV4dSpU3jppZegUqkqfF9lmTBhAs6dO4eCggL8+OOP0msFAMOHD4cQAh06dLD7f9OnT4fJZHKp3PWLL75A/fr10bdvX2ldQEAAHnvsMXz++ecO/09wcDDeffdd6e8/ceKEw55Y1v5QDz30EI4ePYqioiL8+eef6N+/v7TNrFmz8O677wIAzp07J71nbPe7su6jLIMHD8bOnTtd2rYs5e078t5g48ePx+nTp2EwGKR9sbzPJqBkP3VUQmgtl5ULDAzE+++/j7S0NOTk5GDjxo1o1KgRhBCYNWuW3X1EREQgISEBmZmZyMrKwrJlyxAUFFTl58XK+r7t2rUr3nvvPaSmpiIvLw/r169HZGSk3fYzZszAxYsXkZ+fj507d9q9Z8tS3v5nfS169eqFNm3aSPuUs95+RqMRe/futVtv7Ul12223Set69uyJyMhIfPjhh4ptFy1ahDp16mDQoEHSut27d7t0n86cO3cOAOw+9xITE5GRkYGHHnqo3PsgIqrtWMpHREQe07x5cwBAZmamtK53797YvHkzzp49i9deew1BQUF44YUX8MsvvyA2Nhbnz5/Hjz/+iAsXLmDYsGHYsGGD4j6HDRuG06dPY9++fQCAvn37okWLFkhISEBycjJat26NZ599Fq1bt5aCV45MmDABCxcuRF5eHt566y0AQEpKitPte/bsic2bN+PgwYN4/fXXYbFYMGLECOzcuRP33HMPfv/9dwDA4sWL8dhjj+GDDz7AsWPHUL9+fXTv3h233XYbDh065PC+v/vuO+Tm5mLIkCH46aefFLcNHToUf/75J/766y8AwLp169C6dWssXLgQ586dQ4MGDdC3b180bdoU58+fdzp+K0cBO5PJJJVmjR8/Hr169cKKFSvQpUsXWCwWPPvss+jfvz+efvppXL16VfF/P/jgA2RlZeG1115Dy5YtMWbMGDRr1gz33nuvtM3TTz+NFStWYOvWrZg6dSqCg4MxZswY/Pzzz+jYsaNi3BqNBlu3bsX+/fsxZcoU9OnTB1OmTMGZM2ewePFiAECfPn3w5ZdfYvv27Zg6dSqAkh+Q3bp1w3//+18AQFBQEHbv3o3GjRtjyZIluHDhArp27YrZs2ejYcOGmDhxosv3VZb/+7//Q2hoKBYtWoTAwECMHz8eO3fuRNu2bZGamoq1a9di0aJFGDZsGA4fPqz4v8OGDcOPP/6IK1eulPs4586dw969e/Hkk09iy5YtAICBAwciPDwcX375pcNA2jfffIOePXvi008/xeHDh9G/f3+8++67aNy4sV3ZVPfu3fHII4/gww8/RG5uLl588UWsW7cOTZs2xbVr17B+/XrceuuteOqppzBhwgSkp6cDANLS0ly+D2caNWqEZs2aITExsdznoSyu7DtWI0aMQGBgID7++GMYDAZcu3bNpc+milq+fDmGDh2KlStXYt++fYiLi8N3333ndPs1a9YgKSkJ06dPR2xsLJ555hmkpqZi2rRpLj2eo+bpxcXFdo3hFy5ciMzMTLz++uto3rw5JkyYgA8++ABPPPGEtM0bb7yBmTNn4rvvvsP333+P2NhYbNu2zeWG3uXtf2lpaXj66acxY8YM1KlTB9OnTwcAHD9+3KX7t4qJiQEAaZ8EIJ0IOHDggGLbgwcPwmw2o2PHjvjf//5Xofu00mg0iIiIgE6nQ5s2bfDmm28iJycHv/32m922iYmJTnvDERGRks/Ttrhw4cKFS/VerOVGvXr1EvXr1xeNGzcWjzzyiEhJSRGFhYWicePG0raJiYkiOTlZ1K1bV1rXtm1bYTKZxPLly6V1b731ligsLBRhYWHSusjISFFcXCxmzZolrQsMDLQbz9ChQ4UQQnTv3t1ujK6U8sXFxQkhhKLE4+TJk2Lz5s2K7QIDA8WZM2fE1q1bpXWZmZmVKmn63//+J5KTk4VarZbWRUdHC5PJJF555RUBQISHh1e6fMxaKuTI8ePHFdv27dtXCCHEyy+/LJo3by5ycnLE+vXrHb7mv//+u9BqtdL6KVOmCCGEeOCBBwQAERISIq5duyaWLFmi+P8NGjQQmZmZivUJCQlCCCH9vdbl4MGD4vfff5euz58/X2RlZSmeK9tlxowZIjc3V9x8882K9f/5z3+E0WgUN9xwg8v35WixlvLl5+eLRo0aSevvvPNOIYQQ7733nuK1vXTpklCpVNK6Dh06CCGEiI+Pd+m91alTJzF27FiRnZ0t7fOrV68WO3bsEADsSvkefPBB6TWU39+aNWuE2WwWLVq0kNYJIURRUZFiXdu2bYUQQjz//PPSuvJK+Vy5D0dLr169hBBCDBo0qMztyivlc2Xfsb5uWVlZIjIyUrGtq59NCQkJIikpyel7zHq9Y8eOQggh5s2bp9hu2bJlQgih+Byz/t+lS5cqtl23bp1IS0srd3+0/v2OyD+3rPvTtm3bFP//vffeE0ajUfq8jYyMFEVFRXalcG+++aYQQpRbyleR/W/Xrl0ulfI5W7Zt2yaysrJEeHi4tG7hwoXCaDQ63D4lJUV8/vnnZd7nJ598IoxGo93nBwBx9913231+OioHBCAWL14s8vPzK/23ceHChUttWVjKR0REbrNjxw6kp6fj0qVLWLduHfLz8/Hggw/i8uXLAErOQnfs2BHLly9XZFEdPXoUP/zwA+677z5p3cqVKxEYGIjHHntMWjd06FAEBATgs88+k9bJG9Pq9XrUr19fyqaKjY11y9/VoUMH3Hrrrfj8889Rv359aQkJCcGOHTvQo0cPqTwsKysLd999t1Ty5qrVq1cjOjpakWn02GOPQaPRYPXq1QBKGmAbDAbce++9DsvlXPHII4+gT58+imXEiBGKbX744QcsXrwYr776KtavX4+ioiKns0t9/PHHMJlM0vWPPvoIRqNRei379u2LunXrSqVo1sVsNmP//v3o2bOn3X3aZrfs2bMHLVq0kK5nZWUhJCREUdZm6/HHH8eePXuQmZmpeNzt27dDq9VKZZOu3FdZNmzYoMh4+v3337Fv3z67fblx48aKv3XYsGEoKCjAunXrXH6sNWvWICgoCPfffz/q1KmD+++/32kZ33333QeTyWSX9fXee+9BrVZj4MCBivXbt2/H2bNnpetHjx5Fdna24nkvT2Xvw5rlI/9MqKzy9h2rdevWKbJhKvLZ5KoBAwYAgF05WVnNsB2NPzIyEqGhoeU+XmFhod17u0+fPg6zreQN5K2Po9VqpdLMPn36QK/X2411wYIF5Y4DqPj+V1nTp09H3759MW3aNEVD/qCgIKeTVxQVFZVZHvnkk09i1KhReO+993D69Gm7248dO4Y+ffrgoYcewty5c5Gfn486deo4vK/MzEwEBwe7tRyTiKgmYikfERG5zdixY/H3338jPDwcI0eORI8ePRSz8Vl/9Jw8edLu/x4/fhwDBgxAcHAwCgoKcPLkSfz2228YNmwYli1bBqDkx/zevXtx5swZ6f/VrVsXs2bNwhNPPIHo6GjFfbprWvtbbrkFQEmAwZnw8HBkZWXhpZdewooVK3Dx4kUcPHgQ33//PVauXFnutPZbtmxBVlYWhg4dKvXaGTp0KA4dOoRTp04BKCnJmTp1Kt577z2kpKRg37592LRpE1auXFlmGaLcTz/9hIyMjHK3mzJlCh566CF07NgRTz75pKJkS846Nqv8/HxcvXpVKuO0PnfW/l22bGd3KywstCufyczMVJQgfvjhhxgyZAi2bNmCS5cuYdu2bVizZg22bt0qbXPLLbegffv2DktxAKBBgwYu31dZbP9+APj7778xZMgQ6foPP/yAK1euYNiwYdi5cydUKhWefPJJbNy4sUKzyqWnp2P79u146qmnEBwcDI1Gg7Vr1zrctlmzZrhy5Yrd/VvLpGx7Q124cMHuPjIzM1G3bl2Xx1fV+7Dt/VVRruw7Vrbvx4p8NrmqWbNmMJvNdo/lKNhhZfscWoNkdevWtSvHs2U2m7Fjxw6XxlbW41jHDtjv3+np6WWWZVpVdP+rjCFDhuDNN9/E0qVL7QJ6hYWFTksOAwMDUVhY6PC27t2749NPP8WWLVswY8YMh9vk5uZKz/M333wjvZdjY2Pxxx9/KLa17tPCpvcYEREpMTBFRERu89tvv+HgwYMASjJJfv75Z3z++edo2bIl8vPzK3x/K1euxPvvv4/GjRtDr9ejS5cueP755xXbrFmzBl27dsU777yDw4cPIy8vD2q1Glu3brVrmF1Z1vuZMmWKXZ8gK+sPsK+++gp79uzBww8/jH79+uFf//oXpk6dikceeUTqDeRIcXExNmzYgIcffhhjx45FdHQ0unXrhpdfflmx3fvvv49vv/0WgwcPRv/+/fHvf/8b06dPR69evZyOrTI6duwoBW/atm2LL7/8slL3Y33unn76aSQnJ9vdLs+2Alyb2j4tLQ0dOnRA//79MXDgQAwcOBAjR47EihUrMHz4cOlxt23bppgWXu7vv/92+b6qymKx4PPPP8czzzyDsWPHolu3bmjcuLEi889Vn3/+OT755BPExMRg8+bNdoG9ynL2vFckWFTZ+7AGSisSBKvI4zviLDDhCmdBBo1GU+n7tHLH6+BPj+Mpffr0wcqVK/Hdd99h9OjRdrdfvXoVWq0WUVFRiqB6QEAA6tev77CvW7t27fDNN9/gzz//xGOPPeby/rR+/XqsWrUKTzzxhF1gqm7dusjPz1dk9hIRkT2W8hERkUdYLBZMnz4djRs3xrhx4wBAah7csmVLu+1btWqFtLQ0RUbCl19+CbPZjCeffBLDhg1DcXGxVNYGlMyC1KdPH8yZMwevvfYaNmzYYFdOVBZXz2JbM7RycnKwY8cOh4s8wJKcnIyPPvoIDz/8MG688UZkZGQ4Pfsut3r1akRFRaF37954/PHHoVarFX+v1dmzZzFv3jz0798fbdq0gU6nczjbWmUFBwcjISEBx44dw5IlS/DSSy/hjjvucLitNSPKKiQkBA0bNpRmqrI+d6mpqQ6fN0czYrnCaDRi06ZNeP7553HTTTdh8eLFiI+Pl2aAPHPmDOrUqeP09bp48aLL91UW278fAG699Vbp77dauXIlwsPD8cADD2DYsGFITU11OStL7uuvv4bFYkGXLl2clvEBJe+1Ro0a2ZUYWWeYq0wjb09lfZw4cQIAcOONN3rk/l1Rkc+mzMxMh6W0tllA58+fh0ajsfu7br75ZjeN2nOsz4ft/h0ZGVnujKfW/+/u/c/qrrvuwtdff40DBw5gyJAhDgNI1iC97efWHXfcAY1GYxfEb9GiBbZs2YLU1FTcd999FTqRotfrodFoHGbo3njjjRVu5k5EVBsxMEVERB6ze/du7N+/HxMmTIBer0dycjIOHTqE+Ph4xUF869at0a9fP3z//feK/5+RkYHNmzfj6aefxrBhw7BlyxZFGZr1B4ntWf4JEya4NL78/HyXejUdPHgQp0+fxpQpUxASEmJ3u3WadbVajbCwMMVtaWlpuHLlCvR6fbmPs337dmRkZGDo0KEYOnQo9u/frwhwBAUF2d3PmTNnkJub69L9u2ru3Llo2rQp4uPjMWnSJJw7dw4rVqxwWBrz7LPPQqstTcAeM2YMAgICsHnzZgDA1q1bkZ2djZdfflmxnZWjKerLY/vDWAghZSpYnwdrJl2/fv3s/n94eLiU3eLKfZVl8ODBaNSokXT9zjvvROfOnaW/3+ro0aM4cuQIRo0ahUcffVQKulZUfn4+xowZg1mzZuHbb791ut33338PrVYrBYWtJk6cCIvFYjc+Vx8bQKX7mzlz5coVXLhwwWnw0xsq8tl05swZREREoG3bttK6mJgYPPzww4r7tAYex44dq1j/wgsveOJPcKvt27ejuLjYbqyufrZ6Yv8DSgJb3333Hc6dO4f777/faSbSzp07kZGRgTFjxijWjxkzBvn5+YqZEaOjo7Ft2zZYLBb079/faflveHi4w8+wUaNGAbCfARAo6XP466+/uvz3ERHVVizlIyIij3rnnXewdu1aDB8+HEuWLMG//vUvbN68GXv37sWnn34qTcmenZ2N1157ze7/r1y5UmoQPXPmTMVtubm52L17N1566SUEBATg8uXL6Nevn8uZFwcPHsSYMWMwY8YMnD59GqmpqQ57IQkhMGrUKGzevBl//fUXEhIScPnyZamhdU5ODh588EGEhobi0qVLWLt2LY4cOYK8vDz06dMHd911FyZNmlTueEwmE9avX48nnngCISEhmDJliuL2W2+9FTt27MCaNWtw7NgxmEwmPPzww4iJiXG51O6xxx5z2Nfohx9+QGpqKnr27ImxY8fi9ddfx6FDhwAAI0aMwI8//oh///vfmDp1quL/6XQ6aUwtW7bE2LFjsWfPHnzzzTcASl6jMWPGYNWqVUhMTMSXX36JtLQ0NG3aFIMGDcIvv/xS4R/qS5cuRb169bBz505cunQJzZo1wwsvvIBDhw5J2QnvvPMOHnzwQWzatAnLly/HwYMHERISgrZt2+Kxxx5D8+bNkZGR4dJ9leX06dP4+eef8dFHH0Gv12PChAlIT093WEK4cuVKvPfeewBQqTI++f2U59tvv8XOnTvx1ltvoXnz5jhy5Aj69euHwYMHY/78+S5nFcpZy3TfeustfPnllzAajfj2228r1HfJmY0bN9oFdrzN1c+mL7/8EnPnzsXXX3+N//73vwgODsaYMWPw999/o1OnTtJ2iYmJWLt2LSZOnChNyhAXF4dbb70VgPsz0LRaLYYNG+bwtq+//rpCr1N6ejreffddvPzyy9i0aRO+//57dOzYEQMHDnTab07OE/tfnTp1sHXrVtStWxfvvPMOBg0apLj9zJkz0sQXRUVFmDlzJj788EOpZ9w999yDf/zjH3j55ZcVDe63bNmCm266CXPnzkX37t3RvXt36baUlBRs374dAHDvvffiv//9L9auXYtTp05Bp9PhnnvuwSOPPILff//d7j0dGxuL+vXrY+PGjRX+W4mIaiOfTw3IhQsXLlyq9yKf0t72NpVKJU6dOiVOnTol1Gq1AEqmh9+zZ4/Iz88XWVlZYuPGjaJVq1YO7zsgIEBkZGSIzMxModfr7W5v1KiRWLdunbh27ZrIzMwUq1evFjExMXbTsVvHKJ/qvkGDBuLbb78V2dnZQgghdu3aJQCIuLg4IYSwmwK8ffv2Yu3atSItLU0UFhaKpKQk8eWXX4qePXtKY507d644dOiQyM7OFrm5ueLQoUNi9OjRLj+XvXv3FkIIYTabRePGjRW31atXTyxcuFAcO3ZM5ObmiszMTLF3717x2GOPlXu/1unonYmLixN16tQRSUlJ4sCBA0Kj0Sj+/3vvvSdMJpO4++67Fc/nPffcIxYvXiwyMjJETk6OWLVqlahbt67d48fFxYnNmzeLzMxMUVBQIE6dOiWWLVsmYmNjpW0SEhJEbm6u07Fbrz/yyCNiy5YtIjk5WRQVFYlz586Jjz76SERHRyv+X0hIiHjrrbfE33//LYqKikRqaqr4+eefxaRJk4RWq63QfdkuzZo1E0IIMXnyZDFx4kRx/vx5UVhYKHbv3i3atm3r8P9ER0cLo9EoTpw44Zb3lnxJSkoS3377rd3f/95774lLly4Jg8EgTp48KSZPnmz3f4UQYuHChQ7vMyEhQbFuxowZ4uLFi8JkMineTxW5D0dLhw4dhBBCdOvWzek2ubm5Tu/L1X1H/ro5uh9XP5v69Okj/vjjD1FUVCSOHz8unnrqKbvHAiCCgoLEwoULRXp6usjJyRHr168Xt9xyixBCiJdeeslunPXr13f4+ss/t5z9/WWx/n9n+5OjzzyVSiVmzpwpLl++LPLz88XOnTvF7bff7vJr6ur+t2vXLnH06NFy78/62jnjaEyjRo0Sx48fF0VFReLUqVNi/PjxDvd/Z6zfCQBEixYtxPLly8Xp06dFfn6+KCgoEEePHhWzZs0SwcHBdvc7e/Zsce7cOZff61y4cOFSyxefD4ALFy5cuHBxumg0GpGSkiKWLl3q87FwKV1cDZhwKV3q168viouLxSuvvOLzsfjjsn37drFy5Uqfj8PTS/v27YUQQjz11FM+HwsXzyw6nU5cuXJFvPjiiz4fCxcuXLhUh4U9poiIyK8NHjwYDRo0cKl8icifDR8+HBqNBqtWrfL1UPzSyy+/jKFDh6Jp06a+HorbBAYG2q2bMGECzGYzfvrpJx+MiLxhxIgRMBqNWLx4sa+HQkRULbDHFBER+aW77roL7dq1w8yZM5GYmMgfcVRt9ezZE7fffjtmzJiBDRs2VGlGsprst99+c2sTf3/w0ksvoVOnTti1axdMJhMGDhyI++67D0uWLMGlS5d8PTzykCVLlmDJkiW+HgYRUbXi87QtLly4cOHCxXZJSEgQRqNR/P7776J169Y+Hw8X5cJSPteXXbt2CYPBIHbu3CkaNWrk8/Fw8d7Sp08fsWfPHpGRkSEMBoM4deqUePXVV+16uHHhwoULFy61eVFdv0BERERERERERORV7DFFREREREREREQ+wcAUERERERERERH5BJufu1GjRo2Qm5vr62EQEREREREREflUaGgorly5Uu52DEy5SaNGjXD58mVfD4OIiIiIiIiIyC80bty43OAUA1NuYs2Uatq0KbKyslz+fxqNBn379sUPP/wAs9nsodFRTcB9hVzFfaV24utOFcH9hRzhfkEVwf2FnOG+QQAQERGBCxcuuFRVxsCUm+Xm5laonE+j0aCwsBC5ubl801KZuK+Qq7iv1E583akiuL+QI9wvqCK4v5Az3DcIKNkPXMXm50RERERERERE5BMMTBERERERERERkU8wMEVERERERERERD7BHlNEREREREReEhwcjMjISKhUKl8PpUo0Gg0iIyPRrFkz9hEiBe4bNZ8QAunp6SgoKHDL/TEwRURERERE5GEqlQojRozAvffe6+uhuE1QUBB69erl62GQH+K+UTv8+OOPSEhIgBCiSvfDwBQREREREZGHjRgxAnFxcVi9ejVOnDgBk8nk6yFVWWhoaIVmJKfag/tGzabVatGqVSsMGTIEALBs2bKq3Z87BkVERERERESOhYSE4N5778Xq1avx3Xff+Xo4bhMeHo7s7GxfD4P8EPeNmu/MmTMAgKFDh+LLL7+sUlkfm58TERERERF5UP369QEAJ06c8PFIiIjcx/qZFhkZWaX7YWCKiIiIiIjIg6yNzmtC+R4RkZX1M62qkzkwMEVERERERERERD7BwBQRERERERH5rfj4eGRmZpa7nRACDz30kBdGRETuxMAUERERERFRNaFSq3HTHR3RcWBf3HRHR6jUnv1JZ50KXgiB4uJinD17FnPnzoVer/fo45Zl1qxZOHTokN36mJgYbN682QcjIqKq4Kx8RERERERE1UDb3nEYPG0iImKipXVZySnYMGc+ju7Y7bHH3bx5M0aMGIGAgAB06tQJK1asgBACs2fP9thjVkZKSoqvh0BElcCMKSIiIiIiIj/Xtncc4ufNRniDKMX68AZRiJ83G217x3nssQ0GA1JSUnDp0iVs3LgR27dvR9++fQEAOp0O77//PlJSUlBYWIg9e/bgjjvukP7vqVOnMHnyZMX9tW/fHkII3HTTTQCAiRMn4o8//kBeXh4uXLiARYsWISQkxOFY4uPj8dprr6FDhw5SJld8fDwA+1K+G264AatXr0ZmZiYyMjKwYcMGNGvWTLo9Li4O+/fvR15eHjIzM/Hzzz+jadOm7nnSiMhlDEwRERHJeLtEgoiIqDwqtRqDp00EIOy+l0quCzw0dYJXvrNat26Nrl27ori4GADw9ttv49FHH0V8fDxiY2Nx+vRpbN26FXXr1gUALFu2DCNGjFDcx4gRI7B7926cOXMGAGCxWPDiiy+idevWiI+PR69evfD22287fPzVq1fj3XffxZ9//omYmBjExMRg9erVdttptVps3boVubm5uOeee9CtWzfk5eVhy5YtCAgIgEajwYYNG7B79260a9cOXbp0wccffwwhhDufLiJyAUv5iIiIrvNViQQREdVOE75chtDI+uVupw0IQJ16dZ3erlKrUbdhDF7btQkmo7Hc+8tNz8CCJ0a6PM77778fubm50Gq1CAwMhNlsxrhx4xAcHIwxY8Zg+PDh2LJlCwDgmWeeQd++ffHPf/4T7777LpYvX4433ngDd955J37//XdotVo89dRTmDJlinT/77//vnT5/PnzeOWVV7B48WI8//zzdmMpKipCXl4eTCZTmaV7Q4cOhVqtxqhRo6R1I0aMQFZWFu69914cOHAAERER2LRpE86ePQsAOHHihMvPCRG5DwNTREREKC2RAJRnSq0lEismTWdwioiI3Co0sj4iohu47f7KCl5Vxa5duzBmzBiEhIRg4sSJMJlMWL9+Pbp27QqdTodffvlF2tZkMuG3337DbbfdBgC4evUqvvvuO4wcORK///47HnjgAej1enz11VfS/+nduzemT5+OVq1aISwsDFqtFkFBQQgKCkJhYWGlxty+fXvcfPPNyM3NVawPDAzETTfdhB9++AEJCQnYunUrfvjhB2zfvh1r1qxBcnJypR6PiCqPgSkiIqr1yiuREBYLHpo6AX/u2gNhsfhmkEREVOPkpme4tF15GVNWedcyXc6Yqoj8/Hyp7G7kyJE4cuQIRo4c6XKG0dKlS7Fq1SpMnDgRI0aMwOrVq6WAU7NmzbBp0yZ89NFHmDFjBq5du4bu3btj2bJl0Ol0lQ5M1alTBwcPHsSwYcPsbktLS5P+lv/+978YMGAAhg4dijfffBN9+/bF/v37K/WYRFQ5DEwREVGt1yK2vaJ8z5a1RKJFbHucOWA/PTUREVFluFpOp1Kr8crW9QhvEOWwj5SwWJCVkoq3Bjzq8RMoQgj85z//wbx589CpUycYDAZ069YNFy5cAFDS2+nOO+/EggULpP/z/fffIz8/H2PGjMGAAQPQo0cP6bZOnTpBrVZj8uTJUn+nIUOGlDmG4uJiaDSaMrdJTEzE0KFDkZqaapc1JXf48GEcPnwYc+bMwa+//oqnnnqKgSkiL2NHVyIiqvXCoiLduh0REZE7CYsFG+bMB6CyCzyVXFdh49wFXsvq/eqrr2A2mzFy5Eh89NFHeOedd9C/f3/cdttt+OSTTxAcHIxPP/1U2t5isWD58uWYPXs2Tp06hX379km3nT59GjqdDi+88AJuvPFGPP300xg9enSZj3/u3DnceOONaN++PerXrw+dTme3zf/+9z+kp6dj48aN6N69O5o3b464uDi8//77aNy4MZo3b47//Oc/6Ny5M5o2bYq+ffvilltuwfHjx933RBGRSxiYIiKiWi8nLd2t2xEREbnb0R27sWLSdGSnpinWZ6Wker0PotlsxgcffIDx48djxowZWLduHVatWoXExETcfPPN6N+/P7KyshT/59NPP4Ver0dCQoJi/R9//IGJEydi6tSp+PPPPzFs2DBMnz69zMdft24dtmzZgl27diE9PR1PPvmk3TaFhYXo0aMHLly4gPXr1+P48eP49NNPERgYiJycHBQUFKBVq1ZYt24d/v77b3z88cdYtGgRlixZUuXnh4gqhqV8RERU651NPIKs5JRySyTOJh7xweiIiIhKHN2xG3/u2oMWse0RFhWJnLR0nE084tFMqREjRjhcP3fuXCxevBgFBQUYP348xo8fX+b9NG7cGMXFxVi5cqXdbQsWLFCU/gHAZ599Jl1esWIFVqxYIV0vLi7G448/bnc/KpVKcT0lJQXDhw93OJ7c3Fw88sgjZY6ZiLyDgSkiIqr1rCUS8fNmQ1gsiuCUL0okiIiInBEWS7Xqd6jT6RAVFYXXXnsNX331FVJTU309JCLyMyzlIyIiQmmJRN61TMV6X5RIEBER1RRPPvkkzp8/j4iICLz00ku+Hg4R+SFmTBEREV13dMduZCWnYMKXJf0vUpPO4+3BTzFTioiIqJJsy/CIiGwxY4qIiEhOVfrVaDaZGJQiIiIiIvIgBqaIiIhkVOrSxqnagAAfjoSIiIiIqOZjYIqIiEhGrdZIlzUMTBEREREReRQDU0RERDJqTelXo1bHwBQRERERkScxMEVERCSjUssDUzofjoSIiIiIqOZjYIqIiEhGrWEpHxERERGRtzAwRUREJKOWZ0wxMEVERERl2L17N5588kmXtxdC4KGHHvLgiKi6SEpKwvjx4z36GLt27cL8+fMr9X+fe+45fPPNN24ekWMMTBEREcmoZD2mNAFaqFSqMrYmIiLyjiZNotCx401OlyZNojzyuAkJCRBC2C2bN2/2yONVJw888ACio6Px5ZdfSuuSkpLsnquLFy/6cJSl2rVrh40bNyIlJQWFhYVISkrCl19+iagoz+w71VF8fLzitcvNzcWBAwfw8MMP+3poXrds2TLExsaie/fuHn8srccfgYiIqBqRz8oHABqdDiaDwUejISIiKglKnTi5GEFBznsfFhYWo1XL0bh4Mc3tj79582aMGDFCsc7A70a8+OKLUuBObubMmfjkk0+k62az2avj0mq1MJlMinWRkZHYsWMHNm3ahP79+yMrKwvNmzfHgw8+iJCQEKSluX+/8QezZs1C8+bN7fbfsmRnZ6Nly5YAgNDQUIwYMQJr1qxB69at8ffff3tqqH7HaDTi888/x4svvoiff/7Zo4/FjCkiIiIZ+ax8AKAN4DkcIiLyrcjIsDKDUgAQFKRDZGSYRx7fYDAgJSVFsWRlZQEA4uLiYDAYFFkV//rXv5CSkoIGDRoAKCknWrhwIRYuXIisrCykpaXhjTfeUDyGTqfDO++8g0uXLiEvLw/79u1DXFycdHt8fDwyMzPRr18/HDt2DLm5udi8eTNiYmKkbeLi4rB//37k5eUhMzMTP//8M5o2bSrd/uCDD+LgwYMoLCzEmTNn8Oqrr0Ij6y05a9YsnD9/HkVFRbh8+TLef/99p89JZGQkevXqhW+//dbuttzcXMVzlZ6e7vR+2rRpgx07dqCgoADp6elYsmQJQkJCpNtVKhVmzpyJixcvoqioCIcOHUL//v2l25s1awYhBIYMGYIff/wRhYWFGDZsmN3jdOvWDeHh4Rg1ahQOHz6Mc+fO4ccff8SkSZNw7tw5AMDvv/+OyZMnS//n66+/RnFxsTSexo0bQwiBm266CUD5r5n1cX/66ScUFBTgwoULeP/99xEcHCzdnpSUhFdeeQWff/458vLycOnSJYwdO1ZxHxV5XdxBCCG9dqdPn8Yrr7wCi8WCdu3aKbaxLcnMzMxEfHw8AGDHjh1YuHCh4vbIyEgYDAb06tVLWhcaGlrm3z5x4kT88ccfyMvLw4ULF7Bo0SLF/gEAXbt2xa5du5Cfn49r165hy5YtiIiIcPi33XfffcjKysJTTz0FoPz3zLfffosHH3wQgYGBLj57lcPAFBERkYx8Vj6AM/MRERGVZffu3ViwYAFWrVqFsLAwdOjQAf/+978xatQopKamStvFx8fDZDLhrrvuwvjx4zFp0iSMGjVKuv2DDz5Aly5d8MQTT6Bdu3b46quvsGXLFtx8883SNsHBwZgyZQr+8Y9/oEePHmjatCneffddAIBGo8GGDRuwe/dutGvXDl26dMHHH38sZTN1794dK1euxPvvv4/bb78dzz33HIYPH44ZM2YAAB599FFMnDgRzz33HG655RYMHjwYR48edfp3d+/eHQUFBTh+/Hiln7vg4GBs3boVmZmZuPPOO/H444+jT58++OCDD6Rtxo8fj8mTJ2PKlClo164dtm7dim+++UbxvADAnDlz8P777+O2227D1q1b7R4rOTkZAQEBZZak7d69G/fee690/Z577kFWVpYUdIyLi8OlS5dw5swZAOW/Zi1atMCWLVuwbt06tGvXDkOHDkX37t0Vfx9QEsg8cuQIOnbsKP0dffr0AVDx18Xd1Gq1FGxKTEx0+f8tXboUTz31FHSy48inn34aly9fxs6dO6V1Zf3tAGCxWPDiiy+idevWiI+PR69evfD2229Lt7dv3x47duzAsWPH0KVLF3Tv3h3ffvutIuBq9eSTT+KLL77AsGHD8Pnnn5f7ngGAAwcOQKvV4u6773b5b68swaXqS2hoqBBCiIiIiAr9P41GI+6//36h0Wh8/jdw8e+F+woXVxfuK1Vb2vfvLd47uldaIqIb+HxMfN25uHvh/sLF0cL9wnNLs2bNxMqVK0WzZs0U63/7fZ64cDGh3OVq8kphEd+Wu1xNXunS/f32+zyXx56QkCCMRqPIzc1VLNOnTxfh4eECgAgICBCJiYniyy+/FH/++adYsmSJ4j527dol/vrrL8W62bNnS+uaNGkijEajaNiwoWKbH374Qbz11lsCgIiPjxdCCNGiRQvp9jFjxoirV68KAKJu3bpCCCF69Ojh8O/44YcfxLRp0xTrhg0bJi5fviwAiIkTJ4oTJ04IrVbr0vMyfvx4cfr0abv1SUlJoqioSPFcvfDCC9LtQgjx0EMPCQBi1KhRIiMjQwQHB0u3Dxw4UJhMJtGgQcnxx6VLl8T06dMVj7F//37xwQcfSPuWEEK8+OKL5Y75zTffFMXFxSI9PV18//33YsqUKdLjABD333+/yMzMFGq1WrRr105cuXJFzJ8/X8yePVsAEB9//LH47LPPXHrNwsPDxSeffCIWL16suL1bt27CZDIJvV4vPV/ff/+9YpsvvvhCfPfdd5V6XWyXWbNmiYSEBJe3t+5n1tfOZDKJwsJCER8fr9hO/jpal8zMTGk7vV4vMjIyxOOPPy7dfvjwYfHqq68q9pWy/nZHy6OPPirS0tKk6//73//Enj17nG6/a9cuMX/+fDF27FiRmZmpeH+U956xLhkZGeL//u//HN7m7LMNgIiIiBBCCBEaGlru8876BCIiIhm1TcaUhhlTRETkITExdXHDDZFuu7/o6Lpuuy+5Xbt2YcyYMYp1165dky4bjUYMGzYMf/zxB86fP4+JEyfa3ce+ffsU1/fu3YvJkydDrVajbdu20Gq1dv179Ho9MjIypOv5+fk4e/asdP3q1atSuWBmZiYSEhKwdetW/PDDD9i+fTvWrFmD5ORkACWZJd26dZMypICSLKugoCAEBQXhq6++woQJE3D27Fls2bIF33//Pb799lun/aGCgoJQVFTk8LZ33nkHy5cvl647K+W77bbbcOTIERQUFEjrfvnlF2g0GrRs2RKFhYVo3LgxfvnlF8X/++WXX9C+fXvFugMHDjh8DLlXXnkF8+bNQ69evXD33Xdj9OjRePnll9GjRw/8+eef2LNnD0JDQ9GxY0d07doVu3fvxo8//ohp06YBKMmYeueddwDApdesffv2aNeunaK0UKVSQaPR4MYbb8SJEycAlOwLcnv37sWECRMAoMKvS/fu3RWN+XU6HVQqFR577DFp3XPPPYfPP//c6fOUk5OD2NhYACVZbX369MHixYuRkZGBTZs2OX+CZQwGA1atWoWRI0fiq6++QseOHdGmTRs8+OCDdn+rs78dAHr37o3p06ejVatWCAsLg1arlfbZwsJCdOjQAV999VWZY3nsscfQoEEDdOvWTbGflPeesSosLFSUX3oCA1NEREQyKvaYIiIiL0lOznRpu4AAjUtBp5SUTBiN5TfadvVxrfLz86XyLbnw8HDpcteuXQEA9erVQ7169RTBlvLUqVMHJpMJnTp1sgs45OXlSZeNRqPiNiGE4oTSyJEj8d///hcDBgzA0KFD8eabb6Jv377Yv38/6tSpg1mzZmH9+vV2j19UVIRLly6hZcuW6NOnD/r27YsPP/wQ//rXvxAXF2fXSBwoCTbVrev4NUlPT3f4fHlSfn6+S9tdu3YNa9euxdq1a/Hyyy/j0KFDmDJlCoYPH47s7GwcOXIE9957L7p06YIffvgBP/30E1avXo1bbrkFt956K3bv3g3AtdesTp06WLJkCf773//ajePChQsujbeir8uBAwfQoUMH6fqLL76Ixo0bY+rUqdK6lJSUMh/TYrEoXr+jR4+iX79+mDp1qhSYslgsdjM3BwQEKK4vXboUhw8fRuPGjTFixAjs3LnT5b8bKOkftmnTJnz00UeYMWMGrl27hu7du2PZsmXQ6XQoLCxEYWFhufdz6NAhxMbGYuTIkXYBzLLeM1b16tXzeHN8Hm0TERHJqFTsMUVERN5x152TXNquY8ebcDBxQbnb3TfwdRw65N2ACFDSS2j+/Pl45plnMHToUKxYsQJ9+vRR9Kqx7VHTuXNnnDp1ChaLBYcOHYJWq0WDBg2qPPvX4cOHcfjwYcyZMwe//vornnrqKezfvx+JiYlo2bJlmQGjoqIibNq0CZs2bcKiRYtw8uRJtG3bFocOHbLb9tChQ4iJiUFERITUCL6ijh8/juHDhyM4OFgK5HXr1g1msxknT55Ebm4uLl++LDUQt+rWrRt+++23Sj2mnNFoxJkzZxTNtHfv3o2ePXvirrvuwowZM5CZmYnjx49jxowZuHLlCk6dOgUA5b5m4eHhSExMxO23315ukK5z58521+W9uyryuhQVFSke79q1awgLC6tyoNBsNiMoKEi6npaWhoYNG0rXb775Zrum5H/++ScOHDiAZ555Bk899RTGjRtnd79l/e2dOnWCWq3G5MmTpffSkCFDFNv/8ccf6N27N1577TWnYz9z5gwmT56MH3/8EWazGS+88ILidmfvGaDkvR0UFOTwuXYnnzY/nzVrFoQQikW+A+r1enzwwQdIT09Hbm4u1q5dK6VqWjVp0gSbNm1Cfn4+UlJS8Pbbb9s1+oqLi8PBgwdRVFSEU6dOSc3L5MaOHYukpCQUFhZi3759uPPOOz3zRxMRkV+znZVPowtwsiUREVHtoNfrER0drVjq168PoKQE/rPPPsPWrVuxfPlyjBgxAu3atVPM7gYATZs2xXvvvYdbb70VTzzxBF544QVpdrVTp07hs88+w8qVK/Hwww+jefPmuPPOOzFt2jTcd999Lo2xefPm+M9//oPOnTujadOm6Nu3L2655Rbp9+Ubb7yB//u//8Orr76K22+/Ha1atcLQoUPx73//G0BJc/aRI0eidevWuPHGG/H000+joKAA58+fd/h4hw4dQnp6Orp161ap5xQA/ve//6GoqAgrVqxA69atce+992LhwoVYtWqV1Dj+nXfewdSpUzFkyBDceuutmD17Njp06FDhmekGDRqEVatWYdCgQVL20+TJk3Hfffdh48aN0nY//vgj+vfvD5PJhJMnT0rrhg0bJmVLAa69ZnPnzkXXrl2xcOFCtG/fHjfffDMefPBBu9nqunXrhn/961+45ZZbMHbsWDz++OPS31fR18UdVCqVtJ83b94czzzzDPr37694nnbu3Ilx48ahQ4cO6NSpExYvXozi4mK7+1q6dCmmTZsGlUqFr7/+2u72sv7206dPQ6fT4YUXXpD+9tGjRyv+/+zZs3HnnXdi0aJFaNu2LVq2bInRo0dL70+rU6dOoWfPnnj00Ucxf/58AOW/Z4CSBvhnzpxRlNB6SqWaiLljmTVrljh69KiIjo6Wlvr160u3f/jhh+L8+fOiZ8+eIjY2Vvz666/i559/lm5Xq9Xijz/+ENu2bRPt27cXAwYMEKmpqVKDPACiefPmIi8vT7z77ruiVatW4vnnnxdGo1H069dP2mbIkCGiqKhIDB8+XNx2221iyZIl4tq1ayIqKsrlv4XNz7l4euG+wsXVhftK1Za7H3lA0fy8RacOPh8TX3cu7l64v3BxtHC/8NxSVoNgV5YmTaJEfsG6Mhuf5xesE02auP77xdUlISFBOHL8+HERHh4uZs6cKS5fvizq1asn/Z+HH35YFBUViXbt2gmgpAHzBx98ID788EORlZUlMjIyxJtvvql4HK1WK1577TVx9uxZYTAYxOXLl8W6detEmzZtBFDSlDozM1Pxfx566CEhSlJJRIMGDcT69evF5cuXRVFRkUhKShKvvfaaUKlU0vb9+vUTP//8s8jPzxdZWVli3759YtSoUdJ97d27V2RlZYnc3Fzx66+/il69epX53MyZM0d8/vnninVJSUli/PjxTv+PbdPsNm3aiB07doiCggKRnp4ulixZIkJCQqTbVSqVePXVV8XFixeFwWAQhw4dEv3791fsW0II0b59+zLHeuONN4olS5aIEydOiPz8fHHt2jWxf/9+u6bedevWFWazWXzxxRd2z/Ozzz7r8mtmbYx/xx13iK1bt4qcnByRm5srDh8+rGjmnpSUJGbOnClWr14t8vLyxJUrVxTN4ivzusiXyjY/tyosLBQnTpwQ06dPF2q1WtquYcOGYsuWLSI3N1ecPHlSDBgwQNH83LqEhISIvLw8qVm97b5S1t8OQEyYMEFcvnxZ5Ofni82bN4unn35aCCGk5xeA6NGjh/j5559FYWGhuHbtmti8ebN0u7X5uXXbVq1aieTkZPHuu++69J7ZsmWLmDp1qtPny13Nz1HZDyh3LLNmzRKHDh1yeFtYWJgwGAzi0Ucflda1bNlSCCHE3XffLQCIAQMGKGYsACCee+45kZWVJQICAgRQ8mFx9OhRxX1/8cUXYvPmzdL1ffv2iYULFyre/JcuXSrzBbBdGJji4umF+woXVxfuK1VbOj8+WBGYurXLnT4fE193Lu5euL9wcbRwv/DcUtXAFFASnOrY8SaniyeCUuUt8h/HZS22P45ryhIdHS3S09NF06ZNfT4Wf1tc3TfKC+RV96VZs2bCZDKJjh07+nwsFV1uv/12kZycLMLCwsr8+9wRmPJpKR8A3HLLLbh8+TLOnDmDzz77DE2aNAFQUk+p0+mwfft2aduTJ0/i/Pnz6NKlCwCgS5cuOHr0qJTmCABbt25FeHg4WrduLW0jvw/rNtb7CAgIQKdOnRTbCCGwfft2aRsiIqo97GblC2CPKSIi8r2LF9Nw6NAZp8vFi55tTkz2UlJS8M9//hNNmzb19VDIz2i1WkRHR+PNN9/Evn37PN6jyRMaNmyI//u//0NOTo7HH8unzc/379+P4cOH4+TJk2jYsCFmzZqFPXv2oE2bNoiJiYHBYEB2drbi/6SkpCAmJgYAEBMTY9dR33q9vG3Cw8MRGBiIunXrQqvVOtymVatWTseu0+mg1+ul66GhoQBKphy17XFVFo1GA7VaXaH/Q7UT9xVyFfeVqtFolV+NOr2uWjyXfN2pIri/kCPcLzynJj6n1hnJVCqVosl5bSPvO0QluG+U9I768ccfcfLkSTz22GO+Hk6l7Nixw+VtHcVBKvK559PA1JYtW6TLR48exf79+3H+/HkMGTLEpWkPfWn69OkOO9/37dtXMaVpeTQaDWJjY6FSqeym2SSS475CruK+UjU3tGmtuN7prjvRRB/iZGv/wdedKoL7CznC/cJzIiMjERQUhNDQUISHh/t6OG5jOwuZM4MHDwaAGvW3U9lc3Tc6dOgAoObtG4cPH0ZERIR0vab9fVahoaEICgpCjx49kJ6erritTp06Lt+PTwNTtrKzs/H333/j5ptvxg8//AC9Xo/w8HBF1lR0dDSSk5MBAMnJybjrrrsU9xEdHS3dZv3Xuk6+TXZ2NoqKipCeng6TyeRwG+t9ODJ79mzMmzdPuh4aGorLly/jhx9+qNB0oRqNBkIIbNmyhQcAVCbuK+Qq7itV0yMyHLfc30+6/uexYzi4ebMPR+Qavu5UEdxfyBHuF57TrFkz9OrVC7m5uXYVIdWVNSsmJyen1mbFkGPcN2qPiIgIFBYW4qeffrKbKVEemCuPXwWmQkJCcNNNN2HVqlU4ePAgiouL0bt3b6xfvx4AcOutt6JZs2bYu3cvAGDv3r2YMWMGoqKikJZWUlPdt29fZGdn49ixY9I2tlOM9u3bV7oPo9GIgwcPonfv3lIapkqlQu/evfHBBx84HWtxcbHD6SDNZnOFv8gtFkul/h/VPtxXyFXcV6pApbyq1mqrzfPI150qgvsLOcL9wjNq4vNpDTgw8EC2uG/UPo6+NyryuefT5ufvvPMOevTogWbNmqFLly74+uuvYTab8cUXXyAnJweffvop5s2bh3vvvRexsbFISEjAr7/+iv379wMAtm3bhmPHjmHVqlVo164d+vXrhzfffBOLFi2SgkaLFy9GixYtMHfuXLRs2RJjxozBkCFDMH/+fGkc8+bNwzPPPIP/+7//Q6tWrfDRRx8hJCQECQkJPnleiIjId1RqZT28NsCvzuEQEVE1ZLFYAEDRo5aIqLqzfqZVNfju06PtG264AV988QXq16+PtLQ0/Pzzz+jcubNUmzhx4kRYLBasW7cOer0eW7duxdixY6X/b7FYcP/99+Ojjz7C3r17kZ+fjxUrVuDVV1+Vtjl37hwGDRqE+fPnY/z48bh06RJGjRqFbdu2SdusWbMGUVFReOONNxATE4PDhw9jwIABitn+iIiodlBrlOdstJyVj4iIqujq1asoKirC6NGjsWbNGqSmptaILKrQ0NAKletQ7cF9o2bTaDRo0KABhgwZgqKiojLbILnCp4GpJ598sszbDQYDxo0bh3Hjxjnd5sKFCxg0aFCZ97N7927ExsaWuc2iRYuwaNGiMrchIqKaT61WBqY0ugAfjYSIiGoKk8mEGTNm4JlnnlGcaK/ugoKC/H7SKvIN7hu1w4kTJzB79myYTKYq3Q/rE4iIiGRUGttSPgamiIio6tLS0jB79myEh4cjLCxMahBdXWk0GvTo0QM//fRTjcj+IvfhvlHzCSGQk5OD7Oxst/QSY2CKiIhIRqVW/lDQMmOKiIjcRAiBrKysCs3i7a80Gg3S09Nx/vx5Bh9IgfsGVZRPm58TERH5G7VN83ONjj2miIiIiIg8hYEpIiIiGdseUyzlIyIiIiLyHAamiIiIZFR2s/IxMEVERERE5CkMTBEREcnYl/IxMEVERERE5CkMTBEREcmobTOm2GOKiIiIiMhjGJgiIiKSUdn1mOIEtkREREREnsLAFBERkYxaw1n5iIiIiIi8hYEpIiIiGc7KR0RERETkPQxMERERyXBWPiIiIiIi72FgioiISIaz8hEREREReQ8DU0RERDKclY+IiIiIyHsYmCIiIpKxn5WPGVNERERERJ7CwBQREZGMbWBKw8AUEREREZHHaH09gJpKpVajRWx7hEVFIictHWcTj0BYLL4eFhERlcNuVj72mCIiIiIi8hgGpjygbe84DJ42EREx0dK6rOQUbJgzH0d37PbhyIiIqDzsMUVERERE5D0s5XOz2+O6IX7ebIQ3iFKsD28Qhfh5s9G2d5yPRkZERK5Q2c7KF8BzOEREREREnsLAlJvdN34sAGHXo6TkusBDUyfY3UZERP6DGVNERERERN7DCImbhUdHOQ08qdRq1G0Ygxax7b08KiIicpVdj6mAAKhUKh+NhoiIiIioZmNgygfCoiJ9PQQiInJCpdHYrePMfEREREREnsHAlA/kpKX7eghEROSEbcYUwD5TRERERESewsCUm2WnpEFYLA5vExYLMq8m42ziES+PioiIXKXS2H81ss8UEREREZFnMDDlZt+//yEAFYQQivUlwSoVNs5d4DRwRUREvqdW25fyaXUs5SMiIiIi8gQGptzs2O5fsGLSdFjMZsX6rJRUrJg0HUd37PbRyIiIyBWOS/kYmCIiIiIi8gQ2zfCAozt2I/NqMiKb3AAA2LF0JTYvXMJMKSKiasBhKR8DU0REREREHsGMKQ/RaEtjfjlp6QxKERFVEyoHGVPsMUVERERE5BkMTHmIPDAVWCfEhyMhIqKKcFTKxx5TRERERESewcCUhygDU3V8OBIiIqoItca++Tl7TBEREREReQYDUx6iZsYUEVG1xFI+IiIiIiLvYWDKQ+SNchmYIiKqPhyW8jFjioiIiIjIIxiY8hC1trQUJDCUpXxERNUFS/mIiIiIiLyHgSkPUfSYCmHGFBFRdeG4lI+BKSIiIiIiT2BgygPkQSmApXxERNWJWsMeU0RERERE3sLAlAdoApSBqSCW8hERVRtqtaNSPq2DLYmIiIiIqKoYmPIAtU3GlJ6lfERE1YaKGVNERERERF7DwJQH2Jby6UOCoVKpfDQaIiKqCM7KR0RERETkPQxMeYBtYEqtVkMfEuyj0RARUUU4mpWPGVNERERERJ7BwJQHOOpFwpn5iIiqB0ez8rHHFBERERGRZzAw5QG2GVMAEMgG6ERE1YJKbV96zYwpIiIiIiLPYGDKAxwGppgxRURULTialU+rY48pIiIiIiJPYGDKA2xn5QOAwFAGpoiIqgO1g1n5NGx+TkRERETkEQxMeYDDjKk6LOUjIqoOrD2mTMXF0jrOykdERERE5BkMTHmAw+bndZgxRURUHVhPLhQXFUnr2GOKiIiIiMgzGJjyAGZMERFVTypVaeNzY6FBusweU0REREREnsHAlAc4npWPGVNERP5OJesvZTSUBqbYY4qIiIiIyDMYmPIAh6V8nJWPiMjvyWfkU5byMTBFREREROQJDEx5AEv5iIiqJ/mMfMZCWWAqgD2miIiIiIg8gYEpD1A7DEwxY4qIyN9ZZ+QDbEv57D/XiYiIiIio6hiY8gDHGVMMTBER+Tu1prSUz1hcLF3mrHxERERERJ7BwJQHOG5+zlI+IiJ/p5ZlTAmzRcqaYo8pIiIiIiLPYGDKAxwGptj8nIjI78ln5bNYzDAVGwFwVj4iIiIiIk9hYMoD1I5m5WMpHxGR31OpZIEpswWm6+V8zJgiIiIiIvIMBqY8wFHGVBBL+YiI/J58Vj5hscBsMgHgrHxERERERJ7CwJQHOJq9SavTsRSEiMjPqdWlzc+FhRlTRERERESexsCUBzjKmAKYNUVE5O+UPaYs7DFFRERERORhDEx5gDwwVZibJ11mnykiIv8mn5XPYjbDbCwJTDFjioiIiIjIMxiY8gB5YCo/K0u6zMAUEZF/U2tsS/msgSn2mCIiIiIi8gQGpjxALQ9MZWZLlwPrsJSPiMifqdQ2s/IZi6Xrzsq0iYiIiIio8hiY8gBmTBERVU9qRY8pM8zXM6YAZk0REREREXkCA1MeoGHGFBFRtaSYlc9sgckoD0yxzxQRERERkbsxMOUBmgBmTBERVUeKUj5ZjykA0DBjioiIiIjI7RiY8gB5xlRBVo50mYEpIiL/pijlM5thLi7tMaUNYI8pIiIiIiJ3Y2DKA5xnTLGUj4jInylK+SwWmIwm6Tp7TBERERERuR9P/3qAsvm5rMdUKDOmXNGkSRQiI8Oc3p6enoOLF9O8OCIiqi1UmjJm5QtgjykiIiIiIndjYMoD1M4CUyEMTJWnSZMonDi5GEFBzjMTCguL0arlaAaniMjt5D2mhO2sfAxMERERERG5HUv5PEA5K1+WdDkwlKV85YmMDCszKAUAQUG6MjOqiIgqS622zZjirHxERERERJ7EwJQHKJufM2OKiKi6kDc/F0IoZuVjjykiIiIiIvdjYMoDNNrS5rlGgwGGgkIAnJWPiMjfqWTNz21n5dMwY4qIiIiIyO0YmPIAeYNcs8mEorw8AEAQS/mIiPyaopTPYlPKxx5TRERERERu5zeBqalTp0IIgfnz50vr9Ho9PvjgA6SnpyM3Nxdr165FgwYNFP+vSZMm2LRpE/Lz85GSkoK3334bGo1GsU1cXBwOHjyIoqIinDp1CvHx8XaPP3bsWCQlJaGwsBD79u3DnXfeWem/RV7KZzaZYMgvKPl7WMpHROTXFKV8tj2mGJgiIiIiInI7vwhM3XHHHXjuuedw5MgRxfr58+fjgQcewOOPP464uDg0atQI69evl25Xq9X47rvvoNPp0LVrV8THx2P48OF44403pG2aN2+O7777Drt27UKHDh2wYMECLF26FP369ZO2GTJkCObNm4fXX38dsbGxOHLkCLZu3YqoqKhK/T3yWfksJjMKc0sypvQhwVCpVJW6TyIi8jxFKZ/NrHwa9pgiIiIiInI7nwemQkJC8L///Q/PPPMMMjMzpfVhYWH45z//iUmTJmHXrl1ITEzEiBEj0K1bN9x9990AgH79+uH222/H008/jSNHjmDLli2YOXMmnn/+eQRcP7M9evRoJCUlYcqUKThx4gQWLVqEtWvXYuLEidJjTZo0CZ988gmWL1+O48ePY/To0SgoKMDIkSMr9TdZM6bMRhMASKV8arUa+pDgSt0nERF5nl3GlKzHlDZA6+i/EBERERFRFfg8MLVo0SJ899132LFjh2J9p06doNPpsH37dmndyZMncf78eXTp0gUA0KVLFxw9ehSpqanSNlu3bkV4eDhat24tbSO/D+s21vsICAhAp06dFNsIIbB9+3Zpm4rSXP/xYjZZA1P50m2cma9s6ek5KCwsLnObwsJipKfneGlERFSb2PeYMknXOSsfEREREZH7+fT079ChQxEbG+uwn1NMTAwMBgOys7MV61NSUhATEyNtk5KSYne79baytgkPD0dgYCDq1q0LrVbrcJtWrVo5HbtOp4Ner5euh4aGAgA0Gk1pxpTJCI1GI/WYAoDg8DDkpmdI1zUaDdRqtV1frNrqypVraH37WERGhuGXX+dKmW/du01F8fWSmvT0HFy5cq3WPWfcV8hV3FcqTz55BYSAMJUGpgL0euk5VanVuDG2PcIi6yMnPQNJiSWl6LbrhMXivbHzdacK4P5CjnC/oIrg/kLOcN8gABV6/X0WmLrhhhvw/vvvo2/fvjAYDL4aRqVNnz4dr732mt36vn37IjQsDACgUakxcOBANJT1qurZtw+yb70kXddoNIiNjYVKpYLZbPb4uKsLlQpSUAoAGjduicLCksBUTAzQpo2vRuY73FfIVdxXKi+6Y+mHy22tWsGQkytdv71Na9QZOBCRrVvilvv7ITAiTLqt+PoJCJ2sXLsoKwenNm1D+l8nvTByvu5UMdxfyBHuF1QR3F/IGe4bBAB16tRxeVufBaY6deqE6OhoJCYmlg5Gq0WPHj0wbtw49O/fH3q9HuHh4YqsqejoaCQnJwMAkpOTcddddynuNzo6WrrN+q91nXyb7OxsFBUVIT09HSaTyeE21vtwZPbs2Zg3b550PTQ0FJcvX8YPP/yAm0c+gSAARQWF2Lx5M/o2a4gm3Uv6Yh06ehQnf9kn/T+NRgMhBLZs2cI3rYxeHwDgOen6zp07kJWV7/w/1ALcV8hV3Fcqr1MAcPuQhwAAfx49isyrKWj7j8cBAGeSkpBsyMe9Tz0KQCj+X0BwkN196cPqoM1Tj2LVlBn4c+dPHh87X3eqCO4v5Aj3C6oI7i/kDPcNAoCIiAiXt/VZYGrHjh1oY5P2kpCQgBMnTmDu3Lm4ePEiiouL0bt3b2kmvltvvRXNmjXD3r17AQB79+7FjBkzEBUVhbS0NAAlGUvZ2dk4duyYtM19992neJy+fftK92E0GnHw4EH07t0bGzduBACoVCr07t0bH3zwgdPxFxcXo7jYvheS2WyWekyZTEaYzWYUyM6464KD7N6cFosFZrOZb1oZrVavuK5Sgc8PuK+Q67ivVJIs3mQymWCUZfSqAwLw4EvjAQio1MoWjY5mXFWp1RAWCx7414v4Y8dur5T18XWniuD+Qo5wv6CK4P5CznDfoIq89j4LTOXl5eGvv/5SrMvPz0dGRoa0/tNPP8W8efNw7do15OTkYOHChfj111+xf/9+AMC2bdtw7NgxrFq1Ci+99BJiYmLw5ptvYtGiRVLQaPHixRg3bhzmzp2LZcuWoVevXhgyZAgGDRokPe68efOwYsUKHDhwAL/99hsmTJiAkJAQJCQkVOpvs/aYsphKXghF8/M6bH7uipKMqVJaLeuTicjzVGXMyhd5QyNExEQ7+m/O70+tRt2GMWgR2x5nDhxy2ziJiIiIiGoKv577euLEibBYLFi3bh30ej22bt2KsWPHSrdbLBbcf//9+Oijj7B3717k5+djxYoVePXVV6Vtzp07h0GDBmH+/PkYP348Ll26hFGjRmHbtm3SNmvWrEFUVBTeeOMNxMTE4PDhwxgwYIBitr+KKG1+bp2VL0+6LbACdZa1GQNTROQLanXpZ40QFpiuT7oAAIGhlf/8DouKrNK4iIiIiIhqKr8KTPXs2VNx3WAwYNy4cRg3bpzT/3PhwgVF9pMju3fvRmxsbJnbLFq0CIsWLXJ9sGUoMzAVyowpVzAwRUS+oJZlTFnMFpiMpYEps2yGvorKSUuv0riIiIiIiGoqdfmbUEWprwdRzNd/0ChK+UIYmHKFfWCKuyoReZ68d5TFYpE+xwGgICsHWckpFeoVJSwWZF5NxtnEI24dJxERERFRTcFf+x5gnzEl7zHlH6V8KrUaN93RER0H9sVNd3S0a+Tra8yYIiJfUGtkpXxms6LHlDZAiw1z5gNQQQjlrHxCCPt1FgsAFTbOXeCVxudERERERNWRX5Xy1QTWoBTgODAV5AelfG17x2HwtImKJr5ZySnYMGc+ju7Y7cORldLrlbsmA1NE5A1qm4wpk7G0fE+j0+Hojt1YMWk6nnhzpmIyi/ysLOiDghEQWDqjaFZKKjbOXeA3n6tERERERP7Iv9JkagBNQGmmj8VonZWvtMeU3selfG17xyF+3myEN4hSrA9vEIX4ebPRtnecj0amxIwpIvIFlU2PKbM8Y0pX8rl0dMduHPxui7T+q9fn4LV778eez9dI6zbMmY+3BjzKoBQRERERUTkYmHIzRxlTxYVF0uWqzOpUVSq1GoOnTQQg7Er3Sq4LPDR1gl+U9TEwRUS+oJiVz2JWzMqn1emkyyHh4dLlv/f+BmGxwCzLrko5m8TyPSIiIiIiF/g+AlHDyGd0ks/gZMgvAODb5uctYtsjIibaaeBJpVajbsMYtIht7+WR2WPzcyLyBduMKXmPKU1A6YmH4IjSwFR+VjYAKGbw02iVn2FEREREROQYf+27mfyHizwwVZhbUs4n70nibWFRkW7dzpOYMUVEvmDbY0r+Oa4NKM2YCg4LAwCYjSbpxINFtq38u4CIiIiIiJxjYMrN1PJSPtnZc2ufqSAflvLlpKW7dTtPYmCKiHxBMSufpaRPoDVrytpjCgCCI0oCU/nZ2dI6eSmfvN8gERERERE5x8CUmznqMQUARfklM/NpdTqf/WA5m3gEWckpTvueCIsFmVeTcTbxiJdHZo+BKSLyBXmps8Vc8llp7TMl/+wOuV7KV5AlC0yZ5KV8/MwiIiIiInIFA1NuptaUBqYsJrN0uSg3X7rsq6wpYbFgw5z5AFQQQtjdBqiwce4Cv2jYy8AUEfmCWq2SLluufxZas1+tGVOagADog4MB2GZMlX7mM2OKiIiIiMg1DEy5mfwsubL5eWlgypd9po7u2I0Vk6YrxgMAWSmpWDFput9Mbc7m50TkCyr5rHxmaymfNTBV0mMqRNb4vCArR7psVjQ/Z48pIiIiIiJX8MjZzcprfg74NjAFlASnmndsj3vjnwQAnNz7Gz4ZPdEvMqWsmDFFRL4gn1lVWEoyS03Gkh5T1mCTfEa+gmxZYEre/JyBKaJqQaVWo0Vse4RFRSInLR1nE4/41fEQERFRbcAjZzdz3mOqQLrcolNHXD5xyqcHPrpAvXS5KDfP7w7CAgMZmCIi71P0mLI4yZgKD5O2UfaYYvNzouqkbe84DJ42EREx0dK6rOQUbJgz328yyImIiGoD1ke5mXxWPsv1GZra9o5D1yEPS+sfemk8Xtm6Hm17x3l9fFa6oCDpsj9Oa86MKSLyBfmsfNbm57Y9poJlgSlljyk2PyeqLtr2jkP8vNkIbxClWB/eIArx82b79BiNiIiotmFgys3kP2rMJpN04GNbvmc98GnTq4e3hwgA0AWXBqa0ATqfjKEsDEwRkS+o1fJSPuWsfAH6kkxTRSmfImOKzc+JqgOVWo3B0yYCEIosSettgMBDUyfY3UZERESewW9cN7Mt5ZMOfFQqxXbWA58H/jUesLnNG/RBgdLl6pExxV2ViDxPpXFUylcsrVNrNYrm5/lsfk5U7bSIbY+ImGingSeVWo26DWPQIra9l0dGRERUO/HI2c3k5RvhDRoo+hbYKjnwiUZE8ybeGJqCvJTP2jfFnzBjioh8Qa12VMpX2jtKG6BDcLi8+bnjHlNqBqaI/FZYVKRbtyMiIqKqYRqKm8l/jAQE6cvYspQurI6nhuP8Mf28x5SOgSki8gHlrHzXS/mMpRlTWl2ATcaU48CUlqV8RH4rJy3drdsRERFR1TAw5WYaTWmQpzA716X/U5yT56nhOKWTlfIxY4qIqIRiVj6zclY+oKR3VHCEk1n5ZKV8an5mEfmts4lHkJWc4nRGYmGxIPNqMs4mHvHyyIiIiGonBqbcTJ59lHruvAsHPinIOnfRW8OTKJuf+9+Zfb1emcXFwBQReYNiVj6LclY+4HrGlKKUT9ZjihlTRNWCsFiwYc58ACoIIexuA1TYOHeB0+M3IiIici8GptxMfpbcbDSWHvjYHNxYD3y+fed9wOagyBuUpXz+9wOKzc+JyBcUs/KZlbPyASUZptZZ+Yry8xXBKHkvKjY/J/JvR3fsxopJ02HIL1Csz0pJxYpJ03F0x24fjYyIiKj24a99N7Odlc964JObcU2xnfXA58+dP3l7iABsSvmqRWCKGVNE5HmKUj4Hs/JpdQEIDi8p5SuQzcgHsPk5UXVzdMdu7Fv3jXT90Pc/4K0BjzIoRURE5GU8cnYz+Y8Ra/nH0R27cf6PvzBr57cAgKTEw1g04nkIiwUajfcDLpqAAEUATatjYIqICLDtMWVtfi7LmArQITisJDCVL5uRD7At5ePXK1F1IH+v5mZcY/keERGRDzBjys3kzc/NJrN0ubioSLpsKCjy6YGPvIwPqC6lfAxMEZHnOZqVT95jKqRuuNRLUN743HY7ZkwRVQ/yYyB/PFFHRERUGzAw5WYaRY+p0rPnyh4lvj3w0QcrA1O+Ho8jDEwRkS+o1aWfNdbAlPzzOywqSrpsF5hi83Oiakd+DOSPsxQTERHVBgxMuZnapseUdFneo0Tv2wMfeX8pwD8PxNj8nIh8QaWR95iyBqZKP7/DGkRKl/OzbXpMyU5GqFnKR1QtyDOmNHzfEhER+QR/7buZJkA21bgsMCWEkH7cBOj0Xh+XnG0pH+B/M0gxY4qIfEGt6DFVUo4tDziFRdaXLtuX8nFWPqLqRhvAjCkiIiJfY2DKzdSa0gMcecYUUFoO4vOMqWAHgSk/KzvR65U/6hiYIiJvcFzKV5oxFS7LmCqwzZiSfeYz84KoepAHo1iCS0RE5BsMTLmZoseUTWDKaDAA8H1PJ9tSPsD3Y7LFjCki8gVFKZ+DWfnkPaZsZ+WTZ8kyY4qoepAHkf3tJB0REVFtwcCUm8kPcOQzNAGlZ919nSquDw62W+dvB2MMTBGRLyhK+SwlpXyKHlNRzkv5hBDSCQl/+0wlIscUGVN+dpKOiIiotmBgys2cNT8HAJPheo8pva97TFXHjCnuqkTkeWqNrJTvesaUvHdUaP3SwFR+lrKUT74tM6aIqgf5CUVfnzgkIiKqrfhr3800Gnkpn1lxm7UcxNdn0h02P/ejs/tarUaRtQAoSySJiDxFJfvsEUIAUGZMyX/E2mZMAaUnJBiYIqoetAGlwSj2hiMiIvINBqbcTP5jxOKkx1SAr5ufO8yY8p+zhLbZUgBL+YjIO9TXe0zJM16tE1fYsu0xBZR+7vMHLlH1IM8Y96djISIiotqEgSk3U8ubnxsdl/KpNRrFdt7mKGNK60c/ohiYIiJfsc7KZ52RD7DvFwiUBK6KcvPs1kuZscyYIqoW5BnjnJWPiIjINxiYcjP5AY5djylZOUiAznd9pvTBjgJT/nOWkIEpIvIVlVoFoHRGPkA5K59VYU6uw//P5udE1Ys8Y4rvWyIiIt9gYMrN1IoeU7aBqdIfN75sNu64x5T/nN13HJjirkpEnmftMWWdkQ9QnlSwKsi2b3wOsPk5UXXDWfmIiIh8j7/23cyVHlMAoPVhnyn2mCIicsx6ckGYyy7ly3fQ+Bxg83Oi6oaz8hEREfkeA1NuJv8xYlv+IT/rrvVhKZ+/z8qn19v/oGNgioi8wTojqHVGPsBx83NHM/IBbH5OVN1wVj4iIiLfY2DKzTTy5ucms+I2fynlc9hjyo/S15kxRUS+oro+K5/FXHYpn6MZ+QA2PyeqbhQZU37Ub5OIiKg2YWDKzdRllPIpmp/7tJTP3zOmGJgiIt+wzspnsZTd/Lwgy3GPKcv1ExJqjUbqV0VE/kmlViuCyJoALVQqlQ9HREREVDvxqNnN1BpZYMqszJhS9JjyaSmfox5T/h6Y4q5KRJ6nvp4xVdkeU/IgFrOmiPybo2MfDftMEREReR1/7buZtZTP0Rl2k4EZU65gxhQR+YrjWfkcZEw5KeWTZ8oyMEXk3xwd+2jZZ4qIiMjrGJhyM2uvAuuU4XKKM+k+zFBymDHFwBQRkTQrn0WWMeWox1RBtuNSPrM8MMUfuER+zVHGFGfmIyIi8j4GptzMeobctr8UAJhkpXwBPjzw0Tlqfs7AFBFR6ax8Fnkpn/3nubNSPjNL+YiqDa3WUcaU/xwPERER1RYMTLmZtceUo1I+o6yUT6v3ZY8pB6V8ft9jioEpIvI8R7PyOeox5ayUTz4bqz+VSBORPUf9pPi+JSIi8j4GptzM2mPKYjLb3SYvB/FVs3GtXi9lBCjW+9GBGJufE5GvOJqVTwhhd7Ih38msfMyYIqo+HJfy+c/xEBERUW3BX/tupr7+Q8TsqJRP1kDXVz0M9LL+UoaCAumyPx2IMWOKiHxF5aCUD7DvM1XgrJSPPaaIqg1HJ+XYY4qIiMj7GJhyM/X1AIrDwJS8x5SPSvnkZXzy5r3+lLqu19v/mGNgioi8wZpRKi/lA5R9pgwFhQ4bottux8AUkX9z1MaA71siIiLvY2DKzTRlZEwZFaV8vjkjJ298Lg9M+dMZQmZMEZGvSD2mysiYctZfCrDJmGIpH5FfY8YUERGRf2Bgys00mjJm5VOU8vkmQ8lpxpQf/YBijyki8hVHs/IBys/vAif9pQDbjCn/yUQlInvsMUVEROQf+Gvfzawp4OWX8vkoY0rWY6owJ1e67E9nCJkxRUS+otaUfNYIszIwJW9qLg/q22Lzc6LqQxPgYFY+LQNTRERE3sbAlJtZf9SUX8rnbz2m/OcHFANTROQLKtmMpXalfLKAUz5L+YhqBK2DYx9mTBEREXkfj5o9RF7OYWUyyAJTPsqY0jNjiojIIbUiMKVsfq7oMeVkRj6As/JR7aZSq9Eitj3CoiKRk5aOs4lH7Mpi/YmjYx9/Oh4iIiKqLXjU7CEOS/kUGVM+6jEVHCxdLsjxz4wpnSwwVVhoQFCQnoEpIvI4lab0c8a+lK/0M10XHASVWu3wB7eixxQzpqgWads7DoOnTURETLS0Lis5BRvmzMfRHbt9ODLnHPWBY284IiIi72Mpn4eUF5gK8NWsfLKMqeowK19+fklfLjY/JyJPUzsp5WvbOw433N5Kun7HAwPxytb1aNs7zu4+lBlT/IFLtUPb3nGInzcb4Q2iFOvDG0Qhft5sh+8Vf8Dm50RERP6Bv/Y9xOKglM+oKOVjjylnlIGpIgCARqOBSqXy1ZCIqBZQa0q/EoW5pJTP+oPb9seqsx/cbH5OtY1KrcbgaRMBCEWfNuttgMBDUyfY3eYPGJgiIiLyD/53lFBD+G8pn6zHVG6edFnrYGYaX3EUmAIAjYa7KxF5jkpdWspnsViUP7htAuPOfnDLP/vVDExRLdAitj0iYqKdBp5UajXqNoxBi9j2Xh5Z+RzNwOdPx0NERES1BX/pe0j5gSlflfKVZkwVFxZKY9L6UcmJXl/6Y85aygewAToReZY8Y8pitlTqB7f8s9/RjF9ENU1YVKRbt/MmR8diLMElIiLyPgamPMRxYKq0xCPAR6V8ellgylBQKI1J40ep684yphiYIiJPkveYEhZzpX5wy5ufM2OKaoOctHS3budNjtoYsJSPiIjI+yp81HzzzTfjoYceQvPmzSGEQFJSEjZs2ICkpCRPjK/aMjvoMSUsFpiMRmgDAnyXMRWszJiy9kPxr4wp+ax8siwzNkAnIg+Sz8pnMVsq9YNbmTHlP5+rRJ5yNvEIspJTEN4gymF2obBYkJWSirOJR3wwurI5OhZjYIqIiMj7KvRLf9q0aTh27Bjmzp2LRx99FI8//jjeeecdnDhxApMnT/bUGKslRxlTQGk5n896TMlm5Ssp5TP6dDyOWANTRUXFMMoCfMyYIiJPUsv6SFksFukHt5DN0CcnLBZkXk1W/OBm83OqbYTFgg1z5gNQQQhhdxugwsa5C5y+j3zJ0bEPS/mIiIi8z+XA1L333os333wTb731FiIjI9GoUSM0bNgQUVFRmDNnDubMmYN77rnHk2OtVizOAlPXZ+bT6v2gx1RBEUzXf0T504GYNTBlMBhhMpml9QxMEZEnqWxm5VP84Lb5Ue3sBzebn1NtdHTHbqyYNB3FhUWK9VkpqVgxaTqO7tjto5GVzdGxj68y2omIiGozlwNTo0ePxtKlS/H6668jKytLWp+ZmYlZs2Zh2bJlGDNmjCfGWC2VlzEVoPNNjyl5xpSxqMivS/lKAlOlP/gYmCIiT1LLZuWzZn5Yf3Bnp6YptnX2g5vNz6m2OrpjN/76cY90/fjPe/HWgEf9NigFOD728afjISIiotrC5aPmu+66C//4xz+c3r5q1SqsXLnSLYOqCZwFpowG35by6YODAZQ0PhdCMGOKiOg6lc2sfFZHd+zGn7v2oEVse4RFRSInLR1nE484LE1SND9nYIpqGfkEK0U5uX5ZvifHWfmIiIj8g8tHzdHR0Th37pzT25OSkhATE+OOMdUI5faY8lkpX0nGVHFhoXI8fthjyj4wxebnViq12qUfyUTkOvmsfBaLWXGbsFhw5sChcu9DkTGl9Z/PVSJv0IcES5e1Ppp9uCI4Kx8REZF/cDkwFRgYiOLiYqe3G41G6FiXL7E4mJUPkAeCfNtjytoHwnp2X63RQKVW+0VwQ68v2S0NBhPMzJiy06ZXDzz40nhExERL67KSU7Bhzny/Lpkg8ndq2ax8wly5z0J583NmTFFtIw9MBVSDwBRn5SMiIvIPFTpqHjVqFPLy8hzeFhoa6pYB1RQmZ4Gp66V8Gq1W8SPIW5xlTAElB2PGIoPXx2SLpXzORbZuiXufehSAcuaj8AZRiJ8326+bzBL5O5UiY6qyganSz37Oyke1jbVdAOC7zPCKcJQxxVI+IiIi73P5qPnChQt45plnyt2GSjiblc9oEwgyFxsdbucJKpUKAYHWwJQyYwooORjzdWBKpVJBp2Pzc0dUajVuub8fAKH4AW29TVgseGjqBPy5a49fZL4RVTdqRY8pcxlbOicv5XP0o5eoJpMHpgKqQRa9w4ypAP8fNxERUU3j8lHzjTfe6Mlx1Djl9ZgCSg6IvBmYCgjUSz1UiguuZ0wZlYEyX9PpSndJZkwp3RjbHoERYU5vV6nVqNswBi1i27vUC4eIlBSz8lU6Y6r0M50ZU1TbKHtM+X+Ax+GsfH5wLERERFTbsJu0hzgNTBlkgSAv91/QyWbLsZbyyTOm/KFRr7WMD2Dzc1thkfVd2y4q0sMjIaqZnM3KVxGKvngsCaJaRhdcepxRHXpMycv2rOW7LOUjIiLyPpd/6Xfu3BmDBg1SrPvHP/6Bs2fPIiUlBUuWLKlw8/PRo0fjyJEjyM7ORnZ2Nn799VcMGDBAul2v1+ODDz5Aeno6cnNzsXbtWjRo0EBxH02aNMGmTZuQn5+PlJQUvP3229DY9G6Ki4vDwYMHUVRUhFOnTiE+Pt5uLGPHjkVSUhIKCwuxb98+3HnnnRX6W2y5ljHl3YMfa38pwHGPKY0fpN2XHZiq3RlTOekZrm2Xlu7hkRDVTPKMKdtZ+VylaH7OjCmqRXRBQYqZLatFxtT14zCjwSBlsDNjioiIyPtcDky9+uqraN26tXS9TZs2+PTTT7F9+3bMmTMHDzzwAKZPn16hB7906RKmTZuGTp064Y477sDOnTuxceNG3H777QCA+fPn44EHHsDjjz+OuLg4NGrUCOvXry8dvFqN7777DjqdDl27dkV8fDyGDx+ON954Q9qmefPm+O6777Br1y506NABCxYswNKlS9GvXz9pmyFDhmDevHl4/fXXERsbiyNHjmDr1q2Iioqq0N8j56zHlElWuuft/gvKjCn7HlNaP+iHwsCUc0mJR1CUleO0xEhYLMi8moyziUe8PDKimkHeY6rSs/KZ2Pycaid5GR9QvTKmTMVGn8+aTEREVJu5HJjq0KEDduzYIV1/4oknsH//fjz77LOYP38+XnzxRQwZMqRCD75p0yZs3rwZp0+fxqlTp/DKK68gLy8PnTt3RlhYGP75z39i0qRJ2LVrFxITEzFixAh069YNd999NwCgX79+uP322/H000/jyJEj2LJlC2bOnInnn38eAdcPNkaPHo2kpCRMmTIFJ06cwKJFi7B27VpMnDhRGsekSZPwySefYPny5Th+/DhGjx6NgoICjBw5skJ/j5yzjCmjobS5uLfPJsozpgwOZ+Xz/cGYfWCKzc+thMWCU5u2AVDZBadKrquwce4CNj4nqiR3zMpnYfNzqqX0sjI+wD+OKcpjzY4yG40wXc925PuWiIjI+1z+9q1bty5SUlKk63Fxcdi8ebN0/ffff0eTJk0qPRC1Wo3HH38cISEh2Lt3Lzp16gSdToft27dL25w8eRLnz59Hly5dsH//fnTp0gVHjx5FamqqtM3WrVuxePFitG7dGocPH0aXLl0U92HdZsGCBQCAgIAAdOrUCbNnz5ZuF0Jg+/bt6NKli9Px6nQ66GVnA0NDQxW3C4uwKykElGUe+qAgaDQaqNVqh9u6W1CdOtJlY5EBGo1GMfNUgF7vlXGUJTi49Dk1Fpthlv041OkDfD4+X9JoNLh2/BT+99JMPPDSeIQ3KM3oy05Nwzdvv49jP/5cq58jKuHNz5WaRCvPcBKOP8PLIw8MawO8+5nF150qwt37S7DNcVBAoO+PKcpjDZ6ZjEZACGmdv4/bk/g5QhXB/YWc4b5BACr0+rscmEpJScGNN96IS5cuISAgALGxsZg1a5Z0e2hoKIzGis8w16ZNG+zduxeBgYHIy8vDww8/jOPHj6NDhw4wGAzIzs62G0dMTAwAICYmRhEss95uva2sbcLDwxEYGIi6detCq9U63KZVq1ZOxz19+nS89tprTm9v37YtGmvs09ib3VAavOvavTtymzRHbGwsVCoVzJWcntxV9W+7Rbp8Y5MmGDhwIJo2vkFa1+2e7shu0syjYyhPi5tKG3dHxzSEVls6C93dd90FtSraF8PyCxqNpmRfSUzEn0s/R7eXx0u3/bF4FZroQ9Bk4EAfjpD8hbSveOFzpSape0vp7LMtWrTAwEq8n+R9paKioyt1H5XF193/REbVQVhYoNPbc3KKkJ6W58URlXL3/hJxY1PFdW1AAAbed58U8PFHwSEhAAC9NgACJeMMCgnx6vvW3/BzhCqC+ws5w32DAKCOLDGmPC4Hpr7//nvMmTMHU6dOxeDBg1FQUIA9e/ZIt7dr1w5nzpyp2EhRkgXVoUMHhIeH47HHHsOKFSsQFxdX4fvxttmzZ2PevHnS9dDQUFy+fFm6/vtvv+HEnr12/69P0xg069kNAJB4+DBO7/sdQghs2bLF42/aDsKIdtcvHz3yB37ZvBn9W9yApnElmWEHEhNxev8Bj46hPJ07twTwKADg1KkzuHo1E8BdAIAjRw5h8+b9vhucj2k0GmlfCYtugG4oDUzt2v0j8jOzfDc48ivyfYUHA65rmdMZHUY+BQA49fff2C7LCnaVSq1G3L+nAgCyc7IVmcWextfdvzRpEom/jn2IoCDnJW2FhcVofftYXLzo/Ukr3L2/3HZPV3S0Wbd95w4YiwwOt/cHna+f4MnJzoYQAsH160EAXn3f+ht+jlBFcH8hZ7hvEABERES4vK3LgamZM2di/fr12L17N/Ly8hAfH6/IkBo5ciS2bdtWoYECgNFolAJaiYmJuPPOOzF+/HisXr0aer0e4eHhiqyp6OhoJCcnAwCSk5Nx1113Ke4vOjpaus36r3WdfJvs7GwUFRUhPT0dJpPJ4TbW+3CkuLgYxbL+THZ/l6HY4ZuwuKhIuqzWamE2m2GxWGA2mz3+ppX3tDLk58NsNqNY1vNKrdH4/INDqy3t8VJUWAyjrFm8Ws2Iu3VfUdv02/KH1478i7c+V2oSeV6H2WSq3HN3/TNdrVZDrdF6/fnn6+4/6tatU2ZQCgCCgnSoW7cOzp1LKXM7T3Hn/hIQZJ8ZptJoYTYXVPm+PUUrNT8vlj4ANAHef9/6G36OUEVwfyFnuG9QRV57l5ufZ2RkIC4uDnXr1kXdunWxYcMGxe2PP/44Xn/9dZcf2OmA1Gro9XocPHgQxcXF6N27t3TbrbfeimbNmmHv3pJMpL1796Jt27aK2fP69u2L7OxsHDt2TNpGfh/Wbaz3YTQacfDgQcU2KpUKvXv3lrapDGfNz00GebNx705J7HhWvtLAj3V2Gl8KDJQFz9j83KmAQGWZaHWYlpvI38ln5bNUclY+oLQBOpsoU22is2l+Dth/V/kbjdT83MRZ+YiIiHyowkfNOTk5DtdnZmZW+MH/85//YPPmzbhw4QJCQ0Px1FNP4d5770X//v2Rk5ODTz/9FPPmzcO1a9eQk5ODhQsX4tdff8X+/SXlXNu2bcOxY8ewatUqvPTSS4iJicGbb76JRYsWSdlMixcvxrhx4zB37lwsW7YMvXr1wpAhQzBo0CBpHPPmzcOKFStw4MAB/Pbbb5gwYQJCQkKQkJBQ4b/JyuJsVj5ZllWAt2flkx00Ggqss/KVBqa8HShzxH5WvtIoKwNTpQL0yjPT2mowLTeRv1PLZuUTlsqf3TMZjSUNlLUMTFHtoQ8JtlsX4MdBHrVWI73nTcXFELJeWJqAAMWJOyIiIvIsl4+aExMTFV/aVtnZ2fj777+xYMECnDhxokIP3qBBA6xcuRINGzZEdnY2/vjjD/Tv31+aRW/ixImwWCxYt24d9Ho9tm7dirFjx0r/32Kx4P7778dHH32EvXv3Ij8/HytWrMCrr74qbXPu3DkMGjQI8+fPx/jx43Hp0iWMGjVKUXa4Zs0aREVF4Y033kBMTAwOHz6MAQMGKGb7qyhnGVPmYnnGlHeDCcqMqZLAlL9lTJUdmHI5wa/Gsw1qBjAwRVRlKtnMIVXLmCr53PKHz1QibwkMtg9M+XM2r1b2/pTPyme9jYEpIiIi73E5MGVbumcVERGB2NhYHD58GL169cKvv/7q8oOPGjWqzNsNBgPGjRuHcePGOd3mwoULiuwnR3bv3o3Y2Ngyt1m0aBEWLVpU5jYV4SwwZZSX8nk7Y0rW/8EamGLGVPVkmyHl7ew7oppInjFlsVQ+MGW6/oOWGVNUm+ivz3An58/fTZqA0rGZjUYIiywwpQuAwX9bYxEREdU4Lh81v/HGG2Xe/uabb+KNN95Anz59qjyomsBsdNJjqth3Pab05fSY0vrB2X0Gplxj12PKj8sliKoLeY+pqpTyST2mGJiiWsRRKZ+3M8MrQivrAWcqNkLIgtEafqcSERF5ldtqoz7//HO0bdvWXXdX7bmSMeXt3gsOM6bkpXx+kTFVeqDI5ufO2e47/nxWmqi6UKndU8pnZvNzqoX01az5ufyEjtlotDlRx/cuERGRN7ktMGU2mxVlELWd01n55BlTXu4LpJP1f7A2P2fGVPVkPyuf/x78E1UXiln5qlDKZ82YZcZU7ZaenoPCwuIytyksLEZ6uuNJZaobvaMeU36ceSQ/GWcqNipO1PnzuImIiGoitx01P/LIIzh27Ji77q7as7hSyucXPaZk4/G7wJQJarVKus7m56Vsm53zIJqo6hSz8pkrX8pXmjHl+89U8p2LF9PQquVo9OjRGqs+myytf2XGSmzenAigJHh18WKar4boVg5n5fPjbF5F83MHs/IRERGR97gcmHrhhRccrg8PD0enTp0waNAgDBw40G0Dq+6cZkwZfBcIss7KZ7FYYCwyAFD2wvKHAzHbjKmAgNIsKWZMlbINavpzuQRRdaFi83Nys4sX05CRkatYl51dgEOHzvhoRJ7jsMeUPwemZBlTZpNJ0WPKH07UERER1SYuHzVPnDjR4fqcnBycPHkSPXr0wL59+9w2sOrOlVI+26wXT7NmTBmLihyOxx+ybmwDU6rShCkGpmRs9x1v9ysjqokUgakq9JiyZsyyxxQBQP36oYrrdevW8dFIPMtRKZ+3j3MqQmObMWUzKx8RERF5j8tHzS1atPDkOGocp83PfVjKZz1otM7IBwAmRcaU739E2Qam5MEoBqZK2feYYmCKqKrUmtLPmKrMyif//NdotU6/D6h2iIwMU1yvXYEp//1ukmdFmY1GRTDaH07UERER1Sa+j0TUUGZnPaZ8WspXkjFl7S8F+H/GlPw6A1OlbF8rZkwRVZ2ix5Qse6KizDYBf08EplRqNVrEtkdYVCRy0tJxNvGI2x+D3MM2MBVRUwNTjkr5dP6bMSX/HjUVGxWlfP5woo6IiKg24TevhzjNmDL4svl5SY8p64x8gHJWPn84ECt7Vj42P7firHxE7qdy16x8JpvefbIsVXdo2zsOg6dNRERMtLQuKzkF37z9vlsfh9yjtpTy6YKD7Nb5czavspTPCCGYMUVEROQr/KXvARaLRXHmTc5slPWY8uKZRJVK5Thjys+mR9aVGZhixpSV3ax8fnzwT1RdqNWlnzGWqszKJw/4u7kBetvecYifNxvhDaIU68MbROEf776FyNYt3fp4VHX1bTOmIkJ8NBLP0QUFSRmH8veOP/eYUjQ/NxphKpYdD7H5ORERkVcxMOUBljLKNhQHPl4MJgQEBkqX5T2mPPkDqjLKzphiYMqKzc+J3E+tkZfyuSdjSu3Gz1WVWo3B0yYCEIpG7dbbAIFb7u9rdxv5Vm3ImNLLsqXys7Kly37dY0oWmDIZi20yyBmYIiIi8qYKH702adKkUrfVJmX1E7GYzVL/EW/O+iI/aFT2mPKvjCm9vvRHXFGRESaTLLWegSmJXWAq0H/PShNVF+6alU/+HaB1Y4l0i9j2iIiJdhp4UqnVCIwIx42x7d32mFR1taH5uby/VN61TOmyPxxXOKMo5TOabHpuMjBFRETkTRUOTCUlJSEqKspufb169ZCUlOSWQVV35TW6NRYbAHj3gM3aXwpwnjHlzh9QlcWMKdfYlfL58cE/UXUhn5XPUpVZ+YyeyZgKi4p0bbvI+m57TKq6+vVtA1M1r5RPHpjKz8ySLvt1KV9A6femubjY707UERER1SYVDkypVCoIYT9bUZ06dVBU5N4Gr9WVsxn5pNuvH/x484BNF1xaymcoKJAuyw/ENH5wIMbm566xzZDy53IJoupCMSuf2zKm3Jd5kZOW7tp26Rlue0yqOtuMqdDQ4Bp3okUfUhpsqy6lfPIJX0zFRkXPTQ0zpoiIiLzK5VO57733HgBACIF///vfKJAFNzQaDe6++24cPnzY7QOsjiymss+0G6+ni3szVdy1jCnfH4iVFZjS1LAD+aqwPZvrz1NyE1UXbpuVz0O9+84mHkFWcgrCG0Q5LOcTFgsMOblISjzitsekqqlTJ0jxvWYVERGC9PQcH4zIM/TBTkr5/DljSvY9ajIaFX3l/OF4iIiIqDZx+Yi5Y8eOAEoyptq2bYtiWS1+cXExjhw5gnfffdf9I6yG5GfdHN5usAamfFXKV9pjSggBs9EETYDWL84QWg/gjUYThBAs5XPCNmOKs/IRVZ3bZuXzUPNzYbFgw5z5iJ832+FtgAqnNv1Qpcbt5F62jc+t6tatU8MCU6XHGMrAlP9+NymanxcbIWTluwxMEREReZfLR8y9evUCACxbtgzjx49Hbm6uxwZV3ZU1Kx8AqcGmNw/YFM3PC5QllyajEZoArV8ciFkDUwZDSXCPzc8d46x8RO6nKOVz06x87u7dd3THbqx98208/upUxfqslFR8+85/0URf8/oXVWe2ZXxWNa0ButMeU3783SRvfm42GhXBaH9obUBERFSbVPiIeeTIkZ4YR41SfvPzksBUgBfLr3RBpT2m5BlTgLXsJMhPA1PMmHLENqjJWfmIqs5ts/J5qPm5Vcrps4rr2z76FNsWL4NapUKTgQPd/nhUefKMKYvFIgU/IyJqVgCxWjY/l2dMGY0QZmZMERER+UqFj5iDg4Mxbdo09O7dGw0aNFCcYQaAm266yW2Dq67KC0xZS/k0AVqn0367m6KUr0AZmLKWHmr84ECs7MAUm59bcVY+IvdT9phyTymfJ37ghkc3UFzPvJpSkuGlYfDe38gzpi5cSEPz5tEAal7GVKCsx1RRQQGMBgMC9Hq/LuWTH/OYiosVEx54swcoERERVSIwtXTpUsTFxWHVqlW4evWqwxn6ajtXA1OA9w5+nPWYAmSlhX5wIKbXl+ySBkPJc8iMKcfYY4rI/dw2K5+sz6AnMqYibAJT8lJt8i/ywNTp01drbGBKPiufIb80MOXXGVNllvL5/niIiIioNqnwEfPAgQMxaNAg/Prrr54YT41Q3qx8JqM8MOWdgIK8lM9QqOwxZS078f+MKQamgJJyI9ssDH8++CeqLtSyjCMh3NNjyp2z8lnZZkzpGJjyW/XrywJTp66gT58OAGpeYEq+DxryC3wyyUtFKWblK+asfERERL5U4SPmzMxMXLt2zRNjqTHK7TFl8H5gStH83K8zptj8vDyO9hl/Pvgnqi7c1mPKg83PASA8OkpxXS8royL/YpsxZVXTAlOBsh5TJRlT13tp+nH/Q43svWmbMcXAFBERkXdVuGnPzJkz8cYbbyAoiGdonZGXcThiDQQB3syYkgemmDFVnTk60A9gKR9RlclL+SxVmZVP3vzcA4Ep21I+eUYs+Zd6subnNTkwJQ+OGgoKZCe8/Pe7yTZjylRceuzGWfmIiIi8q8JHzJMnT8ZNN92ElJQUnDt3DkabIEynTp3cNrjqqiI9prwVUJAHpqJbNMOlYyektHVraaE2IAAqlcpnfcO0Wo30w5DNz51zNP22Vqfz6WtHVBMoSvnMbmp+rvVA8/MY2x5TzJjyV/KMqVOnrkiXI2paYCpEGZgyFhkA+PdJE3k2o8lo0/zcD07UERER1SYVDkxt2LDBA8OoWcoNTHk5Y6pt7zh0GNBHuv7kW69i4AvPYcOc+Ti6Y7fd1OblZXx5ijVbCigNTAkhpCm2mTFVwllphFavk34MEFHFqdyWMSVrfu7mjCm1RoOwyPqKdewx5b/qX8+YMhiMuHgxXVofERHi7L9US9Z90GI2w1hkUGRM+etJE3lWlNloUvQH9YfWBkRERLVJhY+Y33jjDU+Mo0aRB3ocMXoxMNW2dxzi580GVMr14Q2iED9vNlZMmm4TKAvwq8AUUJI1pdMxMGWlddLoXKvTMzBFVAVqjft7TLm7+XloZD1FZhfAWfn8mTVjKj09B3l5hTCZzNBqNTWulC/w+qx8hvwCAIDRUPpd5K8nTeRZUabiYkVgirPyEREReRdrozzAUoFSPq0H09xVajUGT5sIQEClUtndBgg8NHWCIpDmy/R154Gpkh+IDEyVcFYa4c8lE0TVgbzHlLBUvpTPIvtM1bg5Y8p2Rj6AGVP+zBqYysjIAQBkZuYBqLk9pgwFJYEp5Qkv/2yALj/eMRtNHi/BJSIiIucqHJgym80wmUxOFwLMprJ/0HirlK9FbHtExEQrylPkVGo16jaMUfSG8GXDz7IypgAGpqwCnGVMMTBFVCUqWSZSVTKmTEbPZUzZNj4HAH0Qe0z5o+BgPQIDSz6X09NtA1M1q5TPehxhKCiZ9VeeIeWvJ02sWVEWs1maka86NG0nIiKqiSp8xPzwww8rrgcEBKBjx46Ij4/HrFmz3Daw6qwiPaY8ecAWFhXp0nYa2Y8xT0xt7iplYKr0OSwNTDHBD3BeyueoKToRuc5ts/LJS/ncnIUa3iDKbh1n5fNP8sbnGRm5AEoDUxERdaBWq6u0n/kTa9aetZRPcQLOTwNT1owp+Wx8pmIjtDodS/mIiIi8rMJRiG+++cZu3bp16/DXX39h6NChWLZsmVsGVp2V16PJKC/l82DpXE5aevkbASjKz5cu+/IsoV5fujsW1+KMKZVajRax7REWFYmctHScTTwizaAIOA9m+uvBP1F1Ie8xVaVZ+WTfAe7OmHJUysdZ+fxT/fqywNT1jKmsrNLv2/DwYClQVZ3pggJLZ9SVekzJT8D5aSnf9eMd+fvVepnNz4mIiLzLbUfM+/btw8cff+yuu6vWys2Ysu0x5aEKyLOJR5CVnILwBlEOy/mExYKslFRkJqdI69zdD6UiWMpX0qx+8LSJiIiJltZlJadgw5z5OPbjzwCUB/lmk0n64RugZ9YEUVWoVB7ImHJ7KV9pxpSpuBhanY49pvxUWRlTQEmfqZoQmJIHRg0FJYE3b2WGV4U1m9FkVGZMAb7tt0lERFQbuaU2KjAwEC+++CIuX77sjrur9soNTBm90xRUWCzYMGc+APupmksycFTYOHcBzLI0dm2AP/aYqh3Nz60zKNqW6lhnUGzTqwcAZSlfYU6udNlfD/6JqguVm2bls5i80/w87fxFAJyVz1/Vrx8qXbb2mMqyCUzVBPI+lUXWjClZjyl/b34uz5iyHp+5uwSXiIiIylbhwNS1a9eQkZEhLdeuXUNubi5GjhyJf/3rX54YY7VToVn5PJwufnTHbqyYMsNuVr6slFSsmDQdR3fsVpwt9OXBWPkZUzW3x5RiBkWb7DbrDIoP/Gs8oFIpAlDywBSbtRJVjVpdGvwWwj+bn1sDU/mZWcjPzAIAqDUap73nyHfkGVO2zc8BICKiZjRAlwemiq3Nz4v9v/m59fhL3mPKOksxv0+JiIi8q8JHzBMmTFBct1gsSEtLw/79+5GVleWmYVVv5c3KZ7Qt5UO+843d4O9f90uXr/x9Ghtmz1P0LZIflPmyr0JtLuWzzqDoTMkMitGIaN4E+bnZ0vrC3NIfOewxRVQ1yh5T7sqYct9nqkqlQvj1Ur6slFQUFxZJt+mDg1AkC1ST77lSylcT6ENKA2xS83PFcY5/Bk01UmCqdKzSrHzMmCIiIvKqCgemVq5c6Ylx1Cjlz8onO5PohbNyuqDSMo+Mi5dx5sAhxe3mapMxVXMDU67OoKgLqwNtcemP0cKcHOmyv56VJqouVIpZ+Srf/NzkoebnIfUipB/M2SlpMBQUSLfpggIZmPIzjkr5MjNLT0TVmMCU7BijqMA+MOWv302lpXyyWYCvn6jjrHxERETeVakj5vDwcPzzn//EbbfdBgD466+/sGzZMuTIfiTXZvKDHEcUGUpeOGCTB6aKCwvLHg8zpnzC1RkUi3PyFAf5BYpSPv88K01UXag1pZ8xVekx5anm5xGy/lLZqWnSTGhA7ZiZr0mTKEUWkq309BxcvJjmxRGVrb4iY8q+lK/GBKZkpXyls/LJekz5a2Dq+olBecaU9USdWq2GWquBpZwMeCIiInKPCh8xd+rUCVu3bkVhYSF+++03AMCkSZMwY8YM9OvXD4cOHSrnHmq+CvWYCtDB04c98sa48tIPK//PmKr5zc9dm0ExDVnnLiKgVQtpvbyUz1/PShNVF/JAj6jCrHyean4ub3yelZKKkPBw6XpNn5mvSZMonDi5GEFBzj/nCguL0arlaL8JTjnOmKp+ganyAoJNmpVO2FEamJJlTPlhvyZ5wNhksp+VDyjJqCpmYIqIiMgrKnzEPH/+fHzzzTd45plnYDaXfGFrNBosXboUCxYsQFxcnNsHWd3IyzgckTcF1eo9H5gqN2PKWF0ypmpu83PrDIrx82ZDWARUapXiNkCFb995H030IdAGlmZGFWTLS/mYMUVUFcpZ+apSyueFjKmUVMXndU3PmIqMDCszKAUAQUE6REaG+U1gyhrMMRpNyM0t+e7NyqpepXyuBAQNxWasSjIj16hBsbWUT96yIND/vpvkzc3lMxPLZ03WBOgAByfziIiIyP0q/Ev/jjvuwNy5c6WgFACYzWa8/fbbuOOOO9w6uOqq/B5T8kCQN0r5AqXLhgL7wJQ8Y0qr9ZfAlKzng6k0AGo7u2BNcnTHbqyYNB0FNiWxOekZWDFpOv7c+RMAOJ+VjxlTRFVinZXPUoVsKcBzzc/DbQJTxbLPc30Nz5iqjqyBKWu2FGAzK181CEy5EhDU6zQI0ggApT2mFJO8+GGZuTyoq5yVzz9O1BEREdU2FQ5M5eTkoGnTpnbrmzRpgtxcNl4FKljK54UDn4r0mPJlw0+9vjSzwFHGFABoNDU3awooCU7tXPaZYt2SZ1/E0R27pesBsoP8wlwGpojcxZoxVZUZ+QDPNT+3zsgHAFnJqYrP85peylcd1a9fTmAqIsTu/1R31aXHlDxgLH+/2pbyERERkXdU+Ff+6tWr8emnn2LIkCG44YYbcMMNN2Do0KFYunQpvvjiC0+Msdoxl9OTwFgsD0x5IWMquDRjqrweU748ECuvlA+o2X2mrMJtZuizLdHRBjrOmArww7PSRNWJtcdUVWbkA2wypjxWypemyICVn4Ag3wsK0iM4uOQzOSOj9HM6J6dAysirDqV8FWUNTJn8vMeU/KSgWXZM5i8n6oiIiGqbCh8xT5kyBUIIrFy5EtrrB9xGoxEfffQRpk2b5vYBVkfllvLJziR6o2G1Pqg0sOE4Y8q7GVzOlNf8HKglgSlZVgQA6EOUZ9XlvaRYykfkPtZZ+aoyIx+gnJnVE83PC3PzYCgoUASm9AxM+RVHjc8BQAiBrKx81KsXirp1a2DG1PV9UhGY8sMeU8qMKVnrAC+fOCQiIqISFT5iNhqNmDBhAqZPn46bbroJAHDmzBkUOgh41FYV6zHl+QM2eY8pRxlTika9fp8xVbNL+QAgvIFNYMomY0p+9lleyuePZ6WJqhNrD7uqzMgHKL8DPJExlZ2SCgBSo2mAPab8jXwWu2sZyjYHmZl51wNTNTBjqqCkubtikhc//G5S9JiSNTyXv3e1bgwqExERUdkq/a1bWFiIP//8051jqTHK7THl5Qwlee8Rg+yHjJXZ6B9nCFnKVyKsgbKUL7CO8qy6dVY+i8WCwtzSGZ6YMUVUNVLGVBVL+YCSrClNgNZtwf7g8DAp88QamFKU8tXwWfmqG2cZU0Bpn6mIiBCoVCoIIbw6Nk+SekwVyTPD/S9jShvgZFY++fFZAL9TiYiIvKXCgSm9Xo8XXngBPXv2RIMGDaSeHFadOnVy2+CqK3kjTUcsZjPMJhM0Wq1XggkVy5jy3RlCBqZKMjbCo2xL+Wwypq4f5JsMxYoGs/548E8lVGo1WsS2R1hUJHLS0nE28UiVs3LI/VRq9zQ/B0q+B0oCU+75TJXPyJeVkgYAtWpWvvT0HBQWFpc5Q1xhYbFdEMhX5BlTtmPKyio5oaDRaBAaGoScHPsTRtWRxWyWAlKKAI8fnjRxpfk5e0wRERF5T4WPmD/99FP069cPa9euxW+//VajzvS5S3kZU0DJQZtGq/VO83P5rHwF5fSY8uEZQh17TCGkXoTdD1m7Ur7rB/lGg0FxVtofD/4JaNs7DoOnTURETLS0Lis5BRvmzFfMtki+p9ZYm59XPTBl/R5wVymfvPdcacZUaUCjps/Kd/FiGlq1HI3u3W/H/z6fIq1/f8FGrFy5C0BJAOjixTRfDVFBHpjKyHCcMQWUNED358CUKwFBo1mg0KySsqUAwCjvMeWHJ00UpXyyYJS/TAZDRERU21T4iPn+++/Hfffdh19//dUT46kR5I1vnTEVG6EP9k7pnPxMuqPm5/IDMWZM+ZZtfynAQSmfLGPKJOvjwVn5/E/b3nGInzcbgDKAH94gCvHzZmPFpOkMTvkRKWPKDYEpa68ad32mRigypq73mJJlwNaGWfkuXkxDWlq2Yl1hYTEOHTrjoxE5V1YpX5ZNYOr8+VSvjauirAHBpZ++gL59Oypue+zR2UhKSsHT/50PRGhgkB1fVKeMKbOTjClfTgZDRERU21S4k/Tly5eRm5tb/oa1mNlUfn8S64w1Xs+YclTKV+wfZwjZ/BwIb9DAbp2zUj6jwaB87fzw4L82U6nVGDxtIgAhBTzktwECD02dYHcb+U5pjyk3BKaM7s6YKv1scJQxVdNL+azkAR8AqFcv1MmWvqXMmLJvfm4VEeH/M/NdvJiG225rYre+sNCAQ4fOoEhb8h2lyJiS95jy9+bnTnpMafxw3ERERDVVhX8RTZ48GXPnzkXTpk09MZ4aobxZ+YDSGWsCvNxjyuCglE+RMeXDM4TKwFTpc2iu5RlTZZXyCYtF6o/BwJR/aRHbHhEx0U4DTyq1GnUbxqBFbHsvj4ycsfZMtJjd0Pxcyphyz2eqPGMqO/V6jynZiQbbz4mayjYwFeGnM9vVq++8x1RmZumkFdVhZr7Gjevjhhsi7dY3a1ayT1rLSOWBKX/PmJKfFFTOyseMKSIiIl+ocGDqwIEDCAwMxNmzZ5GTk4OMjAzFQq72mLoeTPB6xpSjHlP+kjFVmllQa0v5ol0v5bM2Prdm3/njWenaLCzK/odcVbYjz3NrKd/1gLE7MqZUajVibmkhXc9OLfmuFRaLFJyq6T2mrOSZSABQr55/BnZczZiqDoGpLl1aSZf37TshXW7evAF0QYFSQFcemAJKv6P8sceUvMRW3n5BcTykZWCKiIjIWyp8xPzFF1+gcePGePnll5GSksLm5w64kjFVWsrn+QMf6w8Ws8mkyI6yUjT79JuMqdrZ/NxhxpSslE+l0Ug/AqwH/UaDAYF1QqSp5Mk/5KSlu3U78jyplM8Ns/JZvwfUVfzMctQ8f/JXy6Xm+YaCAuiCAmtRKZ9tYMo/S/msmV0mkxnZ2fmK26pzYGr1l3vQuXPJ9abNGigy9QwFyr/TZChGgF7vl4Ep+UQv8uwuzspHRETkGxUOTHXt2hVdunTBH3/84Ynx1AgmB8Efu22KZT2mVJ4dj/56xpSj/lKAcrzuKjupDHlgyig/g1mbMqYa2GfPyA/81bLsC5PNtNzeyL4j151NPIKs5BSEN4hyWM4nLBZkpaTibOIRH4yOHFF7oPl5VbJQXWmeb82CrQ3NzwGgnk0pn78GdqwZU7ZlfED1C0x1lgWm1qz5GfMXPAOgpJRPfuLEtlWA0WBAEEL98rvJWcaU8kSd/42biIiopqpwKd+JEycQVEsOgCvLlVI+a7YLAKg1np0Jz9pjylEZH2A7PbLvDsSsgamiomLF+trU/DzsesaU0WBAUX7J2Wd5KZ/8YNp4PSBlbTLrj2elazNhsWDDnPkAVHaBjpLrKmycu8AtQRByD5XGjT2mjNaMqcp9vrvaPF8q5asl38v2zc/9M7BjDUzZlvEBQFZW9ekxpdNpERt7EwDg778v4+rVa7h8uaSUtHlzm8CUXSnf9ZMm/t5jSpExJeuN5cNZiomIiGqbCv/KnzZtGt577z3ExcWhXr16CA0NVSzkYimfLBhU1VKP8lh/sBQ7aHwO2KSu+/BAzBqYspbxqdRq3HRHR9RrWjobUI3PmLreYyo7NQ2GvJKDfPmBv1oemGLGlN87umM3VkyajrzMLMX6rJRUrJg0HUd37PbNwMghtdqNs/JZS/nUaqlEsCJcbZ6vUpWk3OqDg2rFDI+2PabCw0P87nshMFCHkJCSE0LlZUxF1PXvWfk6drxJ+m7eu7ekv9T58yWzQjZsWA+hEaWvh21gyvrd5I8nTZzPyicv5eN3KhERkbdUOAqxZcsWAMCOHTsU61UqFYQQ0LppauzqrCI9poDKn1F3VWnGlONSPovZDIvFArVa7dPghjwwJe+rclt0PoCSoNqtd8Xi55+P+WyMnhQQqEdwWMlBfnZqGkLr1QPgvJSvtMeU/56VppLgVEFOLsYuWwSg5MfaWwMeZaaUH1Jfz5gS7ugxpTj5oK1wFparTfHl+5GuFvSZs+0xBQARESEOA0C+Is/qysgoJzAV4d8ZU/L+Uvv3nQRQEpjq2vU2AEDz5jHS7UUFNoEp68QcfvjdJG9bIH+vclY+IiIi36hwRKRnz56eGEeNYjaW/wPEJC/l82BgSq3VSMEmZ6V8AGAuNkIdqPeLjCkL1Iq+KhZZe5UBz4/C70cu1shME3nj85yUNAToSn5kKjOmZGd5rx/0W89Kq9VqaLRalwKj5F3yH2ZanQ7gpBF+SaW6XspncUMpn+x9qNFqYDKUsbEDrjbFL8wtDXLoZEHsmsq2lA8oKYfz28CUg3EpS/n8O2NK3l9Kypg6lyqta9a8gXTZ2ax8Wp1OOnnpbSq1Gi1i2yMsKhI5aek4m3gEwmJRZkwZHWdMcVY+IiIi76lwFOKnn35yelvr1q2rNJiawqUeU7I+BmoPBoPkfUfKCkyZjEYEBOr9ImMqoE4o5H1VLKK0O7xaVdJX5c9de2pcxkmYLDCVnZqOOtczptRqNXRBQTAXFyt7TF0/6JcHObV6HQNTfiggMFBxXavXSaWY5D9Ke0y5r5QPKGmAXtFX29Xm+deuXEWLTh0AoMbPzBcQoEVYmH3wzd9m5pOXGzoKmFksFmRn5yM8PMTve0x16dISAJCXV4g//zwPoLSUDwCaNq0vXS4ucByYAkrK4uTfVd7gaEbLrOQUbJgzXxF0MisCU6XHZpyVj4iIyHuq3JCiTp06eOaZZ7B//34cOcLZpQAXS/lkZ+VUleg/4ip5YMrgpJSvZDwlB2O+zZi6/thqreKHmPwnokatQt2GMWgR2967g/OCcEVgKk0x9bY1a0pRync9sCEPcgbUglKe6sj2dfHHnivk5ln5ZDN9VSYrVtE83ybTRN48X947sKY3QHeULQX4XwN0eWDKUfNzoLScz58DU40b10eTJiXfS7/99jfM1wO252QZUzc0rCdddtZjCvD+Z551Rkv59ypQOqNlg5uaS+vk45S/b9m3kYiIyHsqHZi65557sHz5cly9ehVTpkzBzp070blzZ3eOrdqqaI8pTwaDrP2lAOfNzwHZ1OZ+kDFlssn2l/8ms+ZOudp/pTqxDUwV5ZUe5Ftn5lM7zJiSHfzrGPDwRzqbjCkGEP2PvEG5WwJTNhlTlWFtnm+bXSdvnm9gYMrvgjvyPljOSgyrQ2Cqc+eW0uV9e09Kl+UZU41iSv9W2x5TRvl3kxf7TLkyo+XNd8ZK65TNz+Wz8jFjioiIyFsqFBGJjo7G8OHD8c9//hNhYWFYs2YN9Ho9Bg8ejOPHj3tqjNWKxWx26UeNqdg7Pab0rpbyWTOmfNS8XqVSQXc9bd4sK90DlD2m1NdvcrX/SnWiCEylpMEgO8i3NkCX7yvWgJTRppSP/I9tKZ/tdfI9RZamWzKmlM3PK+vojt04f+RP3NL5DgDAJ2Mn4eQv+6XvGfnnuq6Gl/LJAz6p6bloEFkSqKrnoCG6L7mSMWXtMxUQoEVISCDy851nNPuKvPH5vn0npMvnz6dJl2OiQvHH9a8qu4wpH303WWe0dEalVismFTE76zHFUj4iIiKvcTlj6ptvvsHJkyfRrl07TJgwAY0aNcKLL77oybFVS6729zF6aVY++Q8VZ7PyAaXp677KmNLpSp8Dg8GoCO4JlAaqVLAg82oyzibWvLLR8Gh5xlQqDPkOSvkcZEzJ9yWWHvgnu4wplvL5HeuMfAAqPIOeI7bNz6siNKqkj4+hoBAn9uxVfD7KM2Freo8pecaUMaiudPnh8aPQtnecL4bkkHyc5WVMAf6bNdVZEZgqzZgqLDQgNTULABBdvzTAY5uVrciY8uJ3U0UzquXBKHmQSsOMKSIiIq9xOTA1cOBAfPrpp5g1axa+//57t5xRrolc/UGj6DFVxR8tZXG5+fn1jClfpa4HBpYetF49cw7yviqKjKnrfVVqWuNzwGZWvtR0x6V8soat1vIek4/KJch1AUE2PaZYyud31GrPlfJV9Qdu2PXAVG5Ght1tilK+Gh6Yan/PHdLlzOLS1ys8PAjx82b7TXCqviJjynFgKsvPA1M6nRadOt0MADh16opdgM2aNVUvVAf19Rl0G7Rorsg8NPmo/2FFM6oVGVOyyyzlIyIi8h6XA1Pdu3dHaGgoDh48iH379uH5559H/fr1y/+PtYyrGVPyAzaPZkzJe0yVEZiyZkz5ahYaa38pAEi/UtI/RaUqyZSSB6b2rl6Lozt2e3t4XhHWoOQsb27GNZhNJofNzxWz8l3fh+RloVpm4vglZkz5P5UiY8q9zc+rUiKt1ekQHFYS6MhNcxCYKpSV/NbgHlMqtRpdH+gjXc8ylAamAjUAUDJjq6MZDL3NtYyp0s/3iIgQj4+pojp2vEn6Xt6794Td7efOpQAA1GoV6gSUvF+envs6Xtm6XgoQKsrMvdj/0DqjpbMAs7BYUFxUmkFucjIrH0v5iIiIvMflI7j9+/fj2WefRcOGDbFkyRI88cQTuHLlCtRqNfr27Ys6dfzvjJ8vmE2uZUzJD9g8G5iSzcpXRvNz68GYWq1WNAH2FnlgymAw4vjP+6TrFlkpX/Lfp7w6Lm9RqVQIjyrJmMpJLTnbK+/X4ajHlDQrn8F3Mx+Ra+x7TPF18jdqRY8p95byVSXzIjSydNaznHT7wJRiVj5Z35yapkVse0UAR54xFaixQKVW+82MrdYeUyaTGdnZBQ638fdSPmXjc/vAVIEo/QwL05W+X6yz3rXtHac4zvFmNq8rM1qmJZ2X1smDUSZ5QJml8URERF5T4VOLBQUFSEhIwD333IO2bdvivffew7Rp05CamoqNGzd6YozVisXFjClzsbwxridL+eQZU+X3mAJ8c5ZQr1f2mNLJfrjLjyu1HnyufCmk3v+z9+VxbtT1+08md3aTzd67bbfdXrTQlp7QFoECpRegoCKoiODXC9GfXMqhKIoiglrqV0G/XojH1wsR+AKlQCmlQCn0pAV60HPb7r2bTTZ3Mvn9MfnMfGYyk0yyuTb9PK9XX51MJslsMvM5ns/zPG+3qIYa6hEsEqFhaUU9bVW+CLPylTuYYqr8IavKlxfFFNXGj6DyqrNBUib7VIgpeVW+yg3VdzU2wG6UfpchmpgyJWTHlRqEmBoY8KUQIwRlT0xp5EsBgnqt4cz54mOXmZc9R9Rr8iDx4vZNpKKlMveKVLQM+qTvnyaj4kwxxcDAwMDAUBKMSPO+f/9+3HHHHRg3bhw+9alP5eucRjX0ElPRIln56MozaTOmotL5lCLwk1ZMRcJR2USePwWIKVlFviQxJavKpxJ+Tioe0aXkWfh5eUKpkGKKqfJD3qvyxfJj5XM1SESLV8XKR7frlRx+7u3tg80odQb+KIdwXFDTWrmE7LhSg1j5tGx8QPkTU6Qi3/BwELt3H5E9N2nebMTsNeJjWjEFQFSv1VAkYSkqxu5evxE71r4oPh4eGMB9Kz+O3es3yvpKmkROJBJStAHLmGJgYGBgYCga8hLGwPM8nnrqKVx++eX5eLtRDd0ZU0Wz8unNmCpt4KfcyheDiZq48wnJyle5xFSTuC0qpjJZ+QgxxRRTZY8UYooppsoO+a7Kx9OWoBEppiQrX0bFVAUTU4e274KZl/rNUNwgElM2E48EXx4VWy0WE5xOob3u7/dpHlfOxNSYMXUYP15YLHn77QOIKxSErsYGmWKNVkzRoDMPS0XG02Mgm9Mp5k6RPM14LJaSRUUW6lj4OQMDAwMDQ/FQ+pTQCoNeYkqmmBrBpCUT5FX5tK18tOS+1IqpsFIxRR1nMlXmJStTTHUnFVMqVj76txGtfGHaesAIj3IEs/KVP/JdlS+Wp/Bz2prm7UtVA0VoZaW9cjOmEjwP3jcIAAjHDeBhQIgQU0klVTlUbK2vlyrypVNMeTxS+15bW17h54tpG59KvpS3tw++qNQXaxFTwwOD4ra5RGpeOt/PZDbDniwkQEgneuxDQBRTTIHMwMDAwMBQPFTmLL+E0Bt+LsuYKmDYOL1aSFvDlJCVSC5JxlQaYupUsPI1q1j51BRTsoypZFU+uvIRU0yVJVj4efnDwEnKzHxY+Whb90jIfrliaiDl+VNFMQUA1Xah/QvGhN8qFBeGMEYD8K9v31MWFVtJvhQADOhUTLnLTDG1aJFETKlV5Du0fRd6TvYilPwdlFY+ol7rOvCBuK9UFWOVuWuu5P1EiClaLU5AchtHonRkYGBgYGBgyA4lJabuvPNOvPXWW/B6veju7sZ//vMfnHbaabJjrFYrfvnLX6Kvrw8+nw+PP/44mijbEwC0tbXhmWeegd/vR3d3Nx588EEYFWTPkiVLsG3bNoRCIRw4cADXXXddyvnceOONOHz4MILBIN58802cddZZWf9NuhVT4eJkTOlVTMXLTDFlpgaTiVPCyicRU95eYuWjFFNVqVa+WIhZ+UYLlJMjppgqPxgoxVQ+rHw02Z+/jKlUxRRNTFVyxhTHcaKyKBARViuIYgoATu4qrYWPgORLAaM3Y2rRYqoinyL4HJCq3nmTqqlqMw8DEuJzgAFPPbAGESr/sFSKKaVatbpeIKbIOCemSkwJ+5hiioGBgYGBoXgoKTG1ZMkSPPzww1i0aBGWLVsGs9mMF154AQ4qsPuhhx7Chz/8YXziE5/AkiVLMGbMGDzxxBPi8xzH4dlnn4XFYsE555yD6667Dtdffz3uvfde8Zj29nY8++yz2LBhA+bMmYM1a9bgd7/7HZYvXy4ec9VVV2H16tX4/ve/j3nz5mHXrl1Yt24dGhslwkAPEnGdGVMRKmOqgKty9EQlffh5eSmm6Im73MpXqcSUNPkUrXz0hDNdVb4wTUwxwqMcwcLPyx90xlQ+7GD5Cj8niik+HsfwoCfl+Vg4LBJp5gquyud2V4kLTiFe+K3CFDFVV1ce5A6tmNJLTLnd5WPlM5tNmD9/CgDggw9Oav4Nu9dvxKGDnQAExVpV0s5Hqt7tXr9RbjMvWcaUnKx1JatcknEOXdWWgKioWFU+BgYGBgaG4qGkOuVVq1bJHl9//fXo7e3F/PnzsWnTJrhcLnz+85/Hpz/9aWzYsAEA8LnPfQ579+7FwoULsWXLFixfvhxnnHEGLr74YvT09GDXrl34zne+gwceeADf+973EI1GccMNN+Dw4cP4xje+AQDYu3cvzj33XNxyyy144YUXAAC33norfvvb3+KPf/wjAOCGG27ApZdeiv/6r//CAw88oPtv0mvlkymmCmrl05kxFS0fxVQoFIFFFn4uHVepxJQrqZiKhsMIDAkTgQTPIxwIwuqwi1Y+Y4bwc2blK0+wjKnyB8fRxFQizZH6ILfyjTxjanhgUJMwCweCsDurYbVXrmKKViKFE8L3Sax8AFBX50x5TSlAE1P9/drEVCwWx/BwENXV9rJSTM2dOwk2m9CPbN6cqpai0XG8H4tmCgr2oxvX4x+/fRKHtu8Sr9NouPSKKeUigFMkpoTziUdTFxPJeMhoYsQUAwMDAwNDsVBWGVM1NUL54YEBIUdj/vz5sFgseOmll8Rj9u3bh6NHj2Lx4sUAgMWLF2P37t3o6ekRj1m3bh1qamowY8YM8Rj6Pcgx5D3MZjPmz58vOyaRSOCll14Sj9EL3VX56IypgoafU1X5Ajqr8pVgAJnOyievyldWl2xeYOA41I5pAQAEhryysvXhpJ3PSqx8svBzgZCKUnYJZj0oTyiJKEZMlR8Mxnxb+UaumDIYDHDWCYopb29qRT4Cooa1OCo3/JwmpkJixpTUN5QLuaPXygdIqqlyOXcgc/A5jSFqrSt4/DAObt0hI09liqkSLZooc9ec9bUAJLJYTTFF9jHFFAMDAwMDQ/FQNsmOBoMBa9aswWuvvYZ3330XANDS0oJwOIyhoSHZsd3d3WhpaRGP6e7uTnmePJfumJqaGthsNtTW1sJkMqkeM336dKjBYrHASk0unU5hMJqI8Sn5VmpIKGweel6TC4iVLxaNAomE5ufQpc0tVkvBzkcLdrs0aI1G47A6pFVnWrtgtpiLfm6FxMyLzsfld9wMW5Vg5ahpasTd6/6Dpx9cgz0vvyra+WzV1TAajSKJGYtGwRkMgNEoU2ZYbLaK+n4qBUqLlcVe2N/JaDSC4zh2LWQBE71AkKat1A1qgm7OsU2tqnWLE+jhgQHN9yBqWKvDXrG/e2OTW9wOJpVStGKqocFVFn93Y6Nb3PZ4/GnPyTPoR1tbI2prq0ty7mrtxCKKmHrrrf1pz2uYl4ibxhpryrHl0Dcp1aquxgYYjUaYzJJiSnleZIHRaDLBZDaXvNJjscH6D4ZswK4XBi2wa4MBQFa/f9kQUw8//DBmzpyJc889t9Snogt33XUXvve976Xsr6utTbEoqsFMreLVNjRg1apViOdhlV6J2mRwbiIWS3teEyZOFLcXLl6MKbXZZWuNFHPnzhC3p00/A6ZYvfiYdtVMnjxJ1/c7GtAwYxpmXvPxlP01zY249mf3Yc9f/w1bUmlhq6rCqlWrUFWdXFmPx8XvwU5V7ZowaWLFfD+VAgPHiRWgCMZNmFDQ38loNGLevHkwGAwFaVcqEdVJ1SIAjBs3bsS/T+MMKUD6jBkz4Vw1nOZodVS1SIU+XFa75jnZkpYjW1UV5s+fX5G/+4UXSoVRgvFUxdSixfNx8mTphzSzzqR+9zPmwu2epHksxwmLWzabBZdffhkikeL+ZmrtxIUXzgEAhEJRjB17Olpb1RfnAMBS2ypuz589NeX6dFDZiRMnTy5J30QKh4jnMX0aVl2ySiR8ndVVKedVUy2p3i659FIZwXYqgPUfDNmAXS8MWmDXBgMAVFfrV4WXfhQH4Be/+AUuu+wynH/++Thx4oS4v6urC1arFTU1NTLVVHNzM7q6usRjzj77bNn7NTc3i8+R/8k++pihoSGEQiH09fUhFoupHkPeQ4n7778fq1evFh87nU6cOHECXV2dWLt2bca/2eqw49zv3AoA8A4PY+3atQW5aefd8iUAgN/rS3teS5rcmLT8AgDAzl27sOflV/N+LukwbZoZgEBKvv3WNnjqpmJK8jnaynfieIeu77fcYeA43HXTF4FEQmbdAwT1YILnMW7peeg5fgLOsa3gjBxefHk9zr7tBtgABP0B8XtwtzRj0W1fAQD09PZWxPdTSbBWOXAB7pLtG/AMFvR3MhqNSCQSeP7559lgQCfaZp6Os/7f5wEARw4fHvHvc0bAi5mfuRIAcODgB9iQw/tNXXwWSO/2/q53sE7jPSZceSmcY1vAmYzYvnNHwfqTUmLadElVG4oRxZTUN/T2niyLtu/Gry4Qt5944hlZyLkSX/jiHMyYKZA7b731Ojo7Bwt+fjSU7URray2amr4MANiyZR+effa5tK9f8JVPSw8SwZTvv3ZMKxbeIrxfV29P0X8fzmjEhfd/W7YvxMfx4kvrceGPhP19Pal95pjLLkbt5HYAwPpXNiDky55UHs1g/QdDNmDXC4MW2LXBAABut1v3sSUnpn7xi1/gox/9KC644AIcOXJE9ty2bdsQiUSwdOlSsRLfaaedhgkTJmDz5s0AgM2bN+Pb3/42Ghsb0dsrVDNbtmwZhoaG8N5774nHXHLJJbL3XrZsmfge0WgU27Ztw9KlS/HUU08BEMiBpUuX4pe//KXqeUciEUTUsgmiUV03X4iqkMeZjIjH4wW5aYmMPRIIpn1/WRi7yVT0BsRslmR+wWBYlkdBi+iNRq4iGrfJc8+Em1JDKGHgONS2NsPTJdlLzTYbDEkFVTQUFr+HMHUtmSyWivh+KglqxQSK8TvxPF+wdqUSQVuG8/G90UUJOKMxp/errpPUkEM9vZrvEQ4ExG2D0VSRv3tdrVS5LiQqpiRS313jKIu/ub5esKHH43H093uRSGgH6Q8MSISHy+XA8eN9BT8/Jeh24uyzJVXa5jfez/h9Gt11iMRjsBgTmDChMeV4um8yms1F/33UKgE66+tgoCpwxiKRlPOic6cMFTLmyBas/2DIBux6YdACuzYYsvntS0pMPfzww/j0pz+Nyy+/HD6fT1QsESWT1+vF73//e6xevRoDAwPwer34xS9+gTfeeANbtmwBALzwwgt477338Oc//xm33347Wlpa8MMf/hAPP/ywSBz9+te/xte+9jU88MAD+MMf/oCLLroIV111FS699FLxXFavXo3HHnsMW7duxVtvvYWbb74ZVVVVePTRR7P6mxwuFwwclzGTgI/Fwcfj4IxGcCMoJZ4JpCpfuop8gDyMvdRV+cLhKOxULkRCVpWvMsLPSaWtzJD+eGuVQ7Qf0NWO6G1Wla/8oMyXAlj4eTlCVpUvPvJMmXgews+dlE3X26tNWoSpwhZGa2UGNhPCB5AypsJ0+HmZVOUj4ecDA8NpSSkA8FBqKre7Ks2RxQEdfL45Q/C5yWqFw1UDb3QQDcY4xo9vFNS+1N9MEzzmEvRNynwpAKiuq5W1v/TYR9xHF4NhlfkYGBgYGBiKgpLO8m+88Ua43W5s3LgRXV1d4r+rr75aPOaWW27BM888g3//+9949dVX0dXVhY997GPi8zzP47LLLkM8HsfmzZvxl7/8BX/605/w3e9+VzzmyJEjuPTSS7Fs2TLs2rULt912G77whS/ghRdeEI/55z//iW984xu49957sXPnTsyZMwcrV66UVfvTg6kLF+DudU9g1tIlGY8lKiXOVJhQOKPJJBIZ4WAg7bHyqnylJ6boASWdMWUs0HdVbKSbZNLwD0oWVqvDAS45SKarHZVD5SMGbahNjpQlzBlKD1lVPn7kK3t0hdZcyX5Xg0Rg+/oGNI+L0MqUCq3MWd9AE1Opiqm6uvKobNeQPM/+fl/GY2mbXzlU5lu4SMrHevPNfWmPdTUKOZDeiPAb2GwWNDe7ZcfQSuxSkPFqiwKc0QgXlX2VriofULn3EwMDAwMDQ7mhpIopg8GQ8ZhwOIyvfe1r+NrXvqZ5zLFjx2TqJzVs3LgR8+bNS3vMww8/jIcffjjjOWVCTVMjrlt9Px679S7sXr9R8ziBDLIXTDFFl0nOqJiKUgOxMlBM0RN3HtJ1YqoQYurQ9l3wdHWjpqkxJWMKABI8D093D3qPdoj77M5qkcSkVVLxWExU35ktjPAoN6hNyJhiqvxAK6b4PCimaNVFXhRTfdpkdkSmmKrMiTRRIgFSxlSUB6IxHmYTVxbEjtlsgsslhG339XkzHl9OxJTZbMKCBUKy4wcfnERv71Da4wlp6o0aAQjX+oQJTejqknKyYiVW8xLFuBK1rVKhg7hKsDmtdizFQh0DAwMDA8OpiMrwRZUZBKIhgcvvuFmVdCAg5ILJbsekBXPTHpsLrNSgjJ64qCFGD8TKgphSV0xVCjGV4Hk8+eOHABhS7B6CDdSApx5Yg5DfL+6vqqsVt6OhsOw1ZGWaKabKD6pWPqaYKjtwVO5MPsrD8zLFVG7EFG359fX1ax4ns/JVqMKDEFOhcAwxsSCGAf6Q8D3XlYGVjybP+vtHFzE1Z85E2GzCtZNJLQVQiqmodN+0t6fmJpJxTikWTSwa7SxNTGVSTJViPMTAwMDAwHAqghFTBYIQXt2CSfNmqz4/a+kSVCeJBku1Azf87he6LYB6kY1iKh4pL8UUPaCsRGIKAHav34jHbr0rhTT0dPeIarvwsERMVdPEVEROTJGBtLlCJ6WjGWqTI6aYKj8YDLRiKs9WvhwVU64GYfIf9A2nkNE06PDzSiemvMPyvswfEn6rcrDyyYiprBVTpc2YovOl3syQLwVIpKkvIvXJEyakElOxEi6a0IopepHH3SpVYKbVUQSyzE2mmGJgYGBgYCgKGDFVYKiFXM9augTXrb4fnFFOshALYL7IKQul1KAzSNQQK7F03ZJGMZVI0Fa+yrpkd6/fiJ3PvyQ+/tvdP8B9Kz8uWkDDfmnCWZ1GMVXKwT9DephVMqZMjJgqO9CVuvg8KKZk4ec5KqacSWIqnVoKUFj5KnQiTcLPh4blCpdAWPitnE5HyRcuGqgcLD1WPo9HIktKrZhaJAs+16OYIlY+6b5RI6ZIdcqSZExRbe/A8ZPidibFlDxzk/WpDAwMDAwMxUBlzfLLEMqQawPH4Yo7bwGQSMnY0msB1AtLNla+slJMxWT2J3qKWOqJRyFA/62Htu6Q2YhClBKiutYtbtMZU4CkoGJKnPIDCz8fHeA4qW3Je1W+HNpUi90GW7WgovFmIKYq3cpXXW0X+whfQF5FLRCRJLWlJndoYirb8HN3qYmpZPC53x/CO+8czni8FH5OKabUrHzJRZRSLJpYqb61X5OYylCVj1n5GBgYGBgYigJGTBUICZ7HYGcXDm3fJds/ad5suFuaNYmnTBbAbJCNYqrUYZ9Wq6QoCIejMoKlUq18BLTVKxKS21RkVr56KQhZSUwxxVT5QrUyFMeVhABm0IYsYyqRD2JqZOHnzvp6cduXoYpnhLbyWSvvuqItcsNBufUqRD0sdc4UUXUBoyv8vKWlFu3tgr3t7bcPIK6DmCU200DcgGBQ6H9UrXwltJmbZcTUCXFbbuVTIaZKvFDHwMDAwMBwKoIRUwUAHV6tDNFVs/apQe9x6WBxOMTtjFX5Shz2mZoxRYefV15VPhq0si0alBNOciufW9yOheX2A9EuwarylR3UFFMAU02VGwx5rsonz5jKvt0iihQA8PYPpD02TLXvlaiYookpf0j+2wRjUv9Q6pypESmm3KXLmCJqKQDYoiP4HABcTY0AhMIbR4/2AFAPPy+XjCnaykdINUCujiKQW/kYMcXAwMDAwFAMMGKqAKDDq5VQWvu0oPe4dKBl7OFMiqlYacM+U6vySZN2umZdJRJTdA6GUjFFW/mqaqmMKaViKmmXMJpNKdllDKUFfS3TZAWzXZYX6PuG5/Mcfp4D2e9syFExVZHElET4+MNyYiocl4ipUlv5aAJNj2IqHI4imFyMKOW5L1osEVObdQSfAxK54+3tF4mpqiqb7LcC5DZzZXxBoUGrkb19/aoLdOqKKZYxxcDAwMDAUGzklsjKoInff+2b2LVxk2a58UPbd8HT1Y2apkZVO1+C5+Hp7kmxAOYCWcZURsVUaTMVUhRTdMaUzMpXeVwqUdREw+GU60azKl9ImTFFKd4sZkSCI59YM+QHNDEVGPLCmbRkMsVUeYGj2uO8Z0zlYOWTKab6Miim6IypCrTz0kqkQDQhey6ckAjFklv5slRMAYJqym63lpaYWpQdMWU0m1GVzDz09vbhaKBHfK69vQn9/RIpR/dVRosFsbB2dcl8QzkG8vUPoH7cGNkxqhlTsvGQ/nvXwHGYNG82XI0N8Pb24dD2XZpjQQYGBgYGBgY5GDGVZxzZkX4gkuB5PPnjh3Dd6vuRSMgD0NNZAHOBLGOKWlFXQ7zEYZ+EmIpGY0gkEvKqfNRxlamYEggKtXLwIcrKZ3dKExdlJSHa2meyWDISkQzFA23lkxFTTDFVVsh7Vb6RKqbojKm+DIopShFbiZk4tBKJtu4BSmJqdCmmAGBw0I8xY+pLRkyZTBzmz58CADh4sBO9vUMZX0Nb4by9fTjaJxFTEyY0Ydu2D8THdF9lthaXmKLHEdFQCL7+fhViKn1VPr0KxFlLl+CKO2+Bu0XKr/J0dePJHz+kqp5nYGBgYGBgkKPy5CejALvXb8Rjt94Fv0c+AExnAcwFuSqmSlmVLxyOwmg2i+oFIf/BgHiSnapEYooQiEobHwCEA/6UfYCKYooa7DMlTnmBnhwFqHueEVPlBboqHx/Ps5UvJ8WUlDOYWTFFh59XnmKKJnxCcfmwJWqQ+qtSW/mIsovneVl+VDp4PMJxVVU2mLNQ5+QL7RPrYbMJ14xeG5+zkSZN+3HkiFwxRSOqWDQpJpQFYHwq95GqlS+aXebmrKVLcN3q+1GTzN0iqGlqxHWr78espUuyOW0GBgYGBoZTEoyYKhF2r9+I333lFvHxe6+8hvtWfjyvK2sWB01MZarKV9pMBZqYogeTwwODAIBEBRNThLiIqpCHfCyuqqRKyZiSWfkY4VFOoBVT/iGKmGIEYllBZuXLg2IqwfMiwcXl0G45G6QqnJkypmRWvgrMxKGJqTAv/y6jBunvLbWVjxBTg4PDulV3g4PS4kMpiLXp0yWFz5u686Uk0nSop0/MmAJSK/PJFFNFbvPkxJRg5VMis5UvPTFl4DhccectABIp8QzC4wQuv+NmzUrMDAwMDAwMDAJYT1lCDHZ1i9sGzpD3LILcFVPFX7WliSlaYTLcLxBTpDJfJRJThLhQU0wBQMifqpqKhrWtfOYKVEyMZsgypjySvYcppsoLMitfHjKmAClnKt3k1sBxmLxgLuauWobJC+aKE1gy+Y9FowgMpbeFyax8FVhFjM5uCiXk/VPMKLV3tWVi5dNr4wPklflKQkxNo4gpnRX5apokYsrXpyCmlIopamHFXGTSVFZYJBiCr68/5Rg1xZTcypf+fpo0bzbcLc2axJOB41Db2oJJ82brPW0GBgYGBoZTEixjqoTwD3gQi0ZhMptR05xaZnmksGRRlS9WcsWUcCmGwzFZJR3fgLDCSaaJlRZ+bjAY0mZMAYJNh+QSESgVU/RjVkWovEDfh7R9lymmygu0lS+Rh6p8gGDnM8MKTsPKly6XhtilhvsHkEgkVF9PwMfiiEUiMFksFWrlk4ipiEFOFEQgPS6lYspkMsLtFoglvcHnAOChiCm3uyrv55UJ05KKqUAgjHfeOaLrNbSVz9vbj87OQUQiUVgs5rSKKVORr0267Y2GQqpWvtgIq/LRltt8HMfAwMDAwHCqorJm+aMMiUQC3p5eAEjJJsgHZIqpgH4rX+kVU9KEfTgpvecr1MonW9HVUEyFh1OD61Mzpphiqlwhy5iirHy0xY+h9DBwUqh23hRTMW3FVKZcmmqx6lmqykMNxM5XyVa+WCwO3igndMNx6XcrZcYUTYqNFsVUS0stmpuF83777QOIxfQRsrSVz9vXB57n0dEh2E0nTJBfz7L8wyLbzOk2Nqxp5UsNP5eRaRnGQ94MNttsj2NgYGBgYDhVwYipEmOoWyCmqmrdMOXZ2mNVBH9mAlk5NJlLmzElm8h7fYhFo2LGlNFYWZesbEVXw24ZUglAT6nKJ1uVZkqccgJt2Qt6h1X3M5QeXJ6r8gES4a/MmNKTS8MZhdeo2Y/UQALQK9LKlySmBgZ8MNvlhG4CBniGhDaylFX5Gii7Yf8oIaYWLjxN3NabLwUALsrK5+0RCBcSgO52V6OmRlJ+yWzmRc+YEhbneJ5HLByGr1/NyhdL2RfLoirfoe274Onq1oxiSPA8Bju7cGj7rmxOnYGBgYGB4ZRDZc3yRyEIMQXkXzVlcTjE7UwZU4BEbpRaMWWRlXgOI+wPgEdlZkzRA3Ut8jDs16OYYla+cgUhH8OBoEwVx6x85QUDbeXLQ1U+QFJMKavy6cmlkR4YdAUnRypYMUVIn74+r6rScGhI+NtLaeWTEVNZWPnkxFRxrXyLFk8Xt/VW5AMAV4Ng5YtFo6I9+ZgsAF0ay5Syb5Js8kK7q0byqimmZMVgMoSfJ3geT/74IQCGFMutQFYZ8NQDa/KeIcrAwMDAwFBpYMRUiTHUIw3m3M15JqaSE+JYJKKr/LkY1FvkwaPJZBQrYikVU9FQSCCmKtTKZ5FZ+TQypobVws/lRGMpV6UZ0oOeHMlsLUwxVVagq/LxGTKd9EIr/DybvJkZF5yLu9c9kbHkfKVa+SwWE6qrBeVLf79PtX3zDAnkfSmtfHTlwNFi5Vu0aJq4rTf4HJCuX5roIYopAGhvlzLT6L6p+BlTwnVDFuZUrXxqiqksqvIBQpXlx269KyX70dPdg8duvSuv1ZYZGBgYGBgqFYyYKjE8tGKqJb8B6MpBWSaQlUO7q1pWHarQIGopIKmYsstzl8KByiWmlCScGkKBzIopWUnuCpuYjnbQVRfp383ECMSyAm3ly7diShl+nm3eDMmdSkdOEcWUgTNUFDlNB58P9PtUCd0hn9B2mkxGOJ32lOeLgVwVUx6PtPBQTGLKbDZh/vzJAIBDh7rQ0+PR9TrOZEQVyT/rka5jWWU+KgA9Gild/iEZS5AxUDQURkix0JMpY0qvNXb3+o04suMd8fH+N9/GfSs/zkgpBgYGBgYGnWDEVIkx1E0rpvJNTBELUSqxocSspUtQXSdUfquurcWNjz6ia5U+H1ASU/TEIxpMWvkSlWnlU5JwalCz8ikH09ESrkozpAchH6OhMGJMMVW2oK18ecuY0gg/z5RLk3puQu7U5XfcrLlgQFuB6cIXox20EmlwSL0v8w1L91Wp7HyjTTE1e/ZE2O1CG7QlC7WUs75OVBd6KcUUTUy1t0tjmVK2eaR/pRd9lHa+uEpVPjp3KhsFubVKsmJGQ2Fm32NgYGBgYMgCjJgqMQqZMWVNZkxlUkyR6lCcIlhczyp9PpBCTFGr/dFwGGG/H8RYU2nElEwxpfE7qVr50mRMsap85QUtxVQlqVoqAbLw83xV5dMIP0+XS6MFA8ehtrUFk+bNVn0+TBFTVipfUAttbY2YO3ey5r+2tvxXis0FMmLKU77ElFwxlRsx5S4iMZWzjY+uyEcp/2gr33haMUVnTBWZmCL9Kz0GUtr5YirEVDZV+WjYndLvZ63KfA8yMDAwMDAwSCh+yjWDDHTGVE2BFFPpKvLJqkMZUqtDJXgel99xM/Zs2FSw1b8UK59NriIKVXLGlD1zxpSqlU+RZRGTBcxWNuFh4DhMmjcbrsYGeHv7cGj7rrJdmeZMRrGYQCwUZhlTZQw6YyrB5z/83GCQk1Akl+ZTP7oHVod+hZNWPhWtjLXYUwPCabS1NWLvvl/DbtcmsYPBCKZPuwEdHb2axxQDNOEz5AuhRuUYb0AiEoodIE5QR1kOc1VMud3FO3c6+Dy7fKl6cZsmpk6c6Ec8HofRaJQppmg1bzFt5iarVbynaTVyCjEVUSGmsqjKR4MmoxgxxcDAwMDAkB0YMVVi+PoGwMd5cEYONXkMPzdZLGK58XSKKVIdSgv0Kv3BrTvydn405MRUTFYOPBoMKax8lSXys+jImFKtyhdWWPkip4aVb9bSJbjizltk16ynqxtP/vihsszyoMmnCAs/L2sYjJSVL2+KKckSxJlMKbah3es3YuH2nTj93MW631Mrn4pkTAGAJQPR1dDgSktKAYDdbkFDg6vkxBSdMTU0HFYlpoaD0vdcDoqpbIipQCCMSCQKi8VcVCvf4iQxFQ5H8c47R3S/ztUojVO8vZItLhaL4/jxfkyY0CTLmCpV+LmFVl6nIabUrXzZhZ8T2KolYtGmQ7XIwMDAwMDAIKGyZvmjEAmeR8QnrJjmM2NKpsRJo5jSWx0qmypS2cJqlfjRiCJjSlBM+UGmiZWmmDLryZhSWPl4nk8ZTMuq8lUo4UEsp0rLa7Esp7lASTwyK1/5Qq6Yym/GFCCoptTgqhcUKLFoNG3uVILnMdjZhUPbd6k+Hw7QVr7KzJjy+aV2js4B84ckhVvpiCnKckipoPSAHF8sYqq52Y2JEwVy/4MDfYjF9CsEZYqpPjlJSnKmGhpcqKqSqgITFLNvonPWZFY+RcaUWvh5nPo+qutqdRWC4YxGmYWWKaYYGBgYGBiyAyOmygBhr7C6Wl1fpzl5yRb0oIyesCihtzpUtlWkskE6K180WZWPjmExGivnspXZFjWUbUorXyycavmTE1OVp5iSWU65VMtppmDoUsGssGoyxVT5Qp4xlV8rHwAYNZQXzuRE39fXL+VOKcgp4bEBTz2wRpO4isisfJUzKaaJKa9fIuSDXqnyHU1MFVN1RIMouwYGfIhnqbgbHBQWH4p17ospG9/evd1ZvdZJE1M98nEBnTNFVFOySqTFVEzZ1fvWVGJKvsgza+kSfOu5f4mPW6dO1lUIRklEMWKKgYGBgYEhO5TXLO4URXhIGGBzHJc3ZRK9Yp5OMZWpOlSmVfp8ICX83K4MP5esfEBlqaZo1YwWMaW08iltfAAQjdAZU5VHTBHLqRbxlCkYulSwKMLtmWKqfFGQqnx0Vo05td3ijEZU19UCEGzdJHdqqEdun/N09+CxW+9Ka1el24+KUkxRFrnhoERABTxD0nZE+r3q6kpDTBErX3+/L8ORqSCKKZfLUZSFFzr4fO++7IipGtrKp1BMHTtKE1PCcXTfVEwyXt63SmMgX/+g7LiJc2aJ/cpIVLm2Knk+mNXhgMFg0DiagYGBgYGBQQmWMVUGCFMrvzXNTRjs7Mrq9Wph0FoydiVIdajrVt+PRCIhG0jpWaXPB9KGnweTxBR1vMlkRDicmgsxGqEnYyqksPIpg8+ByrfylYPlNBeYFUH+TDFVvqAVU4kCZEypqWGr62pFCyGZ5O9evxF7NmzKOuCfbufHTD8NhmeeL9uiANmAzpiiCSj/0BAIfRCkuoNSWPmMRk5UO2WTL0Xg8UhtvNtdnVVVv1xAB5/vy1ExFY/F4B/wyJ47elQiVNvbBaugLGOqiIsm9BiIXhBomjhBdtxX/vAwPF3deOqBNbj8jpuhpcrNVAjG5kwNrrc47KoZkQwMDAwMDAypYMRUGSA8JA1C3VkGoGuFQb/5+FPi43SKKUCqDnXV978FR400CfB09+CpB9YUPFQ6RTFVrbDyUVX5gApTTOnJmNJh5ZOX5K48xVQ5WE5zgTKAl4/HEY/GYDSbGDFVZqCrkvJ5rsoHqFv55BXOJItRguezKjYxa+kSfPi2r4mPz7/2apy57IKyLQqQDWgrXyAidQQBqt8MSV8zakugmKLJsJEopgChqmAhiSmTyYgFC6YCAA4d6oLHk358QIMoU4GkldJgAO2zP3JEIrkkxRS1aFJElah8gUv4G2ctXYLLbv1qyrE1TY347M9+BAOnrXDKVAhGqZgCAGtVFSOmGBgYGBgYdIJZ+coAxMoHCIopvUgnO1/x1S+Kj9Mppgh2r9+If97zI/Hx5sefwn0rP16USU0KMWWTh58rM6YqiZjKpSqfqpWvRKvSxUI5WE5zgTJjCpBIRGblKy/IM6aKo5hyNkgKP1+OpCrpBxxueb26ci4KkA0IMeXxDIMzS21bwCORN8GYRCiUImOKJs9yUkxRxJTbXdjznz17IhwOoe3ZsmW/7tfNWroEd697AtW1bgCC2k+ZvXSUtvIlFVOyjKmiKqbkiz5iTiE9mEiC5BTqgZYq11ad+rvZWM4UAwMDAwODbjBiqgxAK6ZqWvQRU5nDoKn3D+hbsaOzFyLBYNFsIOnDz4mVj86YqpzLVh7QmqqEAnRa+SKVHX5OLKdCMHQi5bliWE5zgfJaBihiiimmygqGAlTl47NRTClCmfVA1g8o8mzSFQXo6/MiGEwluGkEg5GcSJZ8g2Q39fV5YaHyB/1DUsZUgjMhFBL+nlJY+RqoHKz+HL4zuWKqsMQUHXy+5c19ul4jLoIpFs6U5OexY5KVjyimStU3mRXh53pyCvVAS5Vrq1ZRTDkYMcXAwMDAwKAXlTPDH8WgFVNunYqpjIMsapKiRzEFAMMDA+K2MxnIWwxoKaYE21MUoUq28ulQTMWjUcSoEOVYSM3KV9kZU4BkOQ145RM/38BgxmDoUoH+LcjvSwgqppgqL3DG/Ief0/etUaXdotUXtJVPL3ItCtDR0Yvp027Axz56X8prnn76LcyfdzOmT7sBHR29Kc8XExzHwe0WJvz9/T5Ze0mHn5utVpHcKUX4uYyYysGGp7TyFRILqeDzjqEEmmafgUkL5qa9hvSQn+MnNGHGjAno7fUAAKZMGYO5cydjxulj0WSLwWmOly5jKhjSnT+YUFFUAZlVuVY1YoopphgYGBgYGHSDZUyVAcI+2sqnL2Mqm5DnTBlTBMMDkmKqKinXLwaUxJQrOfkgmUthv79iiSmLLXPGFCDY+UxJq46alS9OrUpXYsYUwe71G+FqasTHvnWbuO9v37oX+ze/VcKz0oZahhhTTJUnuAIopmQZUypWPlcDnTGVvZVvJEUBOjp6MX58an+zYMEU7NhxMOtzKQRqa6vF36W/3ydrL/00MWWzYmBgGK2tdSWy8knEVC4qs1IopiLxBM7/xl3gYcCMT34Unq5u1UwyQn5qwcBxGD++EXv3/Ro2qi9vaHBh2/Y1yUcexHig/3gzJi+YqyvMf6RQZkz5+gfSHC1HgudTiTpDelWunRFTDAwMDAwMIwJTTJUBEnEevqSNQ69iKptJTCSgTzEVGvaLK/zVJVNMxcRVcaIsCQcCSMisfJVDTJnt6iWtlaDtfHT5bYJEIiFaJsyWyiY86IB+tcflBJmVL6lcJAQVI6bKCwZZxlSews+j6a18ToqY8vVnr5gaaVGABUsXp+wbM6YOH/38x7M+l0KAzm5SKqaClAXeZLViYEBY4KmutsNiKe6am1wxNdLw88IRU01NbkyaJISX9wTNMou8ViaZHvLTbkzISCk1mDhg3IRW3PjoIynZVIWARZHvpzen8LHbvo2hnlSl4P7Nb6VV5Y4GxZSB4zB5wVzMXbUMk9Oo5BgYGBgYGEoB1iuVCYa6hYGQs6FeZinRQsZBFiVH16uYAiTVVHGJKWkSQVv5iAWxkqvyEeIiHo2Bj2lPhumcsFhIPRuGKKkqWTEFAFWKkOdiqvuyhTzIXyAUiRXTaDbputcZigOOy7+VT69iiuf5rBQdBCMpCjBr6RJc8sVrxMe9Ienv/3/33lgWoenK7CaabPBTxJTZZi2q6kiJEYefe6SFh0Ke+zkfOl3c7grKiSStTLJCVDotRjC/WaGYkucUyu8XOqdw94sb8MMVH8Mjn7sRf7/7B2K73TbjdNV7mEC1Kl8ZZUyR8PobH30En3nw3qIRhAwMDAwMDHrBiKkygadHqGZjNJlQXV+X8XjZICuhFgYtIRLQT0z5BzwAgOra0mVMEbKGWJ7CgaCCmKqcy9assC1qga7MpxZ+Tu+vxKp8NFKIKcXjcoJa1UX692OqqfIBXZUvkaeqfJnCz53J8HP/oCctMa0FvZNt5XMkN8hhkvbv9UjXYrszqhqaXmzIFVNe2f2izJgaGJCIqWIHoNc3lLeVjyhlPvWVT4j7TgZSSRa1TDI95Ke3LzvyKl0wf76grMoHSDmFSkWUp7tHllOY4Hkc3LoDbz/1HPasfwWAoMyd9qFFmp9XzlX50lVwroTKnQwMDAwMlYHKmeGPchDFFAC4deZM7V6/EX/6xrdTAkm9vX2yzJ1wVoopYdXeaDbB7irO4F4r/JxM5BM8j2hUmrRVomJKK/icIOSnrXzqiqlYUjFViVX5aIwqxZRaxhQVXs8C0MsH9ASZ5/Nj5ZOHn2srpnw5VOQj0Jxs9/RqFgUguUEOs8T4n/Cb4YsK38G4qigaxjSnhKYXG6lWPoqYooogmK1WDA5IFrpiK6byaeVz5/ncaaXM4nMkxVRnUNt6R9v3Mi+CGfDKH/836/PSCubPF9QWBQDhfiGKqL/c/l088rkbcd/Kj2va9LY/96K4PfeSZZqfp1qVT0VFVWxkruBcWIKQgYGBgYFBL1hPVCagiSllSeZ02P/m2yn7/nP/avgHPeJjvVX5AKHCGUGxJvw0MRWN8eIEjlYRxaislooipuzZK6bUqvIBUlnuSlfhOEatYkr43ZhiqjxB2yoLophStFuOGpeobsylIh8NMtk+tG2nuO8X135Zc7JNiAe7Ufo7A3EOR4eFttjEAWMd0ayKbBQCdKi4Mvw8EgzJKlwOUMRUsSvz0QQafR56ISOm3PkjM2ilDIcEmu3C9TgU4RCIaQ//lPY9Qn7S1lRAUhp98Nb2nM+xUNeYxSFV5VPmbBJF1I61L+Lg1h1pg9j3v7FFDNqfedESnHX5par5TOVq5cu1cicDAwMDA0OxwYipMsFQd4+4rTcAHQBsKgOf8WfOkMvYs1BM0YSWs0g5UzQxRYexRoPSBD4SqUxiShn0rgWamKqqc6sOMkUrX8Urptzyx+WsmLLSGVMqVj6mmCobyBVT+SGmYmnCz+kJebZWKDUkeB49h46Ij6vTELaEeHCYJAVMIGbAUZ/UdkyojhYkXygbKLObaAViNBSWVbikyZ1iW/mIYsrjGUYsB0umzxcUX5cvtZdSKdNgi8OcvMQ7A+pqqXSZZLvXbxTtk37PUEalkV4U6hqz2FLb3lwQj8XQ8e5e8T0/+cO7VfOZbM7yDD8fSeVOBgYGBgaGYoIRU2UC2oaRjWJKbeAzYfZMWOzUamEO4edA8QLQaWIqTl2S9GAyGpEsMZVETFmTq7rpVG2zli7B7BVLxcfzL1upGloqWfkqm+xwuOVV+MpZMUUTT1E1K1+F/1ajCXTGVL6sfHya8HNXI1WRry/74HM16G2/SW4QUUzFeCDKG3DMbwZxa42zBVQJimJCbpHzyhWI4bBCMVX68PNc8qUISAB6vs5dqZRpdUh9aKdKvlS6TDJAUBSS/Mu+Y8czKo0yIR0Jlg8ow89zxaylSzDtnLNT9ivzmVQVU2VATI20cicDAwMDA0OxwIipMoG3J/uMKUB94NN2xumwOaXBbTZWvuFk+DkAVBWJmLLIFFPSJRmjlCU0MWW1V8Zk3mRNJS2UIFYMK2VLANRDS+nsqUoNQDeazSkTgHJWTMmVi8zKV86gq/Lly8oXTxN+7mygFFN5mhQOU4rXdMQUyQ2yJ8PPg3EOgAGhOIfuoPA9tDgNaG4qLelbp8yYSt4v0XBYyB6kFFNyK1/xFFMcx4lkUi75UgRE8ZUvYkqpgGl1SNdiZzCVmFIGgCtRXV8HLklyKa/Xvj4vgkH17EOCGA8E44IiOhMJlg/Qi3OZFMlaEFVnimwt8hydz0TGXKFhKQ+yHIipkVTuZGBgYGBgKCYYMVUmoBVTrqyIqdRVOovdhjHTpgIQBmTZDPxKrZjiqckhTahFwtKg115d+sFePiCzGqiQhzIrhiLgXi20lCY8KtXOp6aOKm/FlEpVPhZ+XpYwcNI9li8rXzxN+LmrQaq+6h1B+DkNfxbt956XX4WdE5RhgZj0t+/rks754ovn5OW8coUyY0oqjCEneVOtfMVTTNXWVouEzUgUU+T8a2ocKe19LlCSR0QxFeOB3pD8Wnzu57/KaMuradQmUjs6ejF92g2YP+9m8d/+/ScAADyfwN/22vDHA7XwRYX+PRMJlg8QdV0sEgEfz00BmU0+E1kwocdyalELxYae8PpCEoQMDAwMDAx6wYipMkE0FBYDNrPJmKLDNT1UTpUpuTqfrYSdVOUDgOoShJ8nDOpWvsokplIrttHINrQ0Rn1H5gpVTCmDzwFhUkqvjpcTyG/M87wYTi8nEBkxVS7g6IypHCeySsgVU3ILspOa6PvypZiSEVPutMe63VUwJu2LwWQQ9lBvH773+bvFY5Ytn5uX88oVxCIXCIQRCkVSikUQgspit8mtfEVUTNF2w3xY+TiOQ03NyPs4WiljN/JwWwTioTtoAp+QkxRD3T0ZiQlXk3S9KitAAgI5tWPHQfHfk//ZnPx7DOjevUMkpf71/R/nJZsqE/QWFkkHvblL7pYmkTT1D3rENr4cFFOAFF4f9MoVfUO9fQUnCBkYGBgYGPSCEVNlBBKAXtPUqHvFlB747H/jrZTnw9kSUzqtIPmEjJiCNHmjlSVhmphylCcJkS3kQb6pg+dsQ0tjp6hiCgCqastTNSUpPKi8NKaYKksY6Kp8hQg/NynCzxukjKmRVuUjkLffddoHAmhqcovbpEKbs64WW7YcwPCw0G8sWzYnL+qdXEFIH0L4KItF0CTvkE+6x4qZMUUTUwN5sPIB+Tl/WinTaqfzpcwp17etOvPnudIoptTw/PNSpb7ZE6XvqPfIsaKoc0j/mk2UgRJ6LbahYak4ScjvF4uVlAsxBQjk1Gt/e1y275H/+iojpRgYGBgYygaMmCojEMWTyWLBjAvP01TK0LBRA5/DO95JGYRlOygb7i+FlU+yFfBGaZuedERCFDFVVRnElLL0uRLZhpbSGVOVml2kSUwpKvWVCywqkyOWMVWekCmm+NRMmVwgCz83K8PPKcVUf56IqSysfI2NEllAsn84oxHVjQ3YsGE3AKClpRazZk3Iy7nlAqKY6u8XiCmLSEyl2mID4ZhowSymlU9ZOTBXeChiyu3Oz/nvXr8Rj932LVnw+cmACZ7uHrz8+z+L++hMSi3UNEkRA96ezH3T66+/D59PIGjmnlYPQLinLEWyt5G2NzoCYkpvPlPXocPivtCwH+FAkpgqAysfDWeDnKzWazU0cBwmL5iLuauWYfKCubrGpgwMDAwMDNmC9S5lgpkXnY/JC+aJjz/38wdUK68pQa/IBYa86HjvfdnzkUB2iqlIMChOokuhmAJNTFEDynBQmoDYKoWYUpQ+VyLb0FKZla9CCQ+agKKtq+UagE6HNRPIiCmmmCobGKiqfIl8Wfmi2uHnRDEVGvaPSNVBIzDkFduLTO23mmIKAOrbxuGFdZLaZXmJ7HxOpx3mJJnX3++DwWAQ7xfRykfdS0azBUNDAiFQzPBzeeVAbcVUpsm9XDGVmh2ZK46+8y7GVEnX4Y+//B3ct/Lj2Pv6m+I+uw5iiiZSh3QsmkSjMaxf/w4AwO2yockm3FO2IqmICIk5kntLls+k6IfpfCYrZSUPDZenYgoAahrl+aXW6szX2aylS3D3uidw46OP4DMP3osbH31E19iUgYGBgYEhWzBiqgzQMGMarv3pfTKiAlCvvKYEvSIXDgRwdNce2fO5lEkeHhRW3UtBTBlMkgWNzoYIU8SNrVKsfBkypvQOislzUSpoecKcmRW5qulwS5PAnsNHxe3ytfLJrUfK7UolEEcj6Kp8eQs/j2mHnzsbBWIqn2XaEzyPaHJSnJViiiKmGtrG4oUXdoiPS5UzpQw+p+3JUbUKlzapMl8xrXx6FFN6Jvf5tvIRNLe3odkuEFM9A0G8uW4TEjyPkE/6PF1WPipjyquSMaWGdc9vE7fbncLCSTHIGs5oFCvTjiRjCpDymZS5WnSAO604C1PElNlqBWeSZ8uVEqTNIbBnIKZmXnQ+rlt9v0wtB+gbmzIwMDAwMGSLypu5jjIYOA5TL1sOvZXXlKAHeWG/P4WYMlksWRMUxA5S5a4pWL4IvXrsqhUmIKFQRKYgiWoSU3ICb7TCkiFjCtA3KAaEic/CKy4Tn//4t79ZkauatDKq98gxaX+5WvnIqn3o1LHyjVbbB23ly1cGDh+TlFf148aI34XFbhereOWrIh9BRDcxJZG5xMoHAPXjx+HAgZM4cqQbAHDeeTNgtxf/OpUpkfq8qsUilCQvCUCvra0qWjaWXDGVSkzNWrpE1+S+UMTUwg/NhDl5ab+zt0vcH/RJ6i49iilSlS8WiSAwpM+yuG6dRHC2VyeJqSLY28yyirfZL84psXv9RvxwxcfEv9vXPyALcLdR1ZGDw8MIBaTMKasjf+q3kYLOtQMyEJIGAz5y+80AEiltuJ6xKQMDAwMDQ7YwZT6EoZCYOG82bJQKRAm68trBrTtSnpcTUwE4Jsvfq33OLNy97gk8+eOHdIdc+pMBupzRCEeNS6wWmC/MWroEV9x5C9wtzQCA5gmDAOKIxhOKyQcVfh6QJva2Mq3Ali3MGTKmCHav34g9GzZhylnzcN7FF2HTSy/jg7e3i5NnMvGBYh5GJj6VVHWnqkaaTPccPiLtL0PFFGcyirlCtC2VnkwrVZKjHcp7GwA8Xd1ZtT+lArHy5asi36ylS/Dx79wuPp536QpMmj8HT/74IXQeOCjuz1dFPoLosFDdzWK3wWK3a07Mm1ok4qq3zwckSdKGcWMBAC++sANf/NJK2GwWnHfeGTIVVTFAK5H6+32y9pKQuzFFrh5RTBmNRrhcDgwN+Yt6nkQxZeA4TJo3GzVNjbj8jpuhNblP8Dwuv+Nm7NmwqWDE1KJF08Xtt97+QNwODUvfTTbh50M68qUIjhzpxt69xzF9+ji0OmKwcnxRFFN0lVatRZ9skeB5DPX0wlHjgq2qSkZe26rlC4REMQUI1sWgN/fssXzBwHFw1isyptIoptztbXC3aFeIzjQ2ZWBgYGBgyBZsqaPEUK5gaR6nUaGNXn2cOPdMXPX9b8nKQAPZy66zCdDNFmqrx0aDcL5Gqw3jZkiDaHoCH6Im9lZHZahMLLJV3fSD5wTP49DWHejZ9R4Obd0hDooNHIcr7rwFuSruRhtoAqrncHkrpmiSNXoKKKb0KkPKFcTKlw8bH/kulO0n+S7mrLxY3FcoxRQAVNdrt99jxkq/04Hd+8Xrsr5NIKZoIqoUOVNyYsqrqjBVVrikyZ1iBaDXU4qpvj6vzLZ3zQPfR3VdrWb7S0/uPR6JKMonMTV7xhhx+9UN0m8qI6YyKKaMZrN4LWdrPX1+rWDn4wzA+OpocYgpjQWukYIUhzHbrLKxF03shYYDMmKqXHKmqmvd4IxyW2E6QtLi0ncN6q0ezMDAwMDAkAmVMVsdxdA7KdEaDNJBosu/8gXkg6CgK/NV5ZGYkpEo1HkYk6cb5w2YseRD4n56Mh+iFFPWCgmMNmsQF9lg0rzZcLc065r4VAIcScUUz/PoO9Yh7teq1ldKmDUmR8rJdCVA694mz40GgpRLKqYS8ZERU3rI4vM/c7W4z9ubX2IqShNTadrvljHSosixQyfRf/wkAKB+3FgYDAa8/PI7iCfVY8tXzFN9j0JCmTFlViHylSTv4EBhVEfpQFv5xsyZq0rOZoKrsaFgiqlp44W2McoDm16SMp8SPI9gMmcqnXIGkC+gZU1MUTlTE6ojxSGmKLt/tgVg0mF4YEDcpu8t+vsLDQ+XJTGlRiDRSi8lIt5hzedo5DMjj4GBgYHh1Eb5zhJOERzevgshj1d35TUl6KoqNc2NeSEohpNWPiC/iiktEoUopuIJ+QpepMKJKVoBkGtAq97VykpZ1SSKqZBvGL7+AWq/u0RnpI1TSTFVCQQpOXeeH5mVT893QV+vvnwrpigljDNN+00ypmI80H2sE/0dJwAIZKmrSSBK3n77AABg5swJGDOmTvO9CoEGhRLJbE2tYqokeYmVDyheZT5CoA0N+XHZN26CGjmbCd7ePhkx5c4TMdXYWIMGp2AnPulNIEgRJoCkmsqUMUUHnyvzDjPh1VffRTAkWC7bnVHYipIxNfK+VQ0+Wk1OqRGtVTQx5UdYljFVzsSU9u/uOdIBT1dPzmNTBgYGBgaGbMGIqRIjwfM48MwL0Ft5TQky6KGzNtJBD0FBW/nSTWyyhdZnS8SUXF1A29tC1KqntQRBvIVAPnIw9K5WVsqqJsmY8nuGEA2FEU5eF+WpmKKD/CnFlIyYsqASUAkEKbG58CNUTGX7N+bdyjesTzFVn7S6BWIcPF3dIjEFAPVt4wAAL76wU9y3bFlx7XzKjCkL1e6L4edKxVQJrHyEQPMOh9MSkmqgJ/cyYsqtrWDKprjAeUvOFLcPHPelPB9KBqDbnelJPPqazrYvCYUi2PTa+wAAp5nHpLbCV/u16MxvzBZaMQc0sRca9iPsl8jh8lFMpcZGpFXKJRJ4+sE1AAwp8RB6xqYMDAwMDAzZghFTZYC+d/fhz9/4dspKpH9oKGNwNRn06B186RlUDg9SVr48KlG0PjvpoEFMPvaRTTrolV5LhUzm5daU3HIwDm3fBU9X9ymxqsmZjLC7hAmU3+OR/V+exJT6qn0lWvnKhSAdSUVAUpVvpBOtbP/GfH8nUWpSXFWrTgIYDAbUVAvtaDDOwdPVg76O4+LzJACdzplaVuScqTpFxpSa9TmdYqoYVj6O41BbK0zuh4b1LQ4RKCf3Q0MBMd9M69zp/KrPPHgvbnz0kbTVV5dcLFkw33m/M+V5opgy26wwms2a51rTRBNT2ROpa5/bKm7PnlIEYqoA4eeANjFFk0+h4WGEFOHnNEpVtdSZpWIKAPa8/Coeu/UuxCJR2X5lVWAGBgYGBoZ8gFXlKxPseflVvLN+I1bc+AUs+/LnAACv/PF/M3b8pExxYMiLSDCImiZ1O1+C5+Hp7tFFUNAZU/m08hESRX6OCVnGVCQYEi1usowpv6SYstgqg5jSsnplgwTP48kfP4TrVt+PBM/LfvtKW9V01EjWnoBHqHLk9wyhbkxrmYaf04qpyrbyqd/bErJpf3LFSCsCila+EVbl0/NdxKJR8bfPd8YUbeXTCj+vra2GMbkiEIgZ4Onugc0pqSdIAPqWLfvg9QbgcjmwbNkcGAyp6olCQamYck9XsfIp7qWBAYnkK4aVz+2ugjGptOvr8wJZ8OOe7h489cAa8dpMJBIYGgqgtrZalZgSq69CvbiJGlGwaOE0cZuuyEdAMqYAQfVDEy80XI1SZlYuROpzz7yFh1Z/HgAwc3z6PKt8IJvCItlAUzFFh5/7FVY+ipgqZdVStUI71jQZUwS7129Ez+EjGDv9NABAPBrFj1ZdmbfqpQwMDAwMDARMMVVGSPA8dq9/RXzc1D4h42uIlS/sD+DJHz+EXC2BNApVlY+QKKACgY2Uey+eAHoOHxEf0yoiOmOqchRT+cnB2L1+Ix679a4UxV2lrWoSGx8gKaUCniEAgNFsyhjgW2yYZRli6uHnpgpRTIn3dolsH/moCChlTI3sHGXfhbItTiQAg0Ek/6PhcN5LydPh51pW7KYm6V4KRA3w9vahT2blE4ipWCyO9esFMrGxsQZz5kzK67mmA8luikZj8HoDqu1lKjFV3IwpOgfr+OETGdWrBO9tfB33rfx4SttM7HxKYiqX4gJGI4eZp7cCALwRDgfePZRyTqFhiZhK137KrHxZZkwBwIEDJzGY7OImNppQVWVL/4IRglZM5TNjSlMxRYef+/zy8HOH8Fypq5aqWYztGRRT4nEu6V4yms1wNhQ3b46BgYGB4dQAI6bKDL1HpEpjje3j0x5rslhgNAuit5DfnzeColDh54BAomz+15PiY5NBmsQee/+ATAJPq0xiMWl1zmLRthyMJsjKn49wVXf3+o344YqPYSi5mh30DatOfEYzaFupP0lIkf8ByFRTpbJL0JAp4qjfNx6NiuRHpSimAIkgVU7MC02Q5qsiYL6q8gHaZLHBYMCmv/5TvPd9fQNqLx8RZIopjfabBJ8DgMcXBh+PY/BEp6iCaBg/Tnz+RcrOt7yIdj5C+vT3C2STWUWBqLTyFTtjilZ19fV6UxZeCBI8L9tvsphVCSxy/sqMqVyKC8ya1Q67VRgfdAZM6KesmgRyxZQ2kUdb+YZytJ7u7xWuLRNnwIUXzsrpPfRCtigQzGNVPkpNTpO+RLkei0YRi0RSqvKNtI3KR39GiCme5xEY8ibPTd9ijsPlkj2uT1p9GRgYckdbWyPmzp2s+a+tLbvqrgwMlQBm5SszRIJBeLq64W5pRvOk9rTH0hJxMhDavX4j9mzYhEnzZsPV2ABvbx8Obd+VlVIhFg4j5PfDVlWVd2IKAIJeaVWbVkz1neyG2ToRgDB4ogPdZcSUtTKIKXqiFc6D3SDB8xg4fhI1jQ2wO6vBGY2IK353A8eN6NooJRy0YmowSUxRJGpVbQ36j58oqV2CRjpFXDQUhtVhryhiChDaH1oxte/1LfjtjbcW9Bojk3Yt0JP2g1t3aB7HcYIlK5HIz7nSbfGkBfOw8qtfAACMPf00kWT19uU/cysejiAaDsNstWq23y1jJFtPX1JlFI/FMNjZjfpxY0TFFKDMmZqDBx54PO/nrAZC+hBiyqKSyUcrpkxWCwYG8l/ZLh1oxVR/vxe7129Ex573MX7WGbLjiG3vk/d9B7aqKs3r1eMRSEWTyQin0w6fTyBVcikusHjxdHG7M2hG//GTKceHKGLKlqYyH3nfSDAke002eP94CAvbBCJk1aoFeOaZt3N6Hz3I56IPDbliSlINERtsOEkKK4mpkbRR+erPSPi5f9CD0LAfjhpXxmqMgFAUQqmmqxs7Boe27dT92QwMDHK0tTVi775fw27XdoAEgxFMn3YDOjqyV6kyMIxWMGKqDNFz+CjcLc1w1LhQVeuWTb5pyIkpaZU8wfNpJ2B64B/0CMRUHsPPCerGtIjbRk6axIbDMSpfSh4GHotJk0VzpSim8pAxpQSd/+FqrMfgyS7xcbkQNrmiqpayHw2pK6ZyyWEpFNL9vrFwkpiqECsfgbXKAaNJ6lZ4Pl5w4jNfFQENRpIxlb/zJW3xwa07MP+yFWic0IbJ8yXVUSEUUwAwPOBBbWuzJjE1ftIYcbu32yNu9x8/gfpxY+BwuWB3uRD0enHoUBcOHuzE5MmtOPfcM+BwWBEI5FasQS+sVrNo9+rvF9QdGcPPU6ryFd7KJ1NM9XlhsdvQOnUyAKFt+s/9q+Ht6RUXAFZ89YtonToZta0tqu9Hn39tbbVITOVSXGARRUwd6YupjiNoK186ksKVVEyNJKj/3WM+xBdWwcgBK1fNz/l99KBQVfnCgQCioTDMNqssv40opoLJ71OWMeVw5NxG5bM/cyYzpry9faIyUk/FQLXron7cGJUjGRgY9KKhwZWWlAIAu92ChgYXI6YYTikwK18ZoufwUXG7aaJ2zhTJlwKAEDUQygeIZL2q1i2WUc8Xase2itu0YiocjooT9ZSJPKWYMlvMMKjYJUYbyERLqQ4bCWjrUA0VWFvqfIt8gK68RyZZfoXtNB+WrnzBbKetR/KJPFF6VJpiSlkdkVa5FQr5qgjIiRlThQn13fr0c6k7DSjI9egfkNpvtbZy3ASpDe7qlMLX+6mcqQZKNUXsfBaLGUuWzMz7+SqhDD4HFERv8v6JReTEVCgUQTCppiqGlU+umPLhtMULxT7snRc3YMdzL+Dg1h0iOevp6hbOVUFsEHgUxBSBnuqrw4MeGDhOvJ6IYirGA+/vT63IB6SGn6vBbLOKVq6h3twnSL4hP04EhEWliRObMXVq4ciNQmVMAYBvQCCTadKXKIrCw8I4LKRQTOXSRuXLogwI7bIpWXXR29cvVmM0mkwydZka6HwpgrqxjJhiYGBgYMg/GDFVhug5ckzcbkqTM2VTsfLlC7RkXTnZHCno1WIjlTEVCUfFyYdyMEkTUxwSsoHnaIVa9cGRQqaYSq5y53OAW0rQGVL+IakqH0HbzNOzzmEpJGjSKaIkppKPK00xRVdOBOQqt0JBz6R9sLMrY0VAQsDnI2NKDVufXpsSrD7roiW4e90TeSeGhweF9ttoMqlOLFvHSsqM40e7xW06h4jOmXqhyDlTMsKnL6mYsqeqYJQZUwBEO59aZbt8gwS0A4JiauZF54mP97ycqmShFay1LamqKVoxRedMqRUOoWHgOFTXuvGV3/8Sd697Aks+vgpTpgjkY3fQhJ6OVBsfAJGgAACbRhC2q4EOPs9dMRX2B3BkWFI7r1xZONWUWWb7zF/GFCCNjarcNTBwHEwWC0wWQfkQ8hMrH/W9VjlyaqNyyRXTgpNSYvl6+3X97gTKNh0A6qjFRQYGBgYGhnyhvGeipyjkiql2zePUMqbyBToAvSqPOVMmi0Wm2klRTFmJYkpp5aOIKYM+CXq5gxBT+bQayBRTye85nwPcUsLhlgbIaooppRpMC3ptFSNFOitfpSqmUoipIiim8lURMF9V+bTQNmO6qnqpEKrFTAUsmprd4vaxQxIZ1XdM2qZzpjZs2C22wcuKQEzRhI8Yfm5VCT9XVOUDIFbmK3ZVvoFBP85Yci4AgaA4sGVbyvGDnRIJWDsmPTGlJNZ2r9+IJ3+8OuM51TQ14qaf3CE+7gyaZUo4GkGflPeoRmAC0gIHMDIrXzgQwBGfZF0ppJ2PXrhSjiVGCkJMcUYjHDUuWf4Syd+KBCQyzOpw5NRG5cuiDAA1jfLfUE5MpQ9At7tSiSk94eflUICEgYGBgWF0gfUUZYiew0fE7bRWPqqiSiEVU1olx3OBu6VJ9phWTIXDUXFV/FQgpoiVL58DZ3pFm0wo8jnALSVoxVRArMrnEfeRqmqZMJLJVTaQKTxUws8BYTJdCbZUAqV1z+5ygjPl1wqsBlIFT2mJzaYiICdmTOXfyiepFtWfy7dq0U+HNNenlnZvoIifw/uOiNt9FIFBE1NDQ35s2bIPAHDGGeMxblxh2wq5lU9QTMnCzzWq8gGSYsrhsMJa4EIZ9RQx5WgdJ6qL9772JuLRaMrxg52SYsrdmhqGnY6YAoCT+z4Qt6PhcArJAQjXU2uV9NmdARP6j6sTUyFfZoJCRmqMUDHVHzbCFxWu8QsumAmbLX3GSq6wqKjr8gV5AHqtbBxGFFOJRELMmSJjFdJGKdsXrTYqXxZlQAo+B4iVjwq9z0BMOVQIy5rmRlElpoZZSwUl6I2PPoLPPHgvbnz0kYIoQxkYGBgYKguMmCpDDHX3ioMavRlT4XxnTA1SVr48BqArJeB0+HkkGhNzEFIzpqSVRAMSFbECp2VbHAnk4efZBdYWi7DJFbSllJS7JtX5ACAei+fF0pUvpAvgVVYTqxSo2T7U9hUCu9dvlCl+AOD+y67SHQ4sVuUrgGJKVC2msWLlU7WonDwrUVuTJMV5oPOwRFoMUJXbaGIKkHKmgMLb+dQypmiilxBSaoqpTOROoc7z9BUrxO33XtmkeryHIqbUAtAznTvdF6cjtcc4JPKjM2DSVEyFaMWUU0sxJSlRh0bQRwi5SwYc8Ql9vN1uxfnnz8j5/dJBTkwVxsoHCPeW3UkRU5QSiSwW0otou9dvTOkLfvaJ61TbqHxZlAHAKbNj9ooEGqBHMSVdF/FYTNzWsvNVQp4lAwMDA0NpMLpn9hUMkjNVN7YVRrP6qm9BrXwZJja5gh6MR8NhmZUvGqdIKgVZM+mseeI2ZwA+cc+do34FTivofSRQCz/P5wC3lCAER9A3LA6Q5VX5atLYJRLQa+nKF+icEy0rH1BZdj41EopWuhUaSnuUzaFfWVmIqnwExVYtZlK81lQJZGggaoCvX6oMGA4ExMcN48bJXkPnTBXazkdb5Pr6iGIqQ8YUIaYGJLKl0HY+cp7hWAIzL75I3H/pzV9V7ZtkGVMqxJTHIxEGasRUdW3mvtiABFocgmLKG+HgjxnRf1w9Y4oOP7c51QkKV6Oc1MgVZIxydJiy840wZ0rLLkZXcFTm+40UpDAMINxbMsVUBmLKYrenhMzXaxA8ae1/iez6sxTFlE9/xhRNTHUfOiJuqxFTlZJnycDAwMBQGrDeoUzRm8yZ4oxGWQgtDXrAE8o3MdVPW0HySExRg5nj7+2TWflohTs9kZ+1dAkuv/M28TGXJLNG8wqc0WSC0WQCkF/FVNgfEFdDa5oFYiqfA9xSgqgFAkOUSioaFScDVbVu0S5Br+wCQMDrzaq0dj5gSTM5UrMgVQJUiak8Ki7TweasFsu2i/syTLpocBwHpzmOMTUGzJ07WfVfW5u+HDMliq1aHB7wiNvK75/jODisQtfvC8VT7nuiOqtpbsS0cxaKk8i33z4Aj0cgMi6+eLZYxbAQUM2YUqnKF4/FRGuU0soHFL4yX3OLMOEP8XK7qquxXrVvGurtE9umnBRTde6M59Rgi8Oc/Gk6Aybw8TgGO9Wr8tFEipZiqobKmBqJYioSFMYox4bNiCXJ3xUr56V7SVqks4tZ6Iq34cJkTAHC2Igmmujvk1RKppXtNEFEUN+mPr4DJPtfiCIQASCYZX/mUmZMycLZM1n5pHvx+Ht7pfNWyZmqlDxLBoZCo6/Pi2AwfTXuYDAiLswwMJwqYMRUmUJWmU/DzmcroJWPDpXWs0qrF3VjJGLq6Dt7YKIUUzGeVkwJg0myAsdThAqHhPjcaF2BU6swlS+QHBB6MLp7/Ub89c7vpVg/woFg0QmbXGDgOHHllrbvAVLOFLH6vffqGzBA/ne+8c//FP1vlFuPThXFVGrYeVWRrHy1Kpk9WioQNdTYgOunDuK2FXXYtn2N6r+9+36dEzlVbNViOsVrU2u9SO4P+eQD41lLl2Ds9Kni4y/9zxpxsh+P83jpJeH86utdmDdvcl7OVQ11KlY+QjbEIhHZ96gsJECTO4VUTHFGI9xuIWQ7FNenDknwPIa6BdVRpvBzt6piyi1u+/oHVK+nVgeVLxU0Y7CzG3xMPTctFokgHhWIMi1Ll0tR0S1XEAVRmOfw7gHhOzj99DZMmNCU7mWqyGQXczYIuWr5VCMTyO+tOkXWZ6piymgywZS8NtUKdGQKEt+9fiPefOL/ZPte+9u/s+rPZL9h34CM6LI59SumTry/T9yuGzsm7efoPR8GhlMRHR29mD37/yGqyCL8zf88j/nzbsb8eTdj+rQb0NGRu0qVgWE0YnTN5k8hyCrztasTU3Irn1/1mFzhK4KV7+g778oypmIJiUwgihKyApcwSJcqR3EOZAXuQ6vO11Q5jETpUCjIqgblm5hKrmrbqqtkq7VdBw+mHDvU3VP2pBQgBLAShYZ/SEFMJYkqR41LCP6dOhlGs0l2TP241EF0oSGvIsYUU4WGuyV1oq9XMWUwGGA3JmDK0CPa7RaZzUwvZKpFBZmQTeVAvUhHTE2aLvUnA4NSv0Em+7QyCZArU4uVM6UWfk7uE81CAjZ5VT6gsBlTs88/G1yS6A/GUrOetNQhJAC9yl0j6wcAYJD6PdzuVKKIJqZe+NXvoXY9tdoltWhnwCTLDVNDPPl9amZMJUmE0LB/RAtgtKp763tSdcKVWaqm9NjFyDnne9EHSL23aEIvSIWK0/EKtuRYzaVCTDW0Za5w52qQFzDQW4FWfH1SqRUY8iIWicjDzzMUkqGJqePvUsSUSp9aKXmWDAzFwJjWOpgVUS2xWBw7dhzEjh0HGSnFcErClPkQhlJAT2W+QmZMyao6FSD83Nc/gP6O47KMKV5GTAkDSjLATFDPKfNeneY4XnziJlgt2pdzMBgpq9UHtQpT+QI96HM21iN8VLg26selWgaaJ7XD1dhQ9gNFBxV8Tqv5AEkxxRmNsDurMe6MaSmv11PeOt+Q2UkU1eLo37yyFFNq4eepKqpCQFUxlSHYl8Cgs6LjSEBsOVfceQvcLdK5erp78NQDa/JKEMsUrwor9oQp48Xt3qRNQDbZN6RO9hM8j8vvuBl/veEr4v5ly+fiRz/6Z97OmQYhpnieF1VEYiZfUEHyKhRTA7KMqcIRU22TpDYlGNe+fpTqkEFZAHqzLLeHWCUBLSuf9Ftuf3YdfH39KdcTUUzFeKAnZEIkHBJ/QzXEQmFYnNUp2Ufi+TdlV0RDC/QYZdcHHnF7xcr5+J//eV73+5DFKi0YOA7GJGFVCGJKuWhHtzFhOmOKIvGsDgeGBwZlFQ4JlEUG1KC8htzNWRJTyfBzkkEZGqZIswyKKboqX+eBg4hHYzCaTahXUUwRZWhNU6Oqij3B8/B095R9niUDQzFw7rlnpOyrz2Hhi4GhksAUU2WK3qPHwScHkqUgpuKxGIJeYYCfL8WU0WQSB1iDJ7vg7emTZ0xR5BOZuJPBMD2kJlY+ArsxkZaUAnJXOhQKsryUPIezDlElvemBML0yS1cvm7JwZAG0xQAdoB3wyD33tLWvqtaNsaenElN6VqXzjXTh9rEKtfLRlRPFfbWlJKb0EROkIl+hsXv9RvxwxcfwyOduxF9u/y4e+dyNuG/lx/OuWoxFImKwtbNOrrYYP1GaUHZ1CkHnerNhuLpW7N8vVHg755zpqK62qx6vF1rh1aSt9nj8iCfziLSqmIqKKataxlThrHxmXiKbQyqKKQIloUMTU25FzlQ8zsPrFfpy9fBzNwAglszWo6+nv971PUT6e1BrFb6vnqAJfMKAGUvOTVsoJJb8/qzVVSlWb6vDIWYQDY0g+ByQEzXHB6Lo6fEAAJYuPRNms/410mxsYPmuyAekLtrJFVOpVj5AGqu5mlLPXStDlEaKZbFZv/3R5qwW+yJfn2DFlCumMmRMJRcbYpEIwoEABruE61dNMZUuzxIAYBgdeZYMDMXAh1SIqXKapzAwlAKMmCpTxMJhsYKPJjFVwIwpQJKs54uYqmlpAmcUJoCDnV0YHvSAS0gDlDiVC0TsbWQFLk5VyqKtfAmeh7evvNU+apCVs86zYkpWmY9aWaUHklueeFrcnnr2grx+fiFQ5ZY6a6KQUntc5XZjXJKY4nkex/a8J+yvdWdcGc43yG+stmpfqVY+2vZBoEZWFQJqYdJ2nRlTXBEUUwQJnsfBrTuwY+2LOLh1R8EmaVrt95g2aVJ78ngPgOyyYYidz2w24YILZuZ8funCq0n4OcmXAiQyPxpWV0yZrEK1t0wB4iMFIdPaJqZXTGnlhnk6JRtbupyp2trUa5fYYv1UuD25nrY/sw6xw++I+08GJYtIukIhsWT/w3GcbLELkBMp+VRMWRwOrFsnXEdOpwPnnDNd9/tkcx75XvQBhEW7gFdYHKmuq5URO+FMxBR1n5H709XUCJNFqlSoBmeDPDQ9G2LKRb3W20uIKSr8XGfGVCC5UEmsoXZnNeyu1Ek0UYYq71MAiEWi+ODtHSn7GRhONXAcJ7Z7XV2DCIcFpSsjphhOdTBiqozRc0TImbJVV6UMTABpsMPH44XJUkjaQewup1hBbiSgg88HTnQiwfOIhaTBG08TU8lBDVmB4xOpGVMkm+WVP/7viM+t2KArthUqYwoAXI0SMUWrhrY/s04ctI82xZTfo54xBQhZGq2nCaHMfUc70LnvA/G5YqumxIm0yuSocsPPk0qXLmnyTf92hYRSfQJkkTE1yoon6AGx8zlqXLL2u7lVUlAdPyxMMrPJhnkhDzlT6cKrr1/9I5FQIvlSgET0KtvLWFhQLpmtVhgMBnnGVJ6tfDSZtuJzV4n7lRlT6XLDyIITANSqWNIkYkrFypckpoYHB1OeM3Aczltypvi4K2CSPadVKCRGfZ/K+0VWza0nf8SUtcqB59duEx+vWqW/D9JTSICgEIopQKpaXF1XKyN2QjJiStpWU0wd3iGQiBzHiREHarDY7Sk2S7uzOoVE1AJ9j5FFPJliKoPdmRBTREHff0LKLNPKbty9fqNoUeV5HrtfFhShZqsFl3z9hhSFJAPDqYZZsyagpka49zZtelesvseIKYZTHSXtFc477zw8/fTTOHHiBBKJBC6//PKUY77//e/j5MmTCAQCePHFFzFlyhTZ87W1tfjLX/6CoaEhDA4O4ne/+x2qFNLkWbNm4dVXX0UwGMSxY8fwzW9+M+VzrrzySrz//vsIBoN45513sGrVqvz+sTlAFoCuopoiK3XhQIEGX5Rk/YwLzxvxIIJeHSZ2hnhIOvcEJ63wRqjJ/O71G/Gn278jPjYk7X+e7l48dutd+OCt7SM6r1KAtvLlPWOKUkzRA2FSljrk98PT3YPDO4WBcd2Y1pJkMGUDWcaUkpiiFFOT5s8RiZ7j7+9DX8cJ8bl0ZbkLAS3rEaAgpipEMWWtcogECG0VdbjzM9DSsn0RjMjKZyyOla+YGB4YELcJoWHgOLSOldqEoweF3ymbqoGvvLIb0WQlt2U5EFOZwqttRukc+vqEybDJqp3JR99LJqulYFY+JZlGn2cwLiemPN09mtVOZRlTKoopj0cgNCwWMxwO6e+2OathSgblKnP2AMGO2d4oHX8yIF9M0gpjj1F9rd0lv1/oLCOzzTaiMUBIQUy9+OIOMa5gxUr9xJRoF1OGTSafo/fnu28lIGMju8spy+CkCZ8QpWInFZRrkgtFgSEvOg9IxUjS9U0kuFwJvQHoTur1hFykx4zprHycySg+HxgSJs50mL6anY+AkFaDJ7vwzOqHxf3nXP3RFIUkA8OpBjpf6vXX3mPEFANDEiUlpqqqqrBr1y589atfVX3+9ttvx9e//nXccMMNWLhwIfx+P9atWwcrNUj961//ihkzZmDZsmW47LLLcP755+M3v/mN+LzT6cQLL7yAo0ePYv78+fjmN7+J733ve/jiF78oHrN48WL87W9/w+9//3vMnTsXTz75JJ588knMmDGjcH+8DvQePiZuz79sZcpkjFj5CmHjm7V0CU5bfLb4+PrVP8Ld657AmRdfkHZymA5KxRQgJ6ZsVEiyclV85wuviNvkE3/x2S+PiopyaqDDz/OeMdWbmjHFGY2i1ak/SdYceHOreFy5q6bonKJAimLKI25PO2ehuH3ivX3oP04RU0Um39JlTMmsfBWimKIte8MDg6LdJR+KqXS2L0CYQBF1B01U2PRa+Spw5V4Z0ky+w+Zmydr3ke/ejVlLl6SvGphIyLJhfL4gNm/eCwCYNm0cJkzQbysCMudZOShXE1FMpWsvlepDrzcgEh75IqbUyDS7ScrQCUYN8PUP4C933JMxN4xWE6rZT7WsiDQBMqxCTNU2N6LFLthBvBEO/pg62aq0bcZC6oqpWUuX4KPfuk18fO6nrxwRkRAOUAoiux19fV5s3SooWmfPnohWSsmXCbvXb8S6h3+bsj8w5MXj9z4gPi6EkhyQf/90eDlNvqlZ+QiZNNTTK/bDQPqcKS2brbtF331Hgs8BwJvMmErwvKjuSmfloys1BhVWPkBbMWV3ueBI2vz6j59A65RJqplT6SymDAyVDDpf6jWKmLJazSPObmRgGM0o6Wj8+eefx3e+8x08+eSTqs/ffPPN+OEPf4inn34au3fvxmc/+1mMGTMGV1xxBQBg+vTpWLVqFb7whS/grbfewuuvv47/9//+Hz75yU+itVUgQa655hpYLBb813/9F9577z384x//wH//93/j1ltvFT/npptuwvPPP4+f/vSn2Lt3L7773e9i+/bt+NrXvlboryAtquvc4vbCj304ZTJGBjv5Dj4nK8N0DhIgDCI+u/pHmpPDTFBTTPERaeBor5UGpsqVTp5PzZiqzSJnodxgpjOm8mw3IDkSgDSodbc0wZgMmCUD4g/ekoips6+4rCTS+kwqGIKqmnSKKekxrSzseG8v+jsk5U4xrXycySiqhzJa+SpEMUVX5AsMeUWL5UgzptLZvsikpqaxUVQ9dR88Ih5j12vlM3IIxg2IZYh7CgYj4gCy3EErXunv0G6S/khjTYP4HZJsGGXItcFgwMbH/iYjWl4cgZ0vU56VzShNYAeSGVOWNApTZV5bIpFIm9OUC9TINDulmAoljHDW18Hb05sxNywSDIm/jVtF5efRJKYkQpH+bQlaaoywJLmozqC29V5p25QpppJEBLlelIrDkRAJfCwutntk7LLueUntvGJFdtcRbZsj2PLkMzi0baf4WG1RIB+gv39CLkbDYcSjUXG/nJiqgt3lFNt6b2+f7r6Jvl/6qNfozZmiFVf0bx9KWg3TKaZoYoosNPTTiimVynyAnLDqP34iSeqmIp3FlIGhknHeeYLwwecLYNeuw7JxBalKy8BwKmLkwUEFwsSJE9Ha2oqXXnpJ3Of1erFlyxYsXrwY//jHP7B48WIMDg5i2zYpq+Cll14Cz/NYuHAhnnzySSxevBivvvoqotSAYd26dbjzzjvhdrvh8XiwePFirF692lzr9gAA2QdJREFUWvb569atEwkwNVgsFplyy5nswI1GI4xZ2EKMRiM4jkt5zcyLzseKr34x5XgyMPzzN74Ni0Ng1cOBQFafmQ7CyvCt0Cwbrlj1os9nz8uvpn1vehDj7e4RzjkqVTaqbpAmnvFIJOVvikZjMJtNIjFV3zYWHXveg1FncLHRmPo9lwo2Krg+Fk79W9Wgda0okYjF4PcMocpdg5rmJhiNRjROkErED5w4CaPRiLoxreB5HhzHoX3OLNz46CPwdPXg6QfXZPwt84GZF52Pj9x+s2zlV+vzq2i7hM8n+w7ISq4SXZRVAhBWpYv1+9OD/Wg4nPK58YjUHlns9ryfl95rJZ+gFR1B37Bo/XDUuGC2WMDH41m/p6w9UrF9JXgel99xC/7+7XvF/V0fHMS4M4QAfLvTqes7MJvN8EWN+OOBWiy2H8OM8fKB4dtvH8BXb/wV+vq8OHlyoGzaESXo3z1AZa996FNXgnyHjiTxE+WBuMEIJITv8P1X38B7r7yG9199AxPnzcaUhfNx8RevByC0tfTfvH79O/jBD4Xt5Svm4Q9/kPrpTFAjVWjQFrmBwWEYjUbYquj2Un4/xSJSH2JzODBsNGJgYBj19S7U1en7/TPBrUIC0AQayZhyJ9vbTPB09aC6rhY1TY0p94ZnSCI06upd4vs5G6SFm4DHm/I542ulx50BM5RI8Dw83b04umuP+Fqj0Yh4WPr+qmpcMJnN6ccAvHS9ZBvcH/YHYLZaYauqgtFoxIsv7sB3vvtJAMDKVQvw5z+/ovu9xkybmrJv3PTTsFvW9urrW7NFgFJMEUI8NOyXfRZNitmrq2R5Yr6+flnWWLq+ibZTHn93LxqStr+61hZdf1sNlTPpHxgUXxMa9gPNgqpU+T6kHammlMohn3Av0oq/BkW7QNA0oU3cNsAAt0qWmvh80mI65ax5OLSVBaOPRpRivDGa0d7ehLFjBcL4zTf3AzCIizAA0NzsxvHj/RqvHl1g1wYDgKx+/7IlplpahFWo7u5u2f7u7m7xuZaWFvT09Miej8fjGBgYkB1z+PDhlPcgz3k8HrS0tKT9HDXcdddd+N73vpeyf9myZRimcgYywWg0Yt68eTAYDIiTwanBgMW3q6u1CDl01T13ivYTp82et0ws98TxaSXiynLS4vl89w602aoBtRLBSYyZPBEAEA0EcNH5woqrm5KR144bC9I0zzlzNiY45F5rMgY2QPiMRRecj1aY0NBYjUgkBotF+3KORGKYOWs+WlqmaR5TTIyfI+V8zDzjDLQk9BFTKdeKBhLJVXB3cxNWrVqFMWdLq9GtNXW49hs3Y+Y1H095XU1zI6792X3Y89d/o+/dfXr/nKzRMGNaVp8/bmK7uH3ewkXgY9Lfb1YJgQ32D+LCc88DIFxvZocDY6dOLlp2nIW6rutq3CmfW5cMaAeAM2bOhCPP55XNtZIvNJ0pSdMntLaiiqo0ddlHr0A0B2VnxvaI41Db2oxVn/mU9BqT9Lkt48bq+s2tNQIR5Y9ymDxRmNh6vSFEo3HU11fhjDPGo7V1GlpagJm5F6IrOOjfvX68pB6kVWtEMRWICf0H+Q4/ecMX4aHs4zh8EmHfMKzOapxx/odw2RWXiyQGxxng9YbgctmwYsV8XHrpJeB57bZfBoMBIY8X1hpnSn8CyImp1pbxWLVqFaopZVFrU7PsN21plK6PC5Yuhb+rBzwvtKdudzUuuWRVum4p5dzc7W2wuKoR8Q7Dc6QDSCTgnjg+5VBaeRZKVuU7fdJktK5KJYWUsCa/K6PJhI984kqEh6TV8sYGyfJ+8dLz4XIKv2Prgjni/oljU6/rj330AnH7pF/enxA75vH1m7ByxQpxv9FoRFuL9HnzFp6NaRPadd1zKdeLDpCzctYKbSLHGeDzheB02nDJJWdldR1NP2ue8LfxCcTCIZjtdrTPmoHzLpDUXONaWgrS5o9tTVUKGROQfVZNu0TOTJ85A01WqZ9qdNbg3LPOFhbgLBaMn3aa5nlOPvsscbs6Ln03MxfMh7mjW+0lMrRTBN6iufPBzxQC8u3JvDKrw4FVl1wiG7uRdqTutEnS39wsfZexUBgmmxXjpk5RPe8JF5wjvS7NOJrGeRdfhGmN+o5lKC+UYrwxmnHBhdI92dMTxapVq+Culfq4lSuXoqnptFKcWt7Brg0GAKjW6V4AypiYKnfcf//9MpWV0+nEiRMn8OKLL8KjKGefDkajEYlEAs8//7x4005aMBcXpgkMNhgMsFG2mRMdHVi7dm32f4QK5qy8GNnG2RoMBtjcNdjX06m54sUZjTj/B3cAAHqOSuf7pZvOAyCs6Bkd1UBy8fb1TZtwdOdu2XtEIp+F1SoppnqHfeL7vLJhA8aOrcem16R8icOHu3D1VQ8CAPr6vOjoGFlVoXxi2YRWEGpiyxubsf+NLRlfo3ataGHMhy/GtKR975XXX8MFp7WDUHKbXngBV95zF5BQUaEYhIyZcUvPw19W/3dBStkbOA533fTFrD7/jC98GoCgDnz2/56RvYYzGnHu3XKrwIHtO8VrY/I1H8X4mWfA4nLihZdektktCoW6cWPwIdwEADh+9GjK/Tmpdy5mf05QChw5dixv9y9BNtdKvrDYaQNJ5du25S1M5mNomC4MwN7ctlVWzEEv9LZHHsoS/MZLL2P8hR+CxW5HMBbV9d3WjmnBOXd+Ha2OGGwW4Zp89pktsDusuOKKRaiqsuLgwXewb9+JDO9UWtC/e3v3Scz41EdlzxuQgD2p9AnG5Pfe+4cOYufzcuWTZdZp+NCnroTRbMLJaAjb164Tn3v++TNw1VXnorrair6+D7Bly37d59kRGsa1P70PCSRk5FSCl84PADZu3Iy1azdjwuyZOAtfAAAc3H9A9puaZ50mEu9b3n4bHXvew1e/dhamTWsGxxnwxhuvioHi6ZBOwfn8r3+LiR9ZgZqmBiljKnmekTgQiyfg6e7B33/9W11tpmnmVDTOFEqF79z3Po4kK7QBQHs78JlrhXzHQ4f2Ye3aDQCAC1vrMT15zJZNr6WoSn/6s8uE84nE8MFRD5zN8r/j/37y85TXGI1G1E2bjJlnzRI+79gxDBw/oeueU7teMmHa9VfDXlcLg8kk/oZr10rX0cDAQWzenHlBxMBxOPeebwAQ7G0DJ05i2jkLYamuwsHOE5iXPG7f++/jxTy3rQBwZjyM0z6yQrZvoKdXdl2OmTYV8778WQDAye5uHD1yCHOSz+14cwveWLsW0z93NVpPmwKL24Xn161TvXY+dd5ZILToC//6D6ZcugwA4I2EdLVtM28QziHoG8azTz8tnd9lF6MmmW31yqZNCPokxQZpRzoTUcxK7tu9fTteS37etOuvxphpU2Cpcaqe95VnnQlCaW15+RW0nbsQmbDppZeZYmqUohTjjdGMD3+4Xdz+4x+fxisbdmPSJA6f+pSQ9Xr48D6sXTs683OVYNcGAwC4s8iaLVtiqqtLkDk3NzeL2+Txzp07xWOamuQre0ajEXV1deJrurq60NwslxGTx5mOoT9XiUgkgghlISCIx+NZ33w8z8teV11Xm+EVcoSG/Xm74T3dPZkP0kB1Xa3mebiaGsXMnf4TndJx8Zh4DLUYiLA/kPJesaRKhhBT7tZm8ZgjR7pht1tkx1dV2bB164Gc/55CwkSpScKB1L9VC8prRQt0Tkx1fZ2sHLXV6dS1Ij5h9kwcLMBAcfLcM7P+fFKVz+8ZSvnb4/E4AkNeWcZRx7vvi8f1HzuO8TPPAMdxcLc250SQZAuj7PcNppwzXbDAaDEXpMPWe63kC3SIrn/QI7Ns2ZzVOZ2H3vaIrtrWf6ITQd8wLHY7bNVVuj6XiAUmOqU2/dln38aECU244opFAIB58ybjvfeyU4iUAuR3J0HHNGzGhFi0LKBSSU75Xe1Y+2LSBggs+sQViEWj8Pb24dD2XXhh3XZcddW5AIClS2fjjTfe132Ou17cANz+HVz7kx/I9of8wzj43iac/0mBmOntFe532f0UlN9PdOaU0WxCPB5HP2WLqKlxiCHqWpi1dAmu/el9AJRW9QZc+9P78Nitd+HJH6/GdavvRyIhkGm2pGJKUEsZ8NQDDyGmk/QmxT8AQSVK/z3KcyfP0ZVJvf0DstfU17tw2mlCTtHWrQfw/eV3YtK82XA1Noi/lxZhFqEqtFmrHLrvObXrJRNIu2e2WZEAwMfjeH7tVvE6WrZsLl577b2M79PYNlbMazq57wD6j58QC19MmD1LPE5tHJEPKHO6ACDo88k+K+CTlPMWhx3V9VLWE/nu+jpOoPW0KTCZzXA21svsfQTV9ZKF8/jefYhFIjBZLHA1Ner624gF1NfXLzs+SCn7zXYbhhULqjzPw0pVafR7vFKfevwExkwTzru6vk5m7wPk1fp2vvAyLv7S9ahpalTNkRIspj344O3tBVkIYygOij3eGM0450OnAxDiSTa/IYxVe3o84vN1dbmNl8oV7NpgyOa3L9u0wcOHD6OzsxNLly4V9zmdTixcuBCbN28GAGzevBm1tbWYN2+eeMxFF10EjuOwZcsW8Zjzzz8fJpPEwS1btgx79+4VlU2bN2+WfQ45hnxOsaE26EmHfIafZyobng7pzlst+BwAuIR0scb59GWeCTFlSAjnpqxo1N4uJzvyWSo835CHn+c/oJUmpmqaGsVcingsprsCWaaQ4lyh933JcQaDQSSdlMHnBMr9J96X1Bt9JajMR1cRU7uWKz383O/xIuCRyAA6IywbZGqPEjyPwc4uGE2SdcnT1S1VnKrWF35tSLLdhJjieR7r1u3A229LxPZZZ6Vm2pQzaGIwHosjkUjAQdnPiGKKfIeHtu9KeY8jO3eLVS/b58ySFb3oCErX7bIsA9ABoPfIsRQr38GtO8APe8TH/WL4OXU/BdOHnwPA4IBE7mTqB9Qq7tHPkXDmPRs24bFb70reu5KyazgUx2O33pVVhdjBkxIxVdvaKnvO41EPP6fvIb+iKt+iRZJF/c3Ne5HgeRzcugM71r6YMYxdHn5erfueU7teMkGtUt26ddLix4qV81Jeo4bWqZIVuvPAQVl73z5HIqYiWVa8bWtrxNy5kzX/tbUJ6m61nLSwX67Kk1UhdDhQ0yT1e6RyrqwyX5t6ZT5S9CHk9yPsD4h9uzL3TK2QiLWqSsw7jIZCsus7RBFnWtVL5VX5pPZ8gLp+Zy1dknLfEGLK7xlCcMirXfGT5yGQumsYKcVwSqCuzokZMwQN5PbtBxEICG0UHX7e0KDtmGFgqHSUVDFVVVWFKVOmiI8nTpyI2bNnY2BgAB0dHVizZg3uvvtuHDhwAIcPH8YPfvADnDx5Uqzit3fvXqxduxa//e1vccMNN8BsNuOXv/wl/v73v6OzU+g4//d//xf33HMPfv/73+OBBx7AzJkzcdNNN+GWWyTrz89//nNs3LgRt956K5599ll88pOfxIIFC/ClL32pqN8HARkYplthGh70wJlcSaPVFyMFKRt+3er7keB52eeTlWK113i6e9IOVGvHSIPvQWq12GiQVqhpxVRUhawhxFQiybzWjmkRrF9JuUN7u1z1ZjIZUVNThaGhzDaOfKOtrTFt59LaJD1XiMpB3h6JJKxpahRLWg92dmFI54p4tgSp7nPT+b7kOJuzWlTbBVSIqVlLl6BWUd3q6h98G//50c+we/1G9B+TKhnVF6kyn5mqIqZalY+eTFsrhZiSFB2BoSH4qRV4+rlsIGuPFO2PcN8LkxpSKCIaDmN4YFCcdNmqqsTA5nTgjEY4zXE02IS2ZcuW/ejv98oUlwtGGTE1cf4c8TsjxJ1dQUxlmhjOvOh8mVKHoKapESvu/BYOHd2PSRPqsWjRNLhcDni9+vsitfDq8bNmoI4KbSdKJ/n9pCCmwqn30sCANOmuq0ufbUAq7mmBhDNPmjcbu9dvxLF33sWMRXNF5e6eN3dmRUoBwGCnpDChF20AiBUFAf1V+RYvni5ub968N6tziVF9rc1ZLd1zD92fcuxIiQR6rGJ1OBD0+tDZOYBduw5j9uyJWLBgChoaXBkrX7aeJo0bO/d/gO5DR8THdGXWbCretrU1Yu++X6cor2kEgxFMn3YDBlSIqaBPQUwpSDh6QYb0z3SVvfq2sTiwRaqUS0BeR17j6e5B/bixcNS4YLHbEAmGMGvpElxx5y2y69g/6IGBKgwz9vRpuHvdE3jyxw9h9/qNYlU+QLsyn8NFV+UTyN5ZS5fgrMsvFfdfcectuOD6T4vvazSbxfPoTy4KkYqfH/3WbbLqqp7uHjz1wJqs7x8GhtGKDyXVUgDwOqUOpZWyjJhiOJVRUsXUggULsHPnTtGa99BDD2Hnzp24916hwtKDDz6IX/ziF/jNb36Dt99+G9XV1Vi5ciXC1ED0mmuuwd69e7F+/Xo899xzeO2112SEktfrxfLlyzFx4kRs27YNP/vZz3Dvvffit7/9rXjM5s2b8elPfxpf+tKXsGvXLlx55ZW44oor8O677xbni1CADAwBQ0oVPDIw3PLE/4n78qmYAqBZNhyA5vlkGqhqKabowPJYQpp00pMN8XlRMSX8b7ZaZTJ3pWIKKE3ZVTLA3bZ9jea/1V+dA6dZ+DvUFDUjBU3+tJw2WVSO9HecKOiKuB5k+/lV1ITMwHEyspSUNTea5YHDroZ6sax5H7UqXSxiKl15e0B9Mj3aMW5sHZpsMTTZYpjaXou2Bqv4eM7sdlFtkC1IexQOyCeZfFxSqpD2xZOc8Icom4oe1ZSB49BeLdn41j4nTBA9Hj8OHBDKo8+ZMxEm0+ioLDPzovNx3U/vS9lvp4LFAzEDPN09mmofSUmUCqIk6jYI96bJZMSFF56Z1TmOmS4RU2SS7GqoR8sYaQIvKaYoYkrRN6gqpjTIHTVkq+CsqnXDZpL6wUwkihroPtCtINUHByXCwE0rpurcAATVq7IS6UJKMZU1MaVQTAHCPfcWNcYgSHe96IGaYgoAnl8rVFbmOA7Lk+o7NQUQwRiqeMTJ/R+g72iH6gKd2gKXFhoaXGlJKQCw2y1oaHAhMORNqTKqVExFQ2HxGGuVQ0bIePuIYkoiptQUU8SOLLxGsOYOdUvjMldTo9gH0u8PCNZPWvEESFWUZy1dIqpKAcCmEUxrd9GKKZ/4WXan/Hj6fevGtoqqbFoRtnv9Rvxw+UdFS0fPkWO4b+XHGSmVJdLdFwzlj3PPlYrE0LZluh+pZ8QUQ5lDr7o4F5RUMbVx40ZVBQ6Ne+65B/fcc4/m84ODg7jmmmvSvsfu3btx/vnnpz3m8ccfx+OPP572mGKCTMau/O4dsswpssJEZ6rkm5gin79nwyZZToWj1o1PfPcOWYUnvStedZRiis7XsFolQoFWTKlb+ZKTKoocqxvbCl9ywNY+MXXVu77eiUOHtLPCCgE9A1yLiYPdmIAvWigrn0RMTZonVQDs7ziRXhVXBGl9WhWM4vNnLV2Cj3/ndvH50xadJa767tmwSbLgaJY1vxm/uPbL4n4tu0S+YaGsmjWNDSmqHXoyTR87WtHW1oj7rp0Ms9EDALjmZZIdlHx8z3IEb78A06fdgI6OVMI7E3av34jZy1/D3EuWi/uMJhMO73gHNme1uOJPJvxB2aSrKmUirwRn5DDRKeUDPfecpFx4++0DmDp1DOx2K2bOnICdOw9lff5FhcGAj9x+M9TuCwdFqDz7m7/ip/f+UfM+16Mk6ubdAIQB9fLlc/HUU2/qPs0xlOpl+7Mv4JyrhKD25hZhsWF4OIhwWPhNZNZnXYop/Va+bBWcVbVuGcHXnwMx5R/0IBIMwWK3pVjSaVLN7ZZI1eqklS8w5JUtEHEch7PPFki+jo5enDw5kNW5xCNh8DwPjuNkRAb9Pa/71e9w8K3tabOq9IBW6ciIqee34Y47hSyzFSvnY3c3UhRAnq5uUZVDFFMhvx+DJzqRSCRwct8HmDhXTo6GC9C3AsIC3fCgB64GKTeKJnrEz/cHYHc5YXU4RNLU1z8gVpXNtGjiapTen1x/Q12S4rm2pVnbhqoytqb7xY1//oe4X4u8lxFTvmFd/e2/f/CguJ8mpgBhMWG4fwA1TY2w2u3Mvpcl1JRx9H3BUP74kA5iiimmGMoZ2aiLcxnvM6q9jLF7/Ub88robxMcH3nxbXGGiB3WFIKYApORU7H5xA/5027fF53esfVHXipeB4zB2ulT6lA5XJcSUQEpJA6lYODVYXgw/pwJq6UG90soHlHfOFEFBrHy9UmNAf/dkoKilivN7hka0Iq4Xu9dvxD/uuS9l8DzU0yt+PlmdVRYDIKuzF3/xOrhbmjVXDIkFp6FtrEj+FUMxpSTTzrn6Y7h73ROYtVQqY15piqmGBhfMxvSLDERtkCuc1CSQYOLcM2U2TmKRkuWn6ChTa7dZMT6pmPL4ItixQyKfto6ynCl3exvcLU2q9wWdMdU3MJx2YqhHSXQ8YEY0KrTLy5bPyeo8iZXP29uH9155TdxPrHe0tYG+R5QqGLqvMKkSU+l/f1HBqVADE9AKToPBgCp3jaxyYKZgdS2QwOj6cWNkyodMVj6ljW/mzPFwOoXxgJ6KdilIQDWTjW4rN//zyYxZVXpAq5ps1BjmjTf2wucTnrv0soW4bvWPUhRApN2fd8lyMSuw64ND4u92Ym9qVchC9K3NzW4Aqb8DrdIkIH+vzVkNV4PckgcICs94VCgAo0pM0Sor0con2UCnLFyQtg9UA+kXXZTaXI9iqnnyRF397WnnSBX4+o+fTDnO1ycQp9V1tRkXphkkaCnjaLUaQ3nDZrNgwQKBVN+797iMjAoGw2LeFCOmGMoZ2aiLcwEjpsocdLUTzmwSB4Y2hzSoC+UxYyrj+VCkUiKRyDhQnbV0Ce5e9wTGnSFZDW7712NiJyoSU9TbRIIh1UmCVJVPrpgiKBcrX7ZQyyAaKXx9A+CTvw1nlOxH/ccV0voVH8PGx/4m7nvu578u2spbH5X9RPDoTXdg9/qN8kBixeCV2IjOveYqXZ/jamwQ/+66sa0FHQyTwSMdBA6kDh7pyXQlEFPFgCoxNX+OLDzak1RMhRSKqUxYePYUmJO94fb9/bL2Z7QFoFtc2kQMTaiEYdY8DtCnJIryBmzbJVQqnDJlDCZNasnwCgE1zY2i8vbEvgM4tpvY5hNwVgkDHpqYkhcTUFj5VEhe2g6XyconWefVn6MVnCTvzkYTfDkopmYtXSLaT81WqxgoP2vpEkSjMfj9Idm5W+x2UXGTLl/qzSxtfASEVKGJCKIuDQeCoip5pJBZ+agxTDQaw/r17wAA6modaLLHNIPoP3L7TeK+zv0HxW06AJ0gm4wpvXhu7fewfcfPceHEBJrtUZBKjqHh1HFYKPn3uhrqYTQLBoUhatGIj8dFlacqMdWQqpjydEmvH0kxD3I+QObw82g4jKoafZOMhvFt4jZtVSTw9feLn6/sJxnUobdAA7P1lTfOPvs0WCxCv/u6SvVR0pcwYorhVAZrxcoc0VAYgWQ1FHqlpBiKKTV4FdXe0kHPCo+kmEqfLwVQxBQnHUtC1auqbGhsTA3pra8v7wY+GgprrtSPBEQyr0SfQlqf4Hkc3CZVRXI2pk7+C4XGCeNT9jVNagcg2YjSrc5W66z05u3tE4kps9Wa8brNFXrINDJ4TCQS4nVeKVX5Cg2x7Dl1XU+aN1uumEoS+cHh7BRTS5dIgaTb9soJmR07Doq5KKMhAD3iTVVuENCKqY5DJzSPA/RnwT39xCZx33Kd1fnGnCZ9j537DsDvGULfseOwcAkYk+07TfjQVr6U8HOVjClaMVWrQzX73sbXVS3VykwlUhlPrphKbxNVgvSLJot8xZHuF4lqqrZWIAyqk/lSgEpFvhEEnxOIxQKSJK6B48TKagMnUlUvuUKeMSUnQ0jOFABMdMVUX2/gOLHoCyAEnxOc3KdCTBVg0QcA5syZhEvm1uLTk4fwpWkDWDbWh0WzmlFVJbdlq43NaMUUIC3Q2KqqUF0vVwe7qEp+opWPWhw0WdOTy+lAZ1VphZ8TojLo9em2vNopkiudYgpQX2xgSIWe8RAp0MBQvqDzpTZtSs0wJn3eaFhQZ2AoFBgxNQpABhDlQExFgiGJKGvWnuDrXeFRJaY05PeEmDJSxBTJrqLVUj09HnG73Bv4QgSfEwypDCQHVAaK9EDZVcSBYlN7KjHVMnmScB46A4n9niFdIep01kX9+MLkTGU7eBSJqQpQTFkdhc3JMlkscLgEkrn3yDF0HhCUEmOnn4bmyRPF40j4eZhSTNk11AA0LjxPmNzzCWDnPvl9EwiE8e67gipo5swJsNvL+/fyHOmAp6tH9b6gq/Jt25A+D0pWhENZ5p2qiPjCuu3i/mV6iSmqIt/JfQK5cGz3u7Cb1C1yljRVLjNlTE2cPiljSPDURQtgddhT9v/sys/KFKTVbjcAwGbMTTGll7yWiCmBVKULQAwrialk8Hk4HMWOHQeRCwgxZbZaYbJY4G5ugilZUEJN2ZortMLPAWAddR3RhQjS4SRFTHUeOCTa4ggKoZgibQFBlTmBmbVh/Ow7l6K376949rnv4StfuQRtbY2qgexKgqf/hNQ3zV52kew6rWmUxlikP6dV6xxnTEseq4H0i4ep4iY2pzp5T6ryBbw+3UQ1WQiIRSKqBXS8lPqOEVP6kG2BBobyhFa+FAHpS8xmE1wuR8rzDAynAhgxNQpAVEpmq1WUPtMy+HAgNXSzkFAjypTQO0l3VAmTAVnwuUZgKQk/N5mMYoUuYoeg86W2bZMG5+VOTBUiA4NAuTLr6x9QHSjTBFZNU/EGNo3tbSn7mie3A9AfSLzpL/+A6sRZYcGhiamGAuVMZTt4JBPsSlBM6VEljQRELQUI1/GhbTsBCDbVM5ddKD43KFr59CumpkxpxcQJwm9yMmCCz586KSY5UyaTEXPmTEx5vqyQSODpB9dA7b4god2BYAQBf+a2RyuLLhIIikqiHTsOobd3CABw0UVn6qpcSFfkO7lP+G6PvrNHRvgM0BlTaapcqhFTY+fOE/dNPfM0mVVODWcuu0jc9nuGxO0qhSqTVMaz51iVT2+/GIoJpJXdboXVapapQ/2Ula+uzolp0wSiffv2g4hE1JVGmRCkM9mcVTJbmTLAeiSg+x8lEXj0aA8OHhaus1ZHDFYuM9lCCGoAiEej6D50WPb82GlT825v+uy1qzGm9bNY/dhWHPRaEKVO02azYNWq+Xj4ka/g6LE/4FuXt+KcJj9aKMsf6W/b2hrxiS9/AquuvFisXnrDD27Gw288gU98+RNoa2tUDT8fHhgUCTh3c5N2BedEIm0VZZmqVEUxZeA4kTwMen26ieq6sURp16lKYtFKbrpdZ9BGtgUaGMoPHMfhnHOEBbDOzgHVokwsAJ2BgRFTowJ0hTWiUiqVYgqQiCmz1Sqr0EdD7yRdXTGV3spnMhkxeFKo7KemmNq+TVpFrStzYqoQFfkIlJNJOl+KxnC/lEflaiyMzU0NDUkrXzQcFicsRDGld3X2pd8+pjpxVlpw5NWPCqOYynbwWEmKKT05TiMBvbLu6+uXrfbT1h5PsmJV0Je5FDrBqlULxO3DPgsSfDzlmNGWM7Xn5VdV7wsbJ/xtXZ36K7eRLLrf3HALYlGhSl5o2C/eW4lEAi++uBMAUFNThc/cdE1GhRKpyBcNh9F7tAMAcPSd9zQtcjR5m8nKN2vpEnz6gR+KhIE1+Z5aIcGcyYiZFwlVe8OBAHa98LL4nFNReKEqqZiSVeXLwsqnt18MhKVrsLa2WmbloxVTRC0F5J4vBcgz2exOJxooVWmfSk5QrtDKmCL4v6c2AwA4AzC+OpryfILnxb5qsLNLVuQAECoW0rh+zY/TEpI0+vq8YhVILQSDEfT1edHVNYh/Pb0dTx9z4Vfv1+M/R1z4099eT6lA1NZow8KmID41eQhfmi5Y/s6eVodp08Zh/4Hf4B+//iw+PzuKa6Z4xH83LDTiH7/+LPYf+A0mUAtuvt5+8TsgOVU1zY3YvX4j/nnPj1IUeH6PBwGKZAXk/WJIQUYqYbLb4DTH0WSLodESwdy5k2EaOI4tjzwER7AXTbYYnGbhOjUYDFj3q9/hyK49IuGoNd6g88pc9UwxpQd6x0OHqH6Robwwa9YE1NQI95maWgqQV3hlxBTDqQpT5kMYSo0hRa5T5/6DpSWmqPNxNTXKVpgJ9E7SzSZh8iILP89g5QMAT2cnWqZMgtlmRXV9LSZOlAZwW7dKxFQpMqb6+rwIBiNpqxbEeCAYNxQk+JwgxTKgsfLNx+MYHhiEq6FetkJbSHBGozj56Tt2HNFwGONnnoG6cWNgtlkRDYXx5I8fwnUP3Z/yWqUaavf6jdizYRMmzZsNV2MDvL19KWXN6RDWyWfNw+QFc0dc+pzAwHGYNG82apoaEfB6YXc6VQPWEzwPT3ePOHgkv72JEVMZIQsB7uvHoe07U47x9vUjFhHUTrRiKpOVb9Ul88Xtwz4L+HjqNUETU6MhZwpAyn3hHxjATW98F4BBVDjpRYLnse/1N7F/81s44/wPoaa5EePOmIbj7wlV4N4/LvVBX7r9s5h1fZVmGXOL3YaGCYJasuuDQ+CT+V0n9+6HOSGp1ejVY3n4eXrFFLHKheIczBwPW5KYokva79mwCQmeh4HjcN6nrxIXWN5/9Q1x0QMAquvlig5CENHh59kQU3r7xf4+6fepra3WtPItzkO+FAAEfdLfYHdWKxRT+SOmQmmsfADw/NqtuPnmjwAAJlRHcMCraBsNBnDJtnWou1f8TQEhu2vyWfOgBCEkM1Wb7ejoxebN7+OCC84EANx995+x9rltsmP6+rwi+USyvuIJA44MW3Df7X/CwPGTmD17Ij784bNx6WVnYeFCiTisMgmWvzU/+Cgi37kMFkv64bfVakJzaz1iEIhDWm021N2LujGtqK6rhcliQTQi3Td7Xn4Vr/7572I/o9UvhvwUea+imGoeW4vrpw7CxAGY0o7vXL1GcYQHkWgcfzrUAF/UCLvLKQtjV8uXAgAvpZhS3l8M6iBqNT3jIYbyBJ0v9domdWKKKaYYGJhialSADqkk9jl6UBcqumJKyjjQypnSu8JjsQi2j2zCzwHAS5VMrhvTKltZ3LXrsHhsplLhhUBHRy+mT7sB8+fdjPfeE/Io4vE43nlHshm80lkFX9RYkAwMAp+SmNIYKAKS7c/ZUF+UEs61rS1ihknP4aPoPih8NxzHoal9AgBhYv3OixtSXqtUQwHJEPetO7Bj7YuqZc3HTj9NtDW0z56Z0dqjF6Tq5I2PPoJrHvg+HC6XJimlHDxWUvh5XVMdMmX483xCLIecLZz1tGJqAEPdvRg40Sk7huRLAfqtfA6HFRdcMEt43yiH/rBRVGTQ2L37qKimGA2KKQL6vvAcPgguqWLq7c2+khwAvPvKa+L2jAvOAyDcA22XfUrcT5QuWgqllimTxPMg+VIAEI/FEOmXfsPayVNF5RVt5YsGFRlTFLnvbm0WrXLEDkfbA+mcN3LvfuSbXxefn7roLFmf5lRMnCXFlHCxBwJhBIP6r2m9/WLHwQ7pb3JXaVr58hF8DkCunqmulhEMyoIZI0GYIkPUiKmNG/cgEBRIlnanZH8joNvW9jmzxDZcyu5Khd6qZXPmTBJJqcOHu/HAjx/Hjh0HZf8IKTVr6RJcec+dstd/7Y+/xqylS7Br12H88If/wOJF38C1330JL5yoxgcKyx+pzJUJzmQYupLQlI3BmhoxdaGk+nztb4+LfWC6fjEaCouWQLU2sq7BBVOGGYLFbIQlIdzvM5acq8sC6pNlTDFiSi92r98oq6BMoDYeYig/nHveDHFbSzHFiCmG0YC+Pm/G2ACiLs4FjJgaBfD2phJTNoewwhWLRhGPppef5xu0Ysrd3KR6jJhHkGaS/uxP/1ucoEQp0imqmTFFE1PSwKxuTKto5YvF4ujo6BXDb0tVla+joxc7dhwUg5IHBobxzW88Kj4/pz4EIFGw8PNZS5dg1U1fke370Kc+rknEkIGv0WQSc1QKCTpfqvdoh0hMAUDzFCnDhy5f/u/7fopHPncj7lv58awGYbOWLsG1P/lhyn6tiXM276tWdRJASraH2uCREFMcx8Fozr26UjngysvmiLf6P5/bjfnzbsb8eTfjm794C71BgXzmOANuu+2KnN5fljGVnNgoVVORUEiceMqsfBrBvgBw4YVnwmYTlI2HfRYABiTiqVa+aDSGnTsPAQCmTRsnSvJHExobpbawlyoQkQ3eUxBThBAYjnHoCwm/c4tdyAfSIgTkwefySmrmhNSXzb7iCpFAdrdICw/KhQv6Ma38CPHCBWniAKNBfj/OuPA81XvX4XLiQ5+8UnxME1NtbY2YcfoYNNliqDIK18jQUABz507G3LmT0daW2QatN6dnkK4qWFsta5OJYorjOJx9tvBddnT04sSJfuQKmZXPJVn54tGYjPAdKSIBaSFGzcoXDkfx3hFBLeY08zj+ygv4y+3fFTPllCBt+MVfvE4gJDUWVfRULbvjTul3/+lPnkBcRTkJSO1+tcLm6WqsT+lPuro8eHfQhv9LWv7+fbAKjzzyHPoG9OWCmq0CIaskpugAdHdLk0hMRcNhHN7xjq73BiTVlJri1ahTyXtir3APN4wfh9PPWyzu12XlY+HnWUGZERoJhbIeDzGUBkQx5fMFZIvUNBgxxTAa0NHRi/ff70jZv337QXHsP33aDSnWdr1gxNQogMw6p8iYKraND1BXcKlh9/qNePn3f07ZTybp+1/fLO4LhyX2VdvKJw0UvdR3UjumRSSmjh/vQzzOi/aKUoefk8/v7/fhxRd34I033gcANNjimOqKFCT8nAyclflfDqdLk4gZosnPIuRMNSZVUQDQd/QYuj6giKlJAjFl4DiMnyl05p7uHrzx93+rqqHSQW8VrGwDctNVnQQAJBLw9Q/gL3fco0mmxVTK3I9GTJnSimXnTgYABGMG/OyXL4kKg117OvB/HS5EklzPF764AldcsSjrz3DKrHzCJI2e5ALAlLPmiQqKUIZgX4JLLqHzpQRyUE0xBUgB6AAwf/7kLM6+PNDU5Ba3s7XyEXh7+3Bst7DaO/b003D5N78uKpSODgvfH2cA2pKqKTVCQK0iH5BUXk2SFBdE8VTT1IimiUJ7EYtGResfgZYdOhST7ktaNQUA8y9bCc2KsRSpTKxGbW2N2Lvv1/jR52fimike2JM8cmtrLbZtX4Nt29dg775f6yKntALlDQYDnnxwDXav34jBQYm4qK2tRjVl5SMWshkzxsPpFMYBb765L+PnpoOWlW+wsyvl+x4JQtTE2qZQTBk4DqedsxCD9jHSufTux85161E3bkwK2U9eAyRw7jVX6fp8rYyvKVNaceWV5wAAursH8eijL6kel21/Qo/P4gkD3jniw9e++it8/ut/0nW+BHQlO0DK0gOASQvmom6skLV5ZMduxDQU52og7aQ6MaUdRUCDJg3pQhRaiqnQsF+8Z1lVvuzQMmWS7LHFZkNVrXrOK0P5oL29GWPHCtf6G2/s1SS96Uq0jJhiKFcsWDAVs2cL87Rt2z4QnRAWiylFXZwLGDE1CpDOylcKYsojs/KpK6YIDJw0eHvlsb/JJukk+BwAQtTkwlZdrTrZpxVTvh7pHMZMHCcqow4fFlZ3iWLK5XLAbC5NlBpd8pV0OPd+X5JiL2oK5D1jKv3A2QAtIoYEqwKAswg5U40TJMVUz5Fj6Dp4SHzcklRMNU9qFwfMR3ftyelz9FbBSreSnuv7Ouvr4O3p1STTaKXH1LMX5L16VLHw7buvhjF5n2/rt6O3U1rd93uGMBQx4pUuSbX0m9/+P7S01Ka8TzrQ2We+vgHMWroEi6/6aMpxREEx5WwpNypd/hXJl4pG4+jwCxMxtYwpQBmAflpW518OoBVTPT25EVOAPAz7vM9cLW4fHZYmshMUwdU0ISAjpvYLxBRpt+jw82CcE58jUFPTxqh8nVgkIlrlQnGp/SM5Uwmeh69/ANV1tWnvXQKimGpocKXNDAQAu92iezJBAuUf+dyNePupZ1M+b3BQIlaFjCk3AIE0JZmOdL7USILPAbliqmF8m0jm9h3LX74UoFBMUYQxsVV++X/W4HhEais+fPk5Qlvb3JRWDVWtqJ6oBa2Mr29+82MwGgXF38/XPI1QKLUyJ5B9f6JUuBDLPFEZ6YWywi5t5VvwkVXi9oEtW7N6X/K7q7WRJp2LJbRy1WiSxlpaiilAqKwKpFplGdKjeXJqRdiGcYUp5sKQP9D5Uq9r2PgApphiGB34ylekPufXv1orjldqa/MTnTM6Z0KnGOjywCIxlZTBKwc+xYC3R59iCgDaZ88St1959C+ySTpNTJmrJWXT6ectVs3/oYmp4X5poDblNKljPnJEGLDRgbSlyJlSfi45nxde2IGtOwR1UIMtjrnt+bUE5UrEDFED9hqdlaNGgsb28eJ275Fj8HR2I5yctJDKfBPOlDz5x955N6fP0VsFa+rC7Ighve+rddyspUtw2uKF4uPr19yfl8yrYmPKlFZ85jMXABDUUrv6bQgMSaQHmUS/O2jFpm2C9LehwYU/PHpTVllmJGOK53n4h4YE8jWNguLDt31NJP60rHynn96G9mQ23dZ3OhBNWr/UqvIBozMAnUZjo7SynqtiatbSJZi7apnqcyf8ZhBR64TqCOh8IEIIcEYjxk6fmtzXLy6skHbLbqKJKen6INdKXEW9w8fjYv9otlpFC3k4TiumEoLixmBAx7v6SZxCTpxJ/s8zqx9GPCac/7xLl8NgMKQQUyR0PTjkFfvPfOVLAUCQypgad4YU2J2OXMgFfDwuVqIli2tKS7QnwsETEX67BXMmYP7KC3S9t98zlFPVstbWOnz2uqUAgKEhPx555DnNz8i23VcuHJJYhgSfIZBPAVrRDMgXBxuoKrMHtryd1fsSYspkscBkkROvehVTw7396Dl8VLbP7xlCLKIdMUHsfA53DTiTMZtTPmVhNJnQOL4tZT+d68VQnjjvPCr4XCcxVc+IKYYyhNtdhas/KVQx9niG8be/bWTE1KmIRCIh2ldcjQ3gjEZY7EL2QCkUU37PkDjp0wo/B4SOtG3m6QCEVXYfVY0FkBNTCYN8cKKW/0MTU2GvT1QbTWiXVFtHjwiKKZqYKlXOFP259Pn89/9I5cg/dv7YvIaN50qY0CuyrgxkYz7QlCSm/IMeBIa8SCQS6D4kEHZ148bAZLViwpkzxeOPvpObYkpvFaxlN/xXVsSQ3vdVO45MxJT2vZFmXpUC3777alFpsK3fjgjPIeCVBldSuXIDfvXEXpw8KUxIVq6cj6997TLdn0MypvyDHrSfOVMX+UraBy3FFG3je/3tI+K21qRx374T8PmE9nY0BaAT0Fa+XBRTohpTI+U+ljDgZEBo02ssPNwWXkYIzFq6BN958UlY7EI5eVdjvXjPkfaIWO7iPESikIaWrYwuJLB7/UbseflVhWKKh8FggMFgwBnnn6P7b66uz07ZlwuGBwaxf/NbAIS8xInzZsPjkVv5iGJKXpFPIJDC4Si2bz84onOgra/jTpeIqb48VuQjIItpVodDwxJtwBGfQIoYOeCKq5fqet9Nf/kHVLO7MlQtu/XWK8SxyK8eeQ5er/aYKtt2Xzk+G+rR93olaEUzIFdMEQR9w2KVTL2glXLKdlJvtdh//usOtCTk51flrknbn/r6heM5jpPZVBm00TChDcak+j/opca3jJgqe3woqZiKRmPYskVbLSmft5Q2hoSBQQ3XXbcUDofQN/zpsZcRCIRFYsrhsGasNqsHjJgaJSB5FM76OjhqJMKDrnJTivNJR0yNPf00mJODmyM7d6c8b6OsEXRVPkA9r4EmpkwmIwaSZb3HtrjF/UQxNVAGDTz9ufT5bN52FJ0B4ead0FKNj350ccprc0WuhAkdsK+X3MoVFrtNDDPuPSoF6HUfPAJAGKw2T5yA8UnFVDwaw/H3c8tQyVQFi0Y2xJDe6lrKFfpCZV6VArRayh/msatfIMsDQxIx5fd4xG3eUoXrr1sjPn7gwesxY4aknEsHEpLr6x/QfX0Si5ddoyofsfEBwOtbpRV/rYwpnuexbZtAAIwf34jmZreu8ygXyMLPc1BMZVJjAhBzpgBgQlUYhBCYmQwbV2bKkHuOhG0TxZRAKqUSUxGNwhiEmDIlVR4Wu01GTFmNme9/ggTPiwqmYlmNtj2zTty+4Ppr0DJTUhrX1btEa93woFCRr67OiWnThO9s+/aDGSvkZAJdlY8uONGfZysfIJE11iqH5jVFX0ent1oxPDCYsa196bePqWZ3pataVltbjS/fsBIAEApFsGbN02nPPdt2P1UxJfS7fX1eBIPqdkGCSDQuqgaHFP21r28ghaQ9uHV71nlg8uqlcmLKUW1THq6KCROacNNHxuOKCUOotUjXYbr+1NsnLVKyynz6QOdL7X1NymelK2gylB/q61044wxhnLNt2wdpq7iGw1Fx8YtZ+RjKETfQNr5frwUA2UKa2z1y1VT5z34YAMhzpuh8nlAJFFOAREw5XC5RvaVE+5wzxW01YmryHMmqpSSmgFTbGR1+ThNT9VXSZUwypugQwbq60hNT9PlY7HZs7pGCX797zyfzpprKlTDxUiuyrgJnTNVT1oPeo8fE7W4qZ2r8mTPEPIWT+w/knMWVrgqWEtkQQ7L3VShI0q3QFyrzqhSg1VKvHYohwnMIDfvBUwSy3yNd9w53DV56aSfWPPQUAMBms+Avf/1GxhUWu8sp2kx8ff26yddgctJlra5Kub+cTjvOS5ZvPniwE8c6pfNMN7nbKsuZGl2qqUZZ+Hn2ZXz1EIJHqJypMZZhPHbrXdizYVNGMnbhxz8CT1e3qJgKxtXvDz+lGKJB2geyEDJm2lSEFFY+NWjduz1HhHbJ6nBo9m/5xLsbXhXJtRkXnItlN/8/8bn26VKujH/AAwBYuFDKOBtpvhQgt/LR6NMIsB4JaGJK65rq8FtAot7anRFse+Z56FFD0dldf7n9uxmruH71q5eiulpQ8D36h5fQk6FaZdqqiirtfkgjY6qjoxfTp90gVjCaP+9m3HHnY+Jx7/YAd/z+ffiixuTr5GTbjAvOTTm3SfPnZq22lSum5BOKC87MPDGmSfyJziiunerB+S3DsKSpygnIK/OxAHR9aJ7ULm6/9+rr4nYxFFNtbY1i9VG1f21thY9/GK045xzJcv36a+9nPL6vT1jEZsQUQ7nhoovOFBfEXn55F/buFRaulNEDIwUjpkYJ6FVAUqEIKE3GFCAnyrSsX+1zpFVfNWKqtknqzOJpIhfI4FWpmBo8IRBTLos0OFLLmCqdYkrdyme223B02IzuoDDoPPPMibj11itUOvvsLXXZDpwJhgcGxQl5oRVT9PXbc1gipujKfGddfim45GD2aI75UgRaVbDUkA0xRN5XSZqlW6EfaTZVuWDy5FZcc80FAIRCAzv6BUKAVksBtJUPYpXIu+56TCyXPHv2RPzoR59N+1m0asXb26+bfCVtFMdxsDjssmMuvniOWBRh7XNbxWuNvF4LozkAfaSKKT2EYF/ICH9UIJ/G2sLY++rrusnYHU89A3PyEDpfiiaPtKqYilY+qxXOhno46+sQVgk/T/lsBVFG7t3uDySSvLqu8FajaecslGX80KTapDOmiNvEyicLPh9hRT5Arpii0X/85IjfO+WzAslcI7NZZk2kEeUNOEHZQv0H39OthiLZXTvWvpi2iqvDYcXXb/oIAGFs8ZOfPKHr/LX6E7VziQTllUPp6nodHb1iBaMdOw7iVw8/i2hy8a3NzaHxzHnS66iFI2IFV95P9uqqrK3gWla+a6+9EKePFdr0cBz4yOX3yUg08u+CjzyEZ4854U1mghkNwPyGEK4/bRAzakMwcAbV/tRHKaZc9YyY0gNaMdXx7l7x+is0MUUqkpLqo2r/3n3vETQ0libLtdxBFsCA9PlSBCRnqr7emdeYDwaGkeIrN14qbv/6V2vFbQ9FTLndI89NLk25Moas4ZURU+3idikypoDUSoF9lCWLgASfh/x+dFEDfYKoX7qY4yp5IgRkQhSXEVMcBju7AAAus7A/Goujs0uwOpQHMUUrpqTzsdhscJp5NFilv+cnP/2vlNcHgxFMn3ZD1mU3ycD5ijtvES1zgDBwfuqBNaqECR+Pw9c/gJqmRtQ0FjZjilb8yRRThyRiKh/B5zR2r9+IPRs2YflXPo/lN6R+10roJYZ2r9+I7sNH0HbGdPA8j998+WZ88NY2zcnQSLKpygnfvvtqmJKhtat/9iTcnxAUHv4hOeERi0QQDgRgdTjgSBJT4XAUn7nmZ3jr7dWw2Sy49baPYu3abVi/PjWYGJD/Fr7+fpF8vW71/UjwvGyCRpOv8y5dIe63V1fL2ko6X+q557aBs0r3iVZVPmB0B6CTjCmfL6BZdSwdCCFY09SoSjIleB7RcATH/Bac7g7D6bRj0aJpGK7Wab0ckibfoZj0/vTg3GS1wMBxKfcXrZgi4ep6FFMEr/3vv/DOixtwaPsuJHheVtWxur4OgDohlg9I9l4J8YQBMR4wcYCVOneiGMtn8DkAxGMxRIIhmTrM092DWDi/VWMB+Zilc/8HmtfUEZ8Z45PVHWe0ObD+iaexZ8MmTJo3G67GBnh7+8TfKxd84QvLRVXC3//+Ko4k8yn1gPQn6c5l1tIl+Oi3bpO97uoffBtP/PAnqn3w8HAQW985jsXzxsNl4dHW6EBfWCCPCMElt4LLvy9yX1x+x83Ys2GTru9FTTE1eXIrfv7fXxL3P3/UhmeeflP19XNbJmG/14pDPgvOagxgQUMQJg6oMiWwfOwwZteF8EpnVUp/SjKmAKaY0guimIpFIujvOIGB4ydR09QIV0M9LHZ7CgmaL+itSOpyFV5ZOhrxIboi3+v6iSmj0Qi3u0qmRmFgKBXGjKnD5ZcLBZs6Owfw5JNSn8AUU6coNBVTJSOmpPBNd3NTyvO1rS1i/tSxd95VHST1H5VyXdQUU0rbmVIxVZUMzSSKqQBvxl3PPY5ZS5fIrHPlQUxJ52O2WWE3JmDMcPdlU35ciWwtDYBEhlTXa5dSzwdkFfkoQnPwZJdYmY9GrsHnSiR4Hh/oLKedDTFEKuUMnuzEgTffTjshyNVqWU6YPFnKlhoY8OG3j74slgkPKhRTAOAfFMgqopgCgD17juIuyrryx8du0bTc0hMXstKuR7VAT7qsivwUki8VCITxyiu7YeCk4gu8RlU+ADhypFscOI46K19SMZVL8DmgT4357iubZPlAy5fP1X0vmeIS+bPt5Tfw1INrUqrwtc+epRqqTBRTRrMJ42YIBTdk4eem9JP0d17cIFPX0IU6nPV16OvzIpwhxykYjMiqKumFqChTrI4TxZeNqlQ4PDAIjuNEK9/x4304fjw/JDadNwQA/QWw8QHyMYvFbhOrKCpx2Cutm65YIaiH9KqhMsFsNuG2b3xUfPzgA//O+j3SnQtRNbkUpIuzrjatqulf/ysVRpnoEsjjoM8n9sf5toIrM6ZMJiP+8tfb4HQKCtN3B63YeVSblCX3dixhwOaeKjx2oBb7hyQSo9kew9WThnDP15dg7FiqHafur2IUGBgJDByHyQvmYu6qZZi8YG5J8h+NJhMaJwjjpp4jx8DH4zKbbd24MUU/J4bMsNutmD9/MgDg/fc7dPUP9DHMzsdQLvjCF5aLi9G/++0Lsvm4sljLSMGIqVECWqFUFlY+akKoFoCeycYHABazNBmMKcaXarYz+kaYfs5ZWPLZT8LK8eJq+FDEKAZu1k+VKguVriqftmKqGMh2EE/sAkaTCdXJKlCFABlg8TyPPipcN5FIoOfwEdmxfs+Q7JiRIt/EUHV9rWiB6D2SqhpUe/9crJblBKVaKs5JRITSygdIKipHjUs2+f7v//4/vPDCDgDA2LH1+PX/fFX18+hwXDqbJBP5GqQmXXQA+uzZEzFmjDBJevnldxAKRcBRLHEijWIKkFRTDQ0utLc3pz22XGA0cmI7mIuNjyATIbhr3Xoco3Kmli2fK91zGtX8yD0X7OkU9x3ZexiDJ7tkFksCtVDlKKXsIWpLGTHFZXe/K4mpjo5eXPONp+BL2hSj0TjOP+8Oma0pF3UroK3OJIovK3Xu/kEPZswYD6dTyCjMh1qKQJkzVRRiyuHA7vUb8c6LG1KOO3hsAN29Qr95wQUzYbOlV21kg09/eololf+//3sLe/YczfAK/VCvNCg9ly7H8D+PbxK3JzkFYqq2tSWlemUm6D1OSUzdc8+nsHChMHYaDBmwobNKtU0nUPan3qgRz3a48K/DLvQGpfHdR1bOxt59v8a3vnUVrFazrB1XknflhFlLl+DudU/gxkcfwWcevBc3PvpIVtV78wW6Ih+xGfcfl+7PBlaZryxx1llTYbEI46PXddj4AKCfEVMMZQaTyYgvfkkoEhKPx/Hb366TPT+YZysfI6ZGCeiJQN3YVnG7VIopD6WYqlHJmNJDTJESzQDg98mrC6rlNdDh5xdd/2kA8nwpb5STwnSv/Yy4vy6NYipzqGPutra6NBlT5Qha2eBqKlzGUWO7oDDydHbLrCKzli5BExXwCQgr6vkcBOabGCIkGwD0HctMTAHpJve9mtlU5QKlWuoXv/g/WZVQvyeV9Agk7UdGkwk2p0QQJRIJfO76NeIK4ZVXfgjXX59aGp7OIKEzWoD05Cudm2NzSp0lbeNb+5ygoKMniVpV+QhGYwA6PcDNVTFFkI4Q7PzgEPwxTpyULlgwBbXuquQ9lwr6nqujVtr6+n0p9jYCtck9nfM24cyZAAB/MCIuZliNiewy92SKDoEYrW+qg9MskGtv7zyK1157T5YRlAspBWirM8NJe7vFCHAQPnd40INFi6RFl3wEnxMoc6byuSBAg15MszkEgo0mrJ/88UPiNfXMU0L1MbvdivPPn4F8wGAw4PY7Pi4+/vH9/8rL+xKMRNXkmnI6+kLCvdNqj8GeLAagrF6ZCXpViqFh6bc4a+4E3PWtTwAQytqvPeFClOcQ9Pq0Xq7Znx73W/CXAy68dLwKHq+ggq6qsuGH912L997/FS46dyqQvKZzsfLpUTGNVOlEVG/K8W021XvzBVIIBgC6DgqRB/0d0v3JKvOVJ847T7Lx6cmXAphiiqH88OEPny0qXp9++q0UlXa+rXwsY2qUgJ7EckZpJapUVfm8dMaUwspn4Dicdo7gReV5Hsf2qDfINDH13MO/x7Ovd6TNjqAVUy3NbjiicYyrkrJS4rwBTTbBbhG2NQDwANC28pFQx3T++VxznpSfK6vKVyTFVLagc8xcjY048f7+vH9GdV0tHK6kcuOItEpNBoHKCvEmiwXXrb4/r4SNVgaXf8iLx7//46w+h9j4ALktUc857NmwCTf+4WFMmj8HAPCrz3+1YCoFA8flJZtFqZby+YJorZEsegGvmmKKqsxXUyOb6HR2DuBLX/wFnvjPtwEAP//vL+HVV9/FoUNd4jFyK5+cmEqHkF+94tQqmphauw2AvE1NZ+UDgLfflu6Ls86ain/96zXd51QqNDZKv1HfCBRTBIQQVKK/4wQiwRCODlvQaA+C4zgsXTob//rXRvQdOy7LlwPkuXfnfuUScb/R6Zbdm0rQk/uDW3fIFFPEMtp54BAGx1vR2FiD2FA/hnoiujP3lIopADj/7HZx3wsvqS+25AKt7C5a8WU1JhCMGzA8MIhFi88R9+cj+JwgVTFVIGKKGrNYqwRiiti7Y5EIXvvb42Lb9Pzz2/D5LywHAKxcOV9UWI4EV1yxCKefLlyHGzfuyavqDMi9wAVRWh30WtBgC8JgACY6I3jPYxPzo0j1ynQ5b57uHt2KX6KYsnI87vz82aJC8f4Hn4Tz8hsAAIE0xBSg3Z8OdvfitlvX4Pj2bfj+9z+NG75yCUwmIyZObMY///5NHBkaxKs9LvRQxS30YNbSJan5mV3dePLHD4n3sp5j0iGT6i3bLK+RooUiprqTxBRt5StGZT6G7EHnS23apC8rlZ4rMGKKoRRoa2uUXXvfvP1j4vZLL+5EW1ujbE6cbysfI6ZGCaKhMAJerzipJyiVYsrb3w+e58FxnGxFadbSJbjirlvF3CmO4/CNf/9ZdUBAE1PhUER1okODJqYuafOlZDTNrg9hdr2QhxDjgVAoCpvNrGnl0xvq2NDgGhExNTwcRITKJzHbrFm/VzFAq1FcjfmX1xs4DnNWSIqY3qTCKG2gq8GARCL/g0BCDK386hdx8ZeuBwCs/92fsia/GibkRkwBwiTi8I53RGKqsX18QYipkQ7SCdTUUgBkiilVKx9VeauqtiZlwvvkk2/i9797AZ//wnI4nQ78+S+34fzz7kA8aamjrXxKxVQ6hCgVJlFq1dZWY/FiQXHy3nvHxMBjWrGh18oHjJ4A9KYmiZjq7c0+B0kvEjyP7kOHcbRhMhY0CkqJ5cvn4j9Pv426MYLSd+BEJ577+a9SCFKayA8nzKlvrgIyuVdWxgSAk3sPYGDuBDQ21qDabsIPV3xMNzmrRkydPUNagHn6KfUw6FygFeYfloW38wjGOfgHPWJFvkgkiu3bD+btPII+OQHRVygrH5UjaK1ygDMaRcKy92iH7Dd56aVdiMXiMJmMWLlqPm699Xcj/vw77/qEuJ1vtRSQe4ELorQ67ItiYZPwHU1KElOARMY+/8vfYMWNX0xb+EFvPynk8CWwdOwwGmuEXKlXXtmN3zy2Cbclial0iimCTGHwX//6b/A///M8HlrzRVx88RwAQHsNj/EuD7Y7XfhNbbWukGdxAQtyWzBRMT12610AkPGYTP0e+S20oCTGCw26Ih9RTA1QFTOZla/8wHEczjlHyDs8ebIfhw/rK67AFFMMpUQmwcbDj3wFP/3Z52WCDWblO4VB50wRlCpjio/FRfUCyZjKVvosI6bC0YyfSRNTmYLDTRzgGxZIqrq60pSxJRMt2sYHAFa7Xe3wkmOoRxoo1+hc9dULktVAVymad+lKzFq6JO+BrnqR4HnsfW2z+Li2Nfu8IFoBolaZMhO6Dx0Rt+nsuHxh5kXn582O8K1vXyWqpR5aLailADkxpRZ+HqDsfVWUuorGzTf/FgcOCAPtxYun4+67rxafE8mHcFizrL0aQrKMKaGzXL58LoxJddTa57aJz8sVU+kndd3dHrFDnj9/smoOUrmBVkz19HgK+lldHxzCiYBZzA1ctnwuxp0+TcxI+eCtbarWS5qYOtnRBT0gk/uoSvW4k/s/EAdMbnc1DIDuzD1f/6C4XV1fi/p6FyaPEdQ9/SEj3t11QOulOUHN3qtUTAGABTFMny7YubZvP6ir39QLulgAAAyc7NQ4cmRQKqZqx7TCZBEGwT2H5VlPQ0N+UdE0ffq4EWe6XXTRmaL9dseOg1i3bvuI3k8NueYYknauK2hCICb89hOqozAa5ARL37HjGQs/6EVoeBhnuMOYViMozwcHh/HZa1fDSilM1VSwWn9Xuvvr3XePYfmy7+BjH71PVMRyBmBBSxz7D/wGX/nKJTCmGdjpze7KNd+LRr6zvEYKZUU+QCiEQFTBdczKV3aYNWsCXC6hz3jttfd1v44RUwylRDaCDQIZMcXCz08teHtUiCm/X+XI4oAMjJz1dTCazZTqRe7H0hoQyImp9BWPADkxpQeEmKqvd5akqgqpMqYkpso3Y4qy8qnkhuUKLcLS7qzGdavvx4wLz9P1PoUYBNKTr/qx2Ve2IYqpWDSKwU59k2ka9ESseWJ71q9PC4MBH7n9Zox0kA4Iaqlrr70QAFFLPSM+J8+YSm/lmzhvturn+f0hXPuZn4n3+N3fuVrM0iGKFVKRTy/kwb5CZ0nb+J57TqrQSJNLfDxzO0NUU06nA9Omlf+koKnJLW4XUjEFAJ37DyKeMOC4X2jfx49vxLkrFovPH9mlboOjM/l2v7Ejq8l9LBxJOebk3v0YGMhtJS8WDotEjbOuDpdcMh9csl87MMipKrRGCjq7a+8bW8SqfEDSyuf14awFk8V9b27On41v5kXnY+6qZbJ9t/3rsYLk6IQDVLVMh0NGyCuJKQBY97xEHq1YMXdEn02rpR748eMjei8t5JpjSEjWBAw44hMmBhZjAmOroinH5VJ1Vw1jGqtxYav0e3z5S7/E8eN9cLgkkliPYiobPPnkm5hxxo3416ZORJNfQX29Ew8/8hVs274GS5bMVH2d3gWsfCxy5ap6KwTUKvIREJKqbkyrbHEln+jr8yIYTG1faQSDEXi92tUbT0Wcd56Uiac3+ByQE1OlqijOwJAN5FY+ppg6pTCk0gmWysoHSAouzmjEjAvPzXpAYLVKTlJ9iqnsbFz2JsE6YrGYcdNjvyxqVRWXywFzUiFAe8YBwGKzIhg3pFQiVCLX8uO5wksppvJVKSftKqfBACCBeZeu0Hd+BRgEenv6EIskqx9RRQX0wGAwoKFNUC8MHD+pi9BQgs7Zyrdiyt3eBndLU16UaEq1lNcrtTsON5UxpVBMzVq6BCtu/IL4eOkXPqt5/7311n7c+/2/AQCMRiP+/Jfb4HJXobpOKCeeTb4UAAQVVj6DwYBVq+YL7+ULyMJIDXRVPh02mNEWgN7YSIefewr6WV3JqlFHqep8NKFw9B31rA16Ba63x5PV5F5LMTUwIE2qs1XOEjtfdX0tLvvw2eL+3R1BrZeMGER18szPfilW5QMAmzGBcCCIRYtPF/flKxupYcY0XPvT+8S8J4JChTwrFVPNMmLqSMrxzz8vKRtXrJyf8+cuWDBVtJEdOHASjz/+Rs7vlQmZqleqEUi00uqQT7p3SHU+JRmbTdVdtSIvC86aiv/9802wJNV4b34QwOOPvw4AsBeQmAKE8d7fnnsfj+6vxfseKdrgzDMnYsMr9+Mf/7wDEybIs0vzuTCl9V4kML2mqTFFQUgj2+q9I4FaRT4CQkwZzSa4W5pSXpsPdHT0Yvq0GzB/3s148015m7Nz5yHMn3czZpxxI/p69auZTwXkki8FKIgppphiGAXw+0OIRgVxidvNMqZOKSgHOUCJiSnqfJp0qj3oAcFIrHx6EKYOtxsT4uocGXBveUS9UlQ+IA8+VyimbDb4okb88UAt1v7kpzi6cw+++91P4SMfESY/V1/9AD440Im+Pm/OlZ5ywfCgB3w8Ds5olFXlG0lwtp6sBmd9HYYHBlHlrslLoGs2SCQSGDzZhcb28bJql3pQ09QIS1L9lm2+FEFo2I+h7l7UNDfmnZiyuPR1EJkG/Eq11DOvHcPcVcvEa4HOvQsMSbY9rUD7dFkf99//L6xYOQ8f+tAZmDy5FQ8/8jWQ+p++/uyIKZliqqoKCxZMES1tL764U+xIAYDjpNVmPdf22wpi6k9/ejmrcys2aCtfwRVTB4Tco6PDUvt+1qwxeLZLuN5JeK8SpM3keR4ejx8DGqHKaqHlSmKq79hxhP0BDFKKKUHBqt+eNtw/gMYJbaiucWLFzHkAgGDMgL1HPbrfI1d07v8AfT2DQKtAUFiNPNwtTfjIjVeJx+Qj+NzAcZh62XKo5vsVKOSZLthiq3KgaqxbfKymmNqx4xC6uwfR3FyLpUvPhNlskt27enHnXVeK2z958N8ZLbsjRabcJSXorLEjXhPivBBZMMkZwYYTcQBcVvlRBHqKvCQSwKbD0ndqd0rjl0zh57nC1zcAf8yI54878aPv/AE3XXc2FiwQSP5PfOJcXHbZWfjpT/6DBx54HIFAOK8LU2rvpZbFqIZcsryA3MZRBo7DmcsvEh93Ke6PflkA+jgMnCiM/bajoxcdHb1wOuXk9ZQprdi58xA4jsNMdaHbKYtzk8SU1xvA7t1HdL+Oni8wKx/DaMHg4DCamtws/PxUg1rGVKhEGVMAMNTdI24b0hxHgx4QFJqYiihWnL3JjyAD7guu/3RW75cN6MD1AaqjmbV0CSYvENQDvqgR5958B2Z2deNYh5R1FIvGsWNH/kJt9SLB8/D1DaCmuVEkK0YanK13lXPbM8/j/M98Mi+Brtli4GQnGtvHw1ZVBUeNSzXAWw3y4PNjOX9+z/9v76zDm7reOP6N1Z0qUCiUAoUiLW5DOtwHjCFDtiFjxgYMmMJ+YzCBGTrDN8bYcHcd7k6R0RZoKXVvkvP7I703N2nkpk2ayvt5nvdpcnPl3PTm3nO+55X7/8EzwA9uPt5w9fJEliAnU0nITxc3g2muwy/0lrqQ4opRC7WCbuqTBKQLQuy4785kQnsTA16VSo2XRy7AxUs/wMPDBSOGtce2h3m4k+6I9KcWClOCfFRO7q7oJazGJ8gvBeh6TKnNJD8PDvZDvmBg/FzHCERGakOsSltQFoOfTvJz61xfxkh/mqS5hr08kJHH4O4oQS0fCWQJDA+vXjf6O+aEqZSUTF40EDu41w+te3TrDr8vDks7TJzHVHWXAj5XyINMB2Q+S7VoP8WhUXRHKDyrANA8O5xkDBIwVPfSPGkTkzKsco3VimoCJy/jgx9bJHnW9Zhy1RHknz4oKvAzxrB79wWMGtUF7u4uaNcuHIcOWVYVsX796njhBU01w0ePnpWakGyseqUxhBXu4kKcUNOtAJ4OajhkPsXPnxiuIGkOMTlDJBLATSA62NpjCtD1gL2XkIdWLadgzJhofDF3FAICvOHs7IiPP3kJY8ZG4/1py7H+r+OaioQB/kXSRQDaCSypVDOxZmydrLR0SKRS/jkEGE+qDmiuP+G+0pOeYeMX8y36XxSnH2Vom+eGD0HCnbv8NklxupX57pw8I7pNxaFaNd0Kim5uzqhdOxAPHiQa2aJyEhISgGrVNFEH//57ky/kIgalUoXU1Ex4ebmRMEWUG7TCFIXyVSoM5ZjKz7ZdWIE5hEJZbla2xg2dFX2wA4Zdn23uMaXWdiac5LoPBolUCg9f2yWuNOQxxXV+ZArdilOe/n6o1aUH/75OHcs8d6xJ2lNt3rBGXTuXOHG22FnOawePmg19MBSOILTg4OLlxRLOMvpYkGfKVyfxefFLqycIwles6TWV+iAWqU8SLfpN6lO7diDvLZWrlOBGjm7H1NPfD8EN6/Pvcwq9lEqS0P7BgwS89eZS/v3zVTPhJldZHsonCMVwcnPTyS+1c+dZnXV1ckyZCYm5eWspDh+exy9r1CgE585/x9vNW0uLfS3aCl2PKdsKUwAXzifBw2yNR6FCCgS5FOC/S1eNbmOsWISYkKWCfN0cKPG3YwBAL5TPsnwdnDDFhVIBwL10B2Slplq0H0vhRF39HFM+jio4Fjr2PVW5WiVXotiQbWuGUQkLtghzTKUmJBot5iLMM9WjR5TFx5z2/iD+9bcLNutUyS1rcPmjVi3dxC+L3/p7sUQpS3BwMSZM2cbDMl3gAetexRuMMSxfvg/16k7E/G828l5xwcF++GPd+zh06Avc3LDW+A4lmgmsmLPnDYpSgOa35ebthdd/XciHlJtKNwAAYAz5Odp+9obZX1osSlnajzK0jbtChZBAF0xb8j8MmTAEkZGhCHRRwd9JCX8nJepH1C6yH2vi4uJoMEynSZNaNj1ueaRDB20Y3zELwvg4uHA+EqaI8gKXZ8rDw8Xo/VcsJEyVI/QH7fk5ucXKa2MthO3x9PPFgd/WGJ2lMuT1YvtQPm1bnGVFB+c5KgnyC0zvMy9PWaw8T7rCVLqeB0nR5PCp+dqfoj2FKU5IkspkGGiF6jaWVCgyldCVEwSEAoC+FVcQ0BGmqosXpvys6DHF4V9YfccqMIZdC5cZ+YhBjCfahwJvqXNJTijQc7KVSKU61/MH2/9Co+iOJa5qtHr1Qfz551EAgJOcoXv1TGQkW5b8XJmXB1Xh4MavijtatqwLQJMX49Ej3X0JE8cytfF7QnEqlpQF/As9ptLTs61ayc0YhsL5aroZF6bkchk/6LH0ftsouiN6CPKYAUCH4UPQKLqjTvJzS3NMZSanAGCo7aERplQMeJCpQGZKqkX7sRRO1M1TCz1+1ajqohVTktRuVqlSmi5S7LVmGJXQY8qnWhBcvb0AGA7j49i79wIvGFuaZyo42A8jR3YCoBEqly3bZVmD7QBTq7Fi8Ub+fZ8+zU2sbR0cnJ34e7lQmBLrQWwpwokGd4FAmp6ejWnTfkOjiDd1ClR06NAQfyweic4BqXCWFX1mJT2MhZO7Gxp0bAcARidkODhh6Plxo81OojgIKikH1a0j7gQhvpKg8DND27grVBgTloKRYekYGZaGP5eOwrnz3+GfFeMxok4qRtRJxdIPOtp0QoTzAAKgk2OShKmitBfklzpmQeJzDu4Z6O3tZrJSJUGUFTjvdKlUCk9PFzNrm4au+HKEfiifsdnF0iJVEMrnGehvdPbVWMJPscIU5ykTFORjdB19lGogU6nbsdcno0CGJecVSMrRrMeYphQ4x+5YFyw8DcTFW+apAeiG8j17lmHWgyStQDvgD7WrMKU9V09/P7MeL91ef9VkpUO+QpFIwdKYd4QtBYHk+Ef8a5+q4r97vxpCj6ni5ZgCgMR7D/jX1s4z5ejqanj2gjGsnv6JyZnf2rUD8fIoTX6LXJUEF5Odja7LwXX2fWtUF9U+UwPe1ycuwtNkzT2uhlsBBnSyfEaYyzMVEaIVJXbuOFtkPeH1ay6Uz5bYyiuQ85iydeJzDk6YeihIgF7TLR8PrxiePRZ6M+l7TJmC8ywQDqQBwNXTQ+NxUCOEX1acUD4fRxU8HTTXQ3yWAvlqKbJsLExxYm2uwOPXUcoQ6KJ9Rj7OVljFi+n++UvITU0XXf3QGgiFqWrh9fjXpoSppKR0nD2r8YJr0qSWRX2BKVMG8IVIFi3cjsxM+3mZW8L9+wm4dk0z4dGmTX2dPoUtkEqkqNe+NaRyuc79O8dEEvCSkPEshX/tbqDvePt2PPr0no3evWbh9m1NyJpUKkWkvwpj6qYgOOsO1n30GbIK8xr61ayBl/73EZwLK7DePXsBS155A1mpaQZFKk4Yaj/ixSKfmaJqPfHCVHE8hw1t4yxjkJsZrTnIpTadEBEKU3v3XuRfN24SYrNjlle4xOcFBUqcPn3HzNpFSUrSPAOlUqlVcvYQhFjEVuHUn0AsSdoEfSjHVDkiMzkFqgIlX6HDnonPAd3QQq8Af9SKbAwAUCmVWD31I8gdHEwmeXR00g5ajAlTYhJ35uYWYMN1KeDuzeezyVFJ4Ouo9Xxw0vOYYmo1crOy4ezuDQ8HjRiTli/FlRRHdArSfK9hngVICvJFn2E9EHddk2hWbP4Y/VA+c4OIPLUUOUoJnOUMYWHivXasjaUz490mvoJuE18xmS/hyv7DuP3vadRr20pnuaEkxvYg+ZEwlM8CYSpEU8K5IDfPYGECsSQIBmQB1vSYAtCif2/+9bqP56B53x6o07KZptNrZkZZ6C11PskZ+Wrz8xhc3o5Wg/pp8oEYETf1E9obSgybmpqFhf/cxqxXm0IiAd55rQPOHTqNO3d0E7ya+k3mZGbC1dsL4UEO4PKH7NDLLwUAUmGOKRsnRTaGmHtdTk4+6tebaFF+Iblcxgs/tk58zvHkjqZ6VI5KioQcGQKcVQhwVsFZzmBomGuqWIQxxOQxa9y/H7/M0lC+zGfJumF8hZXSbC1McffgPEGORC6UDwBUaiAxV24VLyamVuPOtj2IGD6o1PL7CcOiHF20YrcpYQoAdu08x3s99ugRheXL95k9VpUqHnhtnKbqa3Z2Hn74YWtxmmw3tm87jYYNa0AqlaJXr2ZYvfqgTY83bvECvgAKxzu//4JNcxdY/TmdKfCAdfc1LjTu3HkO+/Zdwttv98Xsz1+Gi5MCTjKGwa28EfX9UFxQO+Gh3k2FMYbQ5pGIiWoCV0HVWH240D4xKPPzIXdwQNV64quwFsdz2Jphs9akalXt/+j4sevo1q0p3N1dyGMKmmc3Jwp6ebmiQQNN3/DGjVjUr1/d4ryTwkG/r69HqVbnJio3XBVOX18P7Nv/P3h7uyM+/hn69f0fv46h6zktVXsT9vZ2w/37CcVuAwlT5QjGGNKTkuAdFAjA/sJUfk4ustPT4eLhgZpNIiCTay6nWydOierEODpqLz9jwpQYTxknJwW2/rQKrSbpuj+7CvJK6bt+S6RSOLu7wUOhgkNhHywtX4b2AdrvtLZHAWp7pGLEmtf5ZWIHhvqhfOnMfMc+JV8GZ7kSwcF+cHJyQG6uadXaFhjKYyYGU5XW5I6OqNGoIQAgPzcX/3z+DZLjH1lU3c+W6HhMiRSmpDIZH/b39GGs2bABU6QnPkVuZhac3FyLeEyVpCKia6A/qjfQeCTEXruBM5u2IeXRY9RpqQmFierdDZd27ze4rdBbKi09BxeeifdQ4GaBdy38Cd0njSs64NULIzSVGDaDOUKzNuCgkGPdn9OLHM/UbzI3IwsSMIR6a96npGQWKXkN6FXls1N4tCVegZZ0coWz6KXlMfUkRlu84WGmAwKcNWLE8883xbp1R4qsL7xfJj8T1wkXU/FT4eULIBUA4G1hKF/GsxS7CFNc+LOnvy9UDJBJAE8HredWYq4MSY8SrObFlHTtFlZP/RD93n/HbPVDa8AYQ25WFpxcdZOkmhWmdp3DJ58OA6AJ5xMjTL39dl+4uDgCAH75eTeeiby2ygpbt57B+9M11QT79G1pc2EKQJGJBE8/X6PP9pKgVqqQmZwCNx9vuFcxneusoECJ+fM3wq3jQAxqH4gIb02xg9o1q6A2MnA3PQ+Hn7giLV9zH5dIJGBqtWhvqLzsbDg4O5tMqp6ZnILghuHwrRkMB2cn5Ofkmt2vWPFYuF5JBOfQ5pG4eOm+TfpVQo+puLgkXL78AO3aNUBISAA8PUue8Li8YmpCqXHjWjh3/jvR4wZO4JLLtNdh27b14eysuYeVxcIqRMUjNvYpHj9Ohre3pl/28OFTswW5hB5TXl4lux9QKF85Q8czQyqxSgLUksCFF3KiFACc27JT1LbCUL7c3JLlPYk5fb5I8uxcYVU+uWHhwEfgVZWhlJp1lxYbLuaj5wHA51oykYg6MVU7CKpdO9DsMWxBmqBTlJuZKbqDw7nFD/50htYjp5BG0R3h7K4ZFF7ctQ9nNm83msTYHmQ+S+E7mWKTn3sFBUBemMS+JGF8HNygzLtqEOSOmk5Io+iO+Gj3P5i0fDFGfvUZJi1fzCdsNYVEKkXt5pEI7d6ZX3Z64zYAmvAG7jcb3qEdWg3qZzAUU+gtNf+bf5D46KnF4lvSwziDCe0LcvP4QY65xLAhdWtCaiaPoqnfZG5mJqq6KPnf/+7d5w1WyJGUAY8pWyFMfJ5USh5TYa2a8/kPhXmmho7uaXB94f9P7OywGM8C4TPAUvdyuSoPQYV5nZ7lyvhBr61zTPHhz5DyeRI5UQoAHmcprO7FdPXAEaP5/WyBoUm1REERCEOcOXOHT2bftWtTs7lX3Nyc8eZbfQBwwsamYrXVnvz7701eTOvePYoPSbQlhnJgis0paSlcgQExSfg9A/zgUSsMe+PdsWB3Ms5f1uZ1DPXIx+g6KWgfkAWFVM23W6w3lKOL4YS9Qq/BR7c0oaRSqRSBdcSFlVuSY1PsNqbo//47ovoIxUEoTMXHP8PlSw/495U5nM9aaSaEeVRHvtyFX/7Lr++U6cIqRMXEX1DJ+cmTFBNrarBmKB8JU+WIRtEdddyIq9ULs9lDSGx79HPJqNVqMJEZ+S1Nfm6OIsmzJ73Pf/b0+lWDuQaqOGkTyqbnW+/noJ9jSjvYKArX+Tmx919+mb0SoGcIckz9d/mawdxQxtCveNP4+U4IbR6JLq++zK9zZtN2q7ZXn3lfjkZgoLfF23HhfGJzTPnVrMG/fmpFYUoqlcKvZnCxKvkAWjFr4i8/okp9TS4Mxhifj46p1Yi9dgMAIJPL8OKsmUUEL6G3VEpKJn74fovRa9cU6U+T+N/kktfegrKwclp6kma5mMSwXoH+Fh9XSG5mJkIEXi+G8ksBelX57JhjyhYIOxilUZGPu3a5/+mjbAUKCr/S6OhGBq9d/fulGMR4FggLYFgayte+ZQgvit7P0A48bO0xBWieZSvfm4nsvKLX4rplf9lEMBJT/dBa6AtTednZRXJo6qNSqfncNt7ebnxYnzEmTOjBd5DXrDlULj0N1Go1H3rs4eGC555raPE+xOQMUao16Q+MYaqKakngEqArnBzh6Go6YW54h7b86z2bDmPcjE3YGeuGzALNfUYmBVr45WBsWAoaeOWCC93OSk0TfS3r9w+F+VEf3dLmChIbzmcyxyZjfCVBYfuE2xTHE9uSqsmWUFUgTD16lIxLl+7z75s0DrHqsSoj5bWwClExEY6jEkQIU6l6oXwlgYSpcgLX2VcUelNw2OohJLY9cgfdG6lEIsHLX34mqj3WFqYA3c71hYMnee8HT09nuHp5FpkVqyLwmErNl8FacKEpSqUKaWmaH+yV/YeRFBtXZF2u83Nij/2FqbSnup13Lnm0pXj6+2HUgi8wafliVC2sYqNSKvkKTGIQJoKuX19cMu2uXSNx/cZijB/fw6KSpSmFwpTCyRHuVcyHrfnV1LbHGh5TCYIE6IG1Qyyu5AMYL0sNAMM+/wSNojuiUXRHNOzcocjnwvvIBx8M4b2lvvt2M9LTs3H1wBHR14L+LDBTqxFz6ixir2lC6HyDq8PZw0NUYliZrGQeAjkZWajlph2U7RKUndc5lsD7olr9MLt7oloTocdUYqJthSlD1UdVTIK4LM293k3B8NpnRa/d4uSYEuON8OxxAjIyNGGEllbl695VOwi/V8rCFKB5Xty9WjRx7p8/bTSwdvkiL0c3Abm5MD6OXTu1+eF6mKjO5+Agx7vv9QegEXe+/urvYrSybLBt62n+dZ8+LSzenssZ0ixqMi/O5eYVYG2MJ9bGeGFtjBdW3PFGRoH5/o+18x+lG6nMJ0QilSK0eSRaD+7PL7t+5ATSnybhZpoTVtzxxqlEZygLbwOuCk0V15dqpyHQuQBH1/wJQCJKnOJCADNTUrHk1Td1vAaLI0wBmt/x/l9WGTzW6X+2GM3LedBIlWuz52AjD7dqesLUxYv3+PeNG1OeKYKoSAiFKUs9piiUrxJgqLMv/MxWbtbFao9EIro9thCmhKjVav7HUsXIbDknTDEGpNtAmOJCDwDN9+blr/EASX2SUCRkIiZGm9TZXsJUVnIqVEqNF1lYq+ZwdtecR8zpc9i79DfR+zH0v5fKZBg9/wtRoqXQrfnc+e+w9vepoo/t5eWGpcvewKHDc0ULWsnx2u/eW0SeKV9BRb6nD63nMQUAddu1triSj0nvI8FvcsCMdw0mPefuI2NmTcao0dEACr2lCpMFB0eE89fCvXMXcXjVH2CMFensm0qYHHddm9upeoN6pZLk1VVaAD9nzW/84pVYgx5DjaI7okW/Xvz7IZ/OMOqJKsb7IDe3aMUSe1KaHlPGxMb/BNX5GtXyLOJ5oZ+TTwxaL9Sig07hdcjdgy3xmFIo5OjRIwoAkKuU4FG2RiBVq9XIThdfNbCkpKToHis+/lm59PzRR99jSqwwtXu3Vlju0dO4MDVqVBdUraoZSG/adBI3bxadECov7N59HgUFmmdyn74ti7WP2NinuHUrjhcXbsUkIDFXgcRcORJz5aJEKaBk+Y8MkSmozGconE8Yzh7cMBwAoFap4F01kBem85UMJxJdseqON2LStfeZIBclhoWmYXgbT2z//HPRBUo4z2+mVuvcVx7djuFfWyJMAUBuhvZ3fPWANsde/Q5tEdmrm8FweqEH2ZnNO0QeSfNst4WHG3ftJCamoqBAiatX/+MnfhuTx1SpERjoZZPKvQQhRFeYSjW7PoXyVTKKU3K2PLSHE6bUajWUStskHOZm391dDbnIMvg4ajp8aQVSqJjls1PG4AZawtl/3xrVoXDSeLw9vHK9SMiEUJgKtZMwxRjj3euFlXn2/bQCu5f8alHug5KIlmLcmvXJzc3H+vVH+fcdOjTEhYs/4JNPXoKDg2nPm+Q4bQL0KiLyTOmG8j00saY4hPlV/ENqGF9RgFDYEfubNLdO14auOt5SnLdfg47t+fXObN6BLV//gJXvFs0fJQx90IerbAkAwQ3rW32QY4iIEO0D8tjpe0U+57zMHJyddZYb80QVeh8I7fe1h/h1fv5pt03FA0sn0UvTY8qY2CjMMxXill9kPaEwZYmox4W8mboOOWHKks5Shw4N+IS+9zMdwKD50rMtCAuyBsLOHgCcPHnLyJrli+IKU0+epPCeGi1ahOlc2xxSqRTT3h/Ev583d0MJWmp/0tOzceTINQBAaGgQwsODzWxhmPDwYD5k+fzpmxY9yw3lQrIGXI4pAKjTqnmR3JSGPIAlUilGz/8CEZ076AjTaQUybH3ogb/veyApV9t3GT06Gpt+HY28fWvwy/i3cOz3v0S1Tf8elZuRyU9gBdUNtcibyb9WCP96z9JfEX/zNgDNc2bkl7MN5o+s21rjHacqUGLHT2vMTogAQCMfbQijoXMoLhKJBEFBmoFqfLymf5idnYc7dzT9poYRNSA1lwySsAobN33IT9gaMspDRVgDSz2mKJSvklGckrO2xFrt4YQpW3hLcXDCkKeHM9ITdDti7go1X5HvWa4M6UnWGSgrFHK4u7voHB8AgsJC+dePBbNvHMnJGfxApE4dcUm4rU2j6I5w89ENZVMplXBydzPpoSAWa4uoI4Z/w4sD9epOxEtDv0J0lw8RE6PpMDk6KjBr9ghcuPgD2rdvYHQ/XI4pQJOA3Nw5BNXV/C/zc3KQlVLywX5SbBxUhbPibj7icmQJhR1r/PY9FSo08NJUOxJ6SwG6+T1uHD0BwEBONzMJk7ncVgBQvUF986FYJah0yNGsrnYW/uSleJ3PiuuJGhurqVAitBkzViI/X3Mfe3lUZ3h4mM6XUhK+mDuar9IjhtL0mDImNibnyZBRmAumumsBctNSdT6vIsibITaUj8PcdcjdUx0dFXyFNnP06aP1TNEJ40u1fY4uQBvGLJfperLEPnxaIWbFuZx3HGKFKQDYLQjH7dq1aZHPBw1qi7AwzfNz376LOHu2aDhkeWP7tjP86+KE8wFARIS24uvVKw+Mexvq3XdNecGWhEbRHXXyT3Z//VVenBHrAXz14NEiwvTDLAcsPJaHeQv38799d3cXzJs3GptWjoNv7iMIxRtjGLqXPbqtuZacXF1FF0oBoFNt1z+kpkGPK+FkiHdQIPwKJ6j+u3wVMbdjUb/eRLz91jJ+/SvJjlgb44Ujj12gLjydxj55aO6rDZO11uSPn58nn3g/Pl4rJl4qTIDu5OSAatWKisSVASdnhfmVrIiDg+njUR4qwhpwQjRgeSifJ4XyVXyKU3LWllirPWKEKTGhMzk5xkNnhIOcg4uXQtgRE+aXepYrw+GVf5g8DofExMyQRCpFsy5t+PfCUL6gwlxLAPDotuHSm5zXVI0avma9fIwhzM1kiasvN0Mp06v8IwzBM+ahYCnWElFv3ozjxQHOS+Xgwcto3OgtzP1iPR8CER4ejCNHv8TSpW8YLG2cHK/1mPKpblyY0oQWbORncR2cna1SgECtVOFZnEY48fT3Q3rSM5MVHPVnr63x22/pn80nexZ6S3n4+6F6g3oANOJShiAviCUJkxPv/4e8bE2nuXqD+iYTwwJFPe4sxcFBjsgGmtDZbKUE9x7pep9Y0xM1Li4Jq1dpSrl7ebnh9dd7mdmiKElJ6by4ZYpu3SJx8NAXopP8+/qVnjBlXGyU8F5TcilQzVWp82lxkp8LMXUdJidr/+9iw/n69NUM/pUqNf7L0A4EMlPMd9BKijCMeciL7XU+m/xu/woxK67vMWWJ1+muXdo8U90N5JmaMXMw/3reXHHeMWWdrcI8U8UM59MRpq4+NPos1//tmvKCLS5cX8PFU3cAzYkzz48bLfrebEiY/l/3Qfjgre9QN2wCli7ZAVVhhdCwsKpYtmAE+gYlwVth+F5ryjuMq8wHAFXraftzXB6syJ5dDYblccJU8qPH6PPeGybD6ftPn4y6bbT/49snNaKkvhduzOMcJGRLce6ZC/Y/0noodAjMRoRXtlU93HTyS8Vrn/+XBQnQa9UunYnxsoSTkwO+/XacvZtRqSnueIcwTUAJckyV1GPK9rVniRLDdfY9/f0MPqiZWo3UhESru1nbuj1ihCkudMbUDEBSUrrR0BlhvpKEa1ex8r2ZGDDjXXgFBqCKo3ZwtPm3f/AoNhFKtWbgZAylGmjQsxfSJEdx7/wlnU5co+iOGDDjXYTWrAIgFQBQvXkLXtCpWte0xxQAxMQ8QosWYZDJZAgJCcDt2/EG1zMGN6gxFQaXk5OP+vUm6nxnut4jRWcoGVPzM5RX9h/G1YNHEdqsKUYt+AIuHh4mxTpD2FpEzc3Nx4cfrsa6dUfx089volUrjbAyfkIP9O3XEm+/tQx//32CX1+YY8pYKB/XmYbeqXKd6ZJ23hPuPYB/rZpQODkiNyvbcPlqxmBo9lrsb1IikRhcx5S3VIPntN5S1w8fL/b5MbUaj27eRq2oJqhSvSpcPD1wZf9hnNm0HS0H9jG4TY5KYvY3qVYzZGQULT/fvn0DuBb+Dh5kOMDRVfdhaW1P1C+/3IAxY6Mhk8nw7nv98cMPW5GTkydqW0Bzr9ux4xwGDGgNAHjv3V9w+PBV/vP27cMx54tRcHNzRsuWdXHy1Hz06/sZLl9+YHK/fqUoTHFi4+gFc8HUap3r7EG6HBHemu+j6/NNsG/vBf4zLpQvIyObF5OtRYpgcsDb2w1xcabvPfXrV+c9Vq/dTUaeWlsZ0hrekeawpDpTecw31Si6IyJ7ddVZ9triBdg0d4Go++eJEzeRnp4NDw8XdO8eWfh80gz0u3ePQmSk5jl75swdHDhw2fonYAfu3n2MGzdiER4ejLZt68PHx11n0ksMDSO0IeJXr2o81Lhnee2oJvDw80X60yTcv3gFoc2aosPzXXB03wHEnDlvVU8pk30NqRRMrUb7ES+K2hd3b+aEaX2ePUvHpElLsHTpTnz/wwR07BgBAKhTRYJaPmm4mOSIU09dkaeW8vsx5R32qDAED9Dkmbqy/zDf7/MKDOA/S32SgE3zvsWV/Yfh5uPNC3CZz1JQo5Fxz21OcBP+Pm7/qxUlQ0MD+ddbf12PiDFvgKnVuJriBCeZGh0CNc/B6GrZ2Pnrdqv936pV03rRP3qkFaaElflqhRhOXl9RcXRU4J+NH5itDgqYnjy3Bbt2z0ZqahaysnKRmZmr8zeLf5+DrKw8I391t8vOzrOKB7u1Ke54hzCPTlW+hFSz66elZUOtVkMqlZIwVRkw1dm3lZt1abTH0VFz+eXlmR6IxMY+LfZNJVkw+16lijtOCDpi8794CQjSdBJ2/7UHLKA2VtzxhrNMcwOO8M5Fkyq5AIAjj10Qm+WAHJUEkUOHIXLoMJ3OBy9agMFJpvXEUitceNGC85jKy87W8dARclcvAbqlwlRxBzWc94gxhDOUnEdCzJnz+GvWXMPXASsaHgWUvoh65coDtGv7PiZN6oU5X7wMd3cXBAX54K8NM7F580m89eYyxMUlITstHbmZWXByczUYyiemM80Jd8X5HTaK7oiwVs359+5VNA8FtUqlk+tLIpHgwG+rigzgdH6Tet+98DcJwOD/q4VfUW8piVSK2lFN0HrwAH69kghTABB7/SZqFXogVW9QH7f/PQ3vqtrO9vE//0a7odr8MBkFMp3fJMf6WXPx8eTn0bx5GKRSCYYO7YA5c9brrNOrl/b7vJ/pACd33YeltT1RY2Ie46+/juOll56Dv78XXnnleSxatF3UtgDg5uaMbt0iAWgGVIsWbdcRaS5cuIuDB69g67ZPULOmP2rU8MPRY19i+LBvsH37GWO75UP50tKykJ9vXdHHEJwnhv5g7er9NPQKlkEqlaBrt0hMn76C/8xQTj5rIZzJE1OZTxgqdfT0faBpff59aVXkq6gYFff9fEWL+wUFSuzffwkDB7aBv78XoqJCce6cZqJnxswh/HoVxVuKY/u2MwgPD4ZMJkPPns2wVpDXTgycx1RycgYeP9aGYxkSde6dvYB6foG4Z8YLtjiI6Wu4iazeK/befPnyA3TuNBODB7fD19+8gpo1/SGTAM388hDunY8TCa64muKIlIREbP7yO6PXoL7HlLDfJ0Q4WZWVphUkstLECdvBDTX3nJyMTMRe1YbA1w7V9k32rt+Fc5fj+Pvs2SRnOMsYmvvlQCoB5s7ohStHz2D//pL3tYQeU7qhfAJhqlbpC1NcH4UTVfUni22FQiHH+r9m8JVBMzJy8OYbS3D1qmHPT1OT58J1cnLyTfbfc3ML4ORkPnTQz8/TYP69kpCVVShsGROvDIhgmZm5GmEoPBgZmQ2QnpatI4plZeWWKL9wRZ/EsSeBgV4ANM8LMel2GGNIT8+Gl5dbiavykTBVTjDW2U818yAty+0pjRxTwllFbvDDdcQCPUcC0CRfv3EjDlWlHsgokCGjsDkPsxS8MKWQMSTm6v5cuM7HqikfoP/0yeDyITjLteeTq9K4Zw+Y8R68AjUz70/u3DM6+yBMgM7lySgNius9Yuw6AIqKU/YQUQHN/3fhwm3YtOkkFi6aiH79WgEA+vdvjS5dGuODmauwZMlOJD96jKp168C7aiAvNnFYKtxZgrHBGrffXQt/goOzE7q8OgqAJhH5o1sxRTpi3P/i5W8+h0yuvVb1f5P6/y9PhQoNCj1ZUlM13lKGZoG5akjC6nqWEndNWJmvPh5evY7aUU0BAEkP43D/3CUdYQqAzm+S309yAcaO+Q4XLv4AuVyGmR+8iNWrD+LhQ23no2ehMKVmmuTbTq66D0veyyzA32oi6ry5f+Gll54DAEx7fxB++mm3aA+gQYPa8jmQ1v95zOB2V6/+h9atpmDjpg/RunV9uLu7YPOWj/Dtt5vx+9rDkMmkqB3qi8jI2lCpNNeFv78XANsnPhdiyBPj3vlLiD75DVq0CEPTprUREOCFhIRUSCQS/t5si1llS0P5hKFSew9cQ6emPfn3JEwVH2uK+7t3ncfAgZqQ+R49onDuXAzatKnPe8TcuBGLTZtO2uQ87MXWracxddoLADTXqCXClJeXK6pX1zy7OW8pe2GNMP7iTnBt2HAc27efxbRpA/H+9MFwcXGEi5zh+WqZCMq4jUmf/4grR64a3T45/hFys7Lg5OqKkKaNEdK0MQzmwRJcz/t+WcUvT7z3AOHt28AcTm4aAf3umXNQq7QDd85jKjc3H48eJSM+Xnuf9a8VAtWH78FJrkaEdx4cHRXYuOlDRHf5EGfOlCzPGlfhEtAmPwc0IezPnqWjShUPhNTyMbSpzTDnqWYr5HIZfv9jGvoWPicyM3PQq+csHD9+vUT7FRMdEhjohe07Zpnd1+PHKXBwkMHNzVmn+nlJcHV1gqurUwn2YDi9QV5egVHvLY2Hl3HProBC8YSwPpzHlJgwPo6UlCx4ebmRx1RlwlhnvzQH+dZsT2kmPwd0Kz8BQIMGmuo29+8nICcnr0g41DNBZRdhPioOrvPxwkfT4F5F+1B2Enh35KikkEilvCgFAI/uGA7jA3SFqTo2rMwXqHdDL4n3iKHrwMXbCwPef6dMiKgccXFJGND/cwwa1BY//DgBQUE+cHd3wY8LJ2LEyE7Yce8ZgDqQKxTw8KuCtAStyGGrAgSmBmsAAMbQalA/zOkxCJE9u8G7aiAC69TGyK8+A1C0I3brxClelMpOSsaqGZ8WCcUQ/r+ee/klTB4ZCZnAW6pG8+YGZ4G5akglCVkUilrVG9RD/Xat+Zxm1w8ft+g6vHvtIRb+uA2T3+0PFxdHLPj2NQweNBcAUKtWAF+96nG2HHkqKZzcdYUpplbjwG9r8MIHU4rsv7gi6uXLD7Bt2xn06dMCNWr4Yfjwjli5cr+obUeM7MS/Xr36gNH1EhJS0aXzh/ht+Tt46aXnIJVKMWXKQEyZMlCw1qAi29k6jE8fQ54Ye/dcQIsWmsS/zz/fFGvXHoKnpwtkhV6BtvCYEk5OmBOmfHzc0a6dpiz9zZtxuHblHjoJPs8kYarYWFPc188zNWfOekyfoc0t9dWXf5fJ0JOScOLEDSQnZ8DHxx09ekRBLpeJ9jZo2FCbX+qaEa+O0qKkYfwlneDKycnDZ5+tw/Ll+/HlV2P4iYSG9avi8OG5+OOPw5j+/gqDIb8RXZ6DXKHpt5orUsJdz0JP6OuHjqFJty7GQ+4ZQ352DhxdNcUzbp88q92fRILatTXC1L17T/jrm7vP3j17AfXatYI8+jk4yRjqeOTDzc0Z23fMwnMdpuPmzThLviYdhKF8QmEK0HhNdenSBD4+rvDz88STJ8n6m1sdMZ5qtuhnSqVSrFz1LgYN0qQ3yMnJQ7++/yuxKMVhLjqEC1M2R5/es3HhgiaPrVwuKxSVHOHm5qz3Wv+vE9zcNAKUa+FfNyN/SyZSaXF0VMDRUYEqlSsStEyj+T9rKlVbJkxlolatABKmKhvGYuntRUnaU/rClHYmokYNP/6Hd/16LICiIYqp+TI+v40hYQrQdD6EohQAOMu0naVcVVFPjMdGEp8D4MvvAkBoMYQpsVWntu+YhdjYpzh+/AZOHL+Bf0/eQnpCAtz9ipc3zNB1cHX/4WKLlmLcmosbt//33yewf/8lzJs3BuMn9AAAtG5dH81bqHE+OQsnE13gU62qjjBlqwIEYgdrz48bDa+gouvpd8SEZalT7z80GorBew36uqLBW5pw1uw8FX74cTveWr8axqohCXONFWdQkPjgIfKys+Ho4oLghuFQ5msLG1w/fMzi/HWzZv2Ol4Y9h8BAb7zwQlt06xaJPXsuoGdPbVLk+4VV1bhZaCHGhMSSiKhzv1jPh4NNnzEYq1cfhNrMd1WtWhV06dIYgCbP3MmTt0yun5ubjxHDv0FGeg7Gje8uql2l6TFljD17LuCDDzX5Y7p2i8TatYdKnPjcHEKPKW9v0y7mPXpE8SLZ9m1nkPlMd5Dl6u1VxJuSEIc1xX3GgHv3HqN27SC0aVMPY8c+z3vCPn6cgqNHr5WorWURlUqNnTvPYcSITvD0dEWHDg1x8KC4HFoRBvJL2Qtz93hzWGuCKzb2KYYP+xpLFu/A9z+MR9OmtQEAw4Z1RP/+rfHlvA34+ut/kJureUaZ8mw2hV+N6vzrhHsPjKbBADTPWE6UAoDnx41GeuJTXNl/GEFB3nBy0jzL7t59YvBYJ//ejEbRHbEj1h09qzxCWKAjfH09sHvP/9C+3fvFDmeqWs2wxxQAXL70AF26aMLzGzcJsbkwZbJioxXSKhg9rkSCX397G8OGaQrd5OUVYED/OTh06IrVjmELlEoV0tKy+GI21kIikcDFxdGAcKUreLl7uKBpk0Z4/CQWLs6ORQQvQyKZTK8aLVG6BOokPk8VvV1qqqavpVDI4eLiiOxs8TlWhZAwRdgFiUTCl5+1rTClFS6E+UUaNtR21K5f084g6oemJefJ4O+sgrejCjIJg4qZ75XoeEwpi3a8jCU+BzReDVxiV0s8pry8XPHWW33x3pQBorcJDvbDSy/58TOG2Tn5SFKm41G2HI9yHPA4W458tbTYM5QlES05t+a2bevjj3XvAwC2bD6J2bPX8euIids3RmpqFiZOXIQ1aw5i2U9vIjw8GHKZFC39chDmkYcbPZpjmUCE03SmE+EZ4GfV3FliB2sdRg7VjMb0jq3fEQusU5v/LDvRvEg2ZrDWW+r8M2cENoywWcgioPme4m/cRu1mTeFTLQguXhpRIicjE/fOXbQ4f116ejben7Ycq1a/BwD44ccJaNzoTT6MD9DklwIAZz1hSiKVonk/TZiWqkCJVVM/hMLRscSeqP/+exOHDl1Bp06NUL9+dQwc2Fon0b4hhg/vCGnhua5dc0jUcRhjWLp0p2hhKqmUPaYM8e+/N5GRkQ13dxc+n5bQkzX5mfVD+XRzTJn2mBKG8W3dehrBEeE6n3cdPwYt+vW0ebhIRcRa4r5+wluZTIZff3uH/zwoyBtXri6qkAlvt287gxEjOgHQ5EITL0wJK/LZV5gydY83x6Z53+LYHxusKjgcPXoNzZu9i9de64bP57wMX18PuLg4YvZnIzD2lecxbepv+GfjSdOezSZwK5y4zEnPQMazZJPpD/Rxr+LDTzx55Wt/F/fuPja4/q3jp5CZkgI3b2/sSQ6Ch2caApxVCA72xaFjX6NVs7eLNZnH5ZjKzc0vknRfmGeqceMQ7Nl93uL9i0UilaL9sME27aMY3KdEgmXL3sDo0dEAgPz8Agx64Qvs3Vu6TgK2nLC1FMYYn3fKFJqcePnYuXMnXx3THE5ODia9tbi/YWFVMemN3tY4HUKATuJzC0P5OLy93UiYIsoXwrhnWwlTwcF+OgkA69arzrvCdu0ayS+/fl3XtV0Y6tR08Xj4t6gOqQTwclDhWZ7hn4xKqeRDqJzkuh5TTK2GSqmE3EHzMHl8x7jHFKAJ54uKCkVISIBZd31fXw+8+25/vPFmH3h4uBhdT59Tp26hYcMavNcYALg4O6AGlKjhrgSQC8aApDwZ7j8twKY1O5F5r/i5hYpDbOxT5OdrSzKfPn2Hd0+2FseOXUdk07cxc+YQfPDhUCgUMng7qrFkzgC0DHXFtGnLkZycoal8c+Aw2g8fUmQfJQktEDtYc/UynshS2BELCA3hl2clmh6U1aoVgFGjugDQXKeXU91Qr10rUe0pSZ6Q2Os3UbtZUwDg8z7dOn4SKqUmp5Kl+evWrDmIN9/qg5Yt66Ju3Wr48ceJiI7WzOAmJqYCaje4KyRFQvnCWjWHV4AmxPbGsRO4euBIsc9Jn7lf/IVOnRoBAGZ+8KJZYWrky50F53PIau0QUtqhfIYoKFDi4MEr6NevFQIDvdGoUYhOTg3b5JgSF8onl8vQo0cUAI2Yle7oi5Gff1IkV56tw0UqKtaq5luZE97u2nUOSqUKcrkMffq2xJQpv4rarqFAmLp27aGJNUsHS8QZIZnJKTbxVlSr1fjpp11Yv/4oPv10GN54sw/kck1l5L82zMTJs/dxx7MKkvLEi1JMrUZa4lN4+GvK1Sfe1wqCwj6mp78f+k+frPHGNDHxFL9uMb/cmMdUROcOcPXyAgDkq6XY+MATL9ZOg4+jCrVqVMHBY/PRtsXbyMjIEX0egFaYevSoqDeUUJhq2qSWRfu1BEM5pUxhjVxmHD/+OAGvjdNMACmVKrw09Cvs2HHWzFbWp6RVyssLubn5yM3N13EsMERkZCgJUzZAmOrFklC+1BShd7pbEe9KsZAwRdgFWwtThsqIDhrUlo8NF6JUqREZGapzQ+e8ffZtOojOLV4GAPg6GRemOFGKMaZTQSxHKQEggUqlghxAyuMnyEk3Ha7CCVNyuQw1a/rjroHZsaAgH0ydOhATJvbUCd/jOq3mmPT6Ely+fB+NG9dCu3bhaNO2Ptq1a4AaNfz4dSQSwM9JBb9gKVrO7I0vZvbG48fJfPjf8ePXcfHifbPJnYOD/Yr9IOVyKgCavAq2ID9fidmz/8DB0w+x9q+PUM1Vcz5jX+mK3n1aYN4Pe3H4whNEdHnO4PYlCS0QM1jLSksXVaXIw88XgaFaj6msBNOdkw8+eJH3WryQ5Iw8tRSBoeI6liXJE2LI4yysdQs0iu7If4eW5K8LDvZDE0GHWOhB5O/vhZf9M6FUA/PjNKIEV8mn2+uv8uud2bSj2OdjiL17L+Ds2Tto3jwMUVGh6N49CruNzCQ3bhyCRo1CAGjyyBj6vVsDBwc5L8zbs/O6d88FPuyqW7dIjXhYiK1D+bxM5D5o374BvLw0n+/adQ59358MW1XhNEVZmhW3JmWtunB5JDU1C0ePXkPnzo0RFlYV9epVx61b5nMHcR5Tjx49K+LxYi+E9/iwVs3RdeIrZrcpaX4qc6SmZuHdd3/BTz/txrffjeO9Ols3r4WWLBVXkp1wItGlsKiNcTT5nyQ4vPpP9J/2NgAg8YGupxrXxwxtHmkyXxU38RTYtgm/zFBfSBvipiVHJcU/DzwwtHYa3BVqNKwXiI2bPkLvXrNE97udnBx4Qd/QQPP69YcoKFBCoZCjcWPbCFPGckqZwlrXyvz5r/Lih0qlwojh39i1sEJJqpQThBh0Q/ksEKZStR5TJanMR8IUYRdsLUyJmVXlWLt2KgBNZ1/f/f/qlQf8ax+HAgDmczg5CXJMZeYq8fcX32HIp9MBmPaW4gSczIxsfln3HlH494TGUyklJRP+/m5YuHACxox9vsh3uGL5PqxefRB7930ualCjUqlx4cJdXLhwFwsXbgMAVK/ui3btwgvFqnA0bVpLJ947KMgHgwe3w+DB7QAA2dl5OHPmDk4cv47jx2/g339v6oTOGBIIDbXHWNgFV4UGsJ0wxXH66EWsv++JRt65aO+fCSeFBP7+Xljw+RDcz1DgwCM3pBcAj27HYNPcBVYpQCBmsHZs7Xr0eHO82X2lP01CQKGwlJuZhbw04wOQkJAAjBqt8ZZKTc3CiQcOkLoCNRtHIDstHc4e7lYNWeRoFN0RHUa8WGS5i4d7ES8UsaGgvr4eZivPyKWAr58Huk4Yi9aD++tWG1SrIRMh5lrK3C/+wt//fAAAmPnBEKPC1Msvd+Ffr1l90Ort4Jgy9QVMmaqp6mXqN2dr9uzR/k+7dY/Ezh3aRNa2EKZ0Q/mMC1N9BWF8p68mosaw0g0X4ajIs+JlrbpweWT7tjPo3FmTj65PnxZmhamAAC/+WjJWzt5ecPf4e+cvocWA3iX2prMWN27Eokf3T9CvXyvMX/AqQkODIJUATarkop5nHk4kuuByshOYkYRTBXl5+H3GLMgU2ueS0GNKiFjPntqCvpAhjylj+SozCmT454EHXqyVBmc5Q5cujfH7H9Pw4pB5fOVWU1StajzxOaCZ2Lt1Mx4RjWqiXv1qcHRyQPWIhjr9I659xekzmcopZQhrXitffDEK7743AICmnzBm9Hf4669jJd5vceAm1MpC4auyQkWdxLE3xc0xlaLnMVVcSJgi7IKuqCKunLqtMeT+L3R795TkADDzY2MMDtCcT75KAqnCEU5uWuXYWOJzYwLOwoUT+ddcSJ/QIyo7Ow8//7QL33yzke80lGRQExeXhD//PIo//zwKQFOdoVWrumjXrgHatgtHmzb1dUIGXVwc0bFjBF+mG9DMoGk8qm4gJSWzRGEXtUrBY4ojrFVzqNUMV1KccS/DAZ2DshDmqUl8Wsu9AKPCUnAiwQUIqw0XTw9c2LnXKsc1N1i7evAoWg/ub7bTHnfjFqpUrwpAk2TVFB98MIT3lvr+u814GtIBoc0jdRKvGjpOSbwa+A6miFxZtuhwObm4oMeb44tU65JIJHj568+hVlk3NGvTppO4cSMW4eHBeO65CLRr16BI9R6pVIphwzWeePn5BVi/vnQ6vfYMdbpz5xEePEhASEgAOnRoiKtXtAM2WwhTmZk5/Iy+qVA+Lr+UUqnChTvJqGF0TS3WDBcRUpFnxctadeHyxtatp/HNfI23Z5++LTF//kaT6wvzS12zc34pY5RVb7otW05h165zePe9gZj1v5fhKJfASc7QpWoWGvvk4tBjV8RmOYCp1chMSYXcwQHO7m5Qq1S4fvg4oseN5vdl7Jks1rMnyE/Tj1Sr1bh/v2hfyNS9KDlPjo3/eWBwSBocZMDAgW2w7Kc3MXfJUZO/QYlUipbRrfn3jx+nILR5ZJFtLl26j4hGNaFQyLHg0Hrkumr7MVmFlUxdBV7f+tWETWGuQEwRJNa5Vj6dNRwzZmpTN4wfvxBr1x4q0T6Li6EwRku+w4qK/iTOL7++jchITdRA504zkZ6eU24ncexJUFDxPKZImCLKNY6O2kvPlsnPS8r9+wnIzs6Di4sj5GmPsWnemiLu0kIkUilcHSUAGHIKK/I9L+icGEt8LsbDSyhIZWRkY/GiHfj22806oTCAdQc1WVm5OHDgMg4c0CRZlUqlaNiwBtq1C0fbduFo164BatXS7TQ0aFADDRrU4GPySwIXypeRkW3TWQ/9ijtZShm2xXqgdmoeulTNgrtCDYUU6BiUjfqeeXD6fDLetKKAYm6wZrTTXhgysPnL7+BXUzuUTrh7X/8QPCEhARg9RpPEMzU1E3svPMMLg5oWWU8/t05JvRqsWS6+JBTJ42GFaoOGYIxh3ty/sHKVJjH7jJmD0bfPZzrrREc3QdWqmvwdO3acKzNhNrZm754LGDe+B5ycHNB/gHbgY6vfeHJyBgICvI16TNWtWw1hYRpR99ix64i/Hy9qv7YOLaqolLXqwuWJ3NwC/PdfImrW9Ee7duHo2DEC6enanEH6A7GylPjcFGXVmy4/X4kv5/2FM/ey8MPSd9DAWzNZ5eukwuBa6biTpsDhR65Y+b+vENGlI5r36wknV1eENG2EgNoh/H6MeUyJzb1WLVIz+I6Le4b8/KKTuebuRQk5Cmx96IG+1ZPhoJDjlVe6onHffjiaoBG89IUOTgxpVc8LgOa5FPniS5jUVRsCz21z6fJ9jEAnAECtqh64IUhn6GIgP6YlefosFf8L8vKgLChAZM+uxRa9v/1lGt55VZu6Yf8jV1R76Q00epBf6tehsTBGynWogRvvSKRSXL6VwAtTDo4KXLhw1c6tK58EFDOUj4Qpolwj9JjKL8PCFGMM168/RPPmYQgNDURBprmBE+Or8mnySwHOHtpZ+scxJUvenZ2dj6+//hs/fL9F5yZQWqjValy58gBXrjzA0qU7AWjC+9oW5qhq2y4ckZG1eW+ckiCVShESoklOfe9eQon3ZwwdV3G9fDL3MhwRd0eBtgHZaOqTC4kECHBRYUIrORQrpmHyxO+KXXlCH1ODNWOddqZWY9XUj3Bl/2E079eLX5547wGMRXjreEt9vxXRb03S9Hn0ohIkEgmf42r11I9w9+yFEok21iwXb21sJYr98ccRzP5sBEJCAtC7dws0aVJLJ1nsiJGd+NfFCeMT48peFtlTKEwBunnkzCU7LS7JyZkICPA22lkShvFt23raaom6CcKa6HtWy+UyHDw0V2cd/TDd8iJMAWXbm+7A+h0Y9iwLb37xLvpGOCLQRSMOhXkWoIZzEhI6BWPH2bN8pdf67VvDv5bmu1cVKPEszrDYLcZbbP/CxfhssyZXlbEchGLuWZdiknFizU5881FfSCRAc78c5KgkOJvkwgsdR9asQ056BrpPGgeAwU2urbqWpdINeee2ufPHT/wyf2eVjjBlKCWAJR7SYsX/++cvoVZUEzg4OeG1RfP55alPEnByw2YkPYwTdT19tfg9HVHq0GNXXE52hqe/Y6kLQabCGEvDy7ykSKRS1G4eCf8mDVD76RPEnDlvk3ZyImpAfU8AmnHR1IWz8OT1BZVatCsuXCifUqmyaLLQWjmmLKt7ShBWojSq8lkLLpxPKpXCz9VwTgEORymDtHCV1LTsIp+PW/wtGkV3LHZbPpu9E5//70+7iFLGePw4GX//fQLvvfcLWreaAi/Pl9Cp40x8+MEqHD16rdj7rV69Ci+g2CohNKD15DGWvyBfLcWhx25Yd88TSbmajplUArz2cntcubqIT5Bqa67sP4zPu7+AxWMnIfaaJu+YVCbD0/9iAUCnIt8TIx5T+t5Smw/dLzx3w9e1RCqFm7cXmFpd4g6FtcrF2xJri2JKpQpff/UP/14YGuDi4ogXXmgDQDPTtH37GYv3z7myN4uajGZRk9GyxXv45pv9JW+4jTlw4LLB0tG2COUDtDN5np6uBotD9BEIU1u3nuYHi4CkyHVPiboJeyG2ImFERA1ERoYiMjIULVqG8Z8pFJoCCMHBfib2YF+4CZoLO/eWeDLE2lzZfxgT27yA5zp9iFkL9uJp4f3K0UGODz8aivULh6KuRw4AhvDn2vFezEmxcVCbqK7MTTyl6VXSTU1IxMr3ZiI39h6/7J6Rinwm71mFntVbvvoebm16YV+8NmS/Q2A2IrxzIZFKIZFK0HHUME1OS0nh81+h3VeWUvfeqekzMYT2Hcwv83Uyfp7623KTQabgBDf9EHzheac8foILu/YZXMczwB893hyPkV99hknLF+Oj3f+g8fOdENo8EpE9uyK0eSTf93vjzT6Y+rq2Qu7RJy648MxZ51z7T59stK8okUoN7re4mOubiv0O7UGj6I74aPc/mPjLj2j40kBM/OVHfLT7nxKNf4wdZ/SCufD090NCjnZCvKafRki09vEqA5wwlZCQavR3ZwjymCLKNeVJmLouyDPlJc8zOSvlKBU8lJ2L5jPx9Pct0axLbhnJx2WKnJw8HDlyFUeOXMWuXedx7vx3xdpPaGgQ//q+DT2mxIoRT3IUWHvXC818c9DaLxtyKVCrVgB27f4Ma9YcxJT3fsXTp2nmd1QCuE77uW27ENywPgCgTosoPLlzV6ciX8Ld+wiN8kFwsK/OA+Kjj7SV+P788yiCagRCDNYQbMqDF4otRLHly/fhk09fQkCAN4YMaYdPPq6KO3ceYeDANnBz03R6N/x1rNj3QWHorkwmQ9t2qdZqus1IScnEmTN30Lp1fX5ZXl4BsrJyTWxVfHQq83m56swCenu7oV27cADA7dvxuHPnEYCyG1pU2aGEt+b5Z+OHBgtCHDv+FQD7Fj8o7zC1GjFnzuOzM+fx7ayf8dFHQ/HO5H5wcFCgWlUfVEMWIrPycNCpJhJzNc9aY2F8Qkx5i3HFZgDTuTaN3bMkEgkOr16HrNQ0eAUG4Goq4CwH2gdqJk+jq2YiVyVBTLqjzjYA4CoQpjILij63JVIpZB5VkFXwDK4KBn8nJQy6YBvBXN+CqdU4uOJ3DDSQQoObJNjy1ffoP32ywe31PbY8/f0wasEXumkKniRAdmU/Pnm3G7/sRIILzibp5tw05VltizxQYvtdYa2a29Sz0NLE66UVfqjvUZaSJ0G+CnCQaSIbOCGxrHqUlUUkEgkCArwAWBbGB+gKU6YqIJuDhCnCLpQnYUro/t4gPBi/m3C7dpZpb8S5ypK5MFd2apdS4nNLxAg1k+B0ghPOxqSjcd51dOmiqY40cmRn9OzZDFOn/IaVK23vsRJzWlvJLKxVMxz7/S+dinxpCYnw9XPDteuLjQ7gJkzoidFjCrDmgQoZBaar0llDsCmrCW6549tKFMvNzce3CzZj3pdjIJVKMX36ILz22o86YXyrbViNrywSHOyHK5f/0xGm0tKyEBkZCsD6VeeEubu8vd10RIsePZrxXlTbtp7W2a4shxZVVipy1UJrYa5KqT2LH1QkMjJyMH36Cvzyyx58M/9VPiS4qqsSw0NTcTXFEccTXJEgQpgCjIfzCyfpzHmPC+9ZoS2j0P311wAAdVs316k+eybJGc5yNZr55kIqAXpWz8Cm/ySIzdLtL7jJBcKU0rgH0NNcOVwVBXCSM7gp1Mg006fgENO38PI37OHHTRJkp6WLSpDurlDBWcaK5M9sFeGKts9rRalTic449dR4IRh9wchWQozYflfXia+gxYDeNkmGbqngVprhh/p5SxkkeJorRzVXJTwd1HBWAJJSyFtakahSxZ3vD1kqTFkrlI+EKcIu2FqYsmbuFWFlvgYNa+DKzJVGZ9JjDmzG8LmDAAC5avPut3SzNE7t2trv1pbClDlPHiGcgLL8U43HxJgx0fhm/qvw8XFHlSoeWL5iMka+3BkTJyyyafjhkzt3kZWSCldvL9QurKbHV+QrDOPz8HAye/07OSqgTE0Ccy4dLyZbeKGI+a0r1eCLEehTGqLYkiU7MPODIfD0dMXLo7rg0KEr6Nq1KQBNCe7s7DwEB/tVioGisQqk/v5evHeltT06UgUzefqV+fr0bcG/3qonTAGUqLssUpGrFhLljzt3HqF/v/+he/coLFs+BTWCPCCRAI188lDXMx+h7/TBk5s3cWF38SYgQkO1k3R3jYTyCeHuWXfPXkDL/n3gXTUQQXXrIKhuHcFaEhx54gonGUND7zzIpUC/GhnY8MADCTna/jkXypejlEDFjHtBPclgCCm8tfo5qcwKU2L7FlKZDM36avIRKgsKsOq9D+Hg7KQzSRDZs6vJfQAaUWpMWArkZqLrLj9zxIlE46IUoCsY2UKI4TyUPP39kJ+TAwdnZ7PbcCLY7sU/i86nZY7iCG6lWeTGkEdZYo5GmAIAfyclHmY52CVvaXklUJD4PKEEHlMUykeUO2wtTOnPqgYGehl1b+cw5v7/8OFTZGRkw93dBQ0banIGGJtJHz5MmzQxx8TsEmCfJM+lTUnCLmqVkseUSU8eM5XpVqzYj+3bz+Lb78Zh+HBNLHt0dBNcvvIjPpu9DvPnb4TSRG6JYreZMcScOY8m3brAxcNDp2NmLL+UMQ4uX4tWk94tNS8ma3uhGPOgqNMyCn2nvAWAIVct473CrF1tUAxeXm5wdXECoMnzsmr1FP6zatWq4Oy576wmxqSn55bpUCexeXKs6dEh9JgSVuaTy2Xo0aMZAE3OtePHb1jleARBVD4eKd3xT1IImiIXbfxz4ChjcJQxdK3F0GzTVHw0uyaWzlth8X5r6whT4ie8GkV3hFeQKU8iCfbGu8FJxhDqkQ8HGcPAmun4854nUvLlABhcCz2mjHlLMcaQ+iQRl67dQeuhGg9YPycl7mc48J8DuiF1wmrC5p77ddu25PvK1w8fx7VDR4usI8azyFnGzIpSAHDqv3wwBWAgZztfDEaTj0sjOllbiDHkoQQU7bcYOg5jTJMfrJCShBIWV3ArzSI3hv7vCblaWSPAWSNMUfVc8QTqVORLtWjbggIlsrJy4erqRMIUUf4ojVA+/VnVumETiu3+f+3aQ7RuXR+1awfC1dUJWVm5BmfSq1TRzsbnGvHQ4BDeLMUKOOnptsnBYisMiQYKhQw7ds6Cj487CgqUeK7DdIPfOxfKp1ar8d9/iTZtp1FPnicJOPX3FpMzUE+fpmHkiG+wZvVBLF7yOkJCAuDs7Ii580Zj2PDnMH7cQpw+fdvqbY45fQ5NunUBALQbpk0+mmChMBVz+jxuxpRuLh1re6EY8qC4cOEubsYkFut/am18fT0gV5ieQbaWGJP0NBMNG0wy2TGobKFOwhxTQo+pdu3C+e9p585zNhGRCYKo+HADeTUDLjxzwc1UJ7QLyEKEdx4kEsDHSY3Fcwehd/saeO/dX/hcdmLg+kLJyRk64TJi2mMs3RMndDBIsD3WHQND0hDsqoSznGFQLY04pVRLeDEns0BqVBw5u3kHPJ7XFpDwc9LmQs1KTYUEErh6e/HL8nNy8ccHs032LTivoa4TXhEcZ7vBdS3xejfH/QtX4Neqk9E2uXl74fVfF/Kij9xBXFSGGCHGmIeSWAzl0zIXSmgsf5RYwa3b668i5tRZ3L94BbWaNkJA7RBRbXXz8eYFruJy7/wlpCU+hYefL3/uwgTo/k4FSHn8xOopGizNuVWe0BWmzHtM6X8XqalZcHV1olA+ovxhjxxTxXX/Dw7203FpfPXVrnj6VOttkJqaySvLDRtqSzPnGDktQy7MYvJmpKRkIiKihdHPyyqGvvelS3bigw81ibh7926Oc+diimzHdcbi4p4hP9/2Sd9L6smza9c5NIp4E7NnD8c7k/tBJpOhceNaOPHv11i0cDs+/HA1MjNzrNZeYZ6pqgIXfc5jytPDSfS+KmounYp6XuaIjU3Cgwe2KxhQ3tDPMcXRV1CNb/s2y6siEgRBAEVDmHJUUux75I7Lyc7oFJTJhxf17t0CXbs2xQ/fb8X//rcOGRmm+wQKhRzBwRpRQ0wYn7H26CMUMVRMgi3/eWBwrTQEOKvgrlBjSK00nEnU9iGUasDPMR9SqQw5KgnvgSyRSNB14lhIwKBUP4NcCrjmp2LN+9/wz1tAk6B77PdfwsHZCWqlEtePnDDaNkNeQ2qVCjIHwxEPprzeLcXRxXQYH4cwdE4M3ES0MVHDlIcSAIAx5GRmwdldvCeKuVBCU/mjxApu3Sa+gm4TX4FapYJUJi6vGAAMmPEuOo0ZXqK8WEytxsMr13Uq76XkyXQSoFvb498WSe6tTUmEs8BAL/61OWHK0Hch99SM9chjiih36ApTZbfSnKF8KN99P97EFlqc5JoHuUQi5R/kpsKjzAlnMpkMERHFOo0yx7JluzB9xiDIZDKMn9ADX3zxl463goeHCy/S2TJXkz4l9eTJysrF1Km/4fffD+Onn99CVFQopFIp3nq7LwYMbI03Ji3BNisNgBPv/4f0p0lFZuISYu5h+PCOmD7DfN4FIRU1l05FPS9CPCk6Oaa0HabefTRCv1Kpws6d54psRxBlCTGe1bm5BXByMp38nLA+xjxiEnPlWH/fE/U889AhMBvuCjUcHBSYOu0FjHy5Mz6YuRIrVx4wWpY9JMQfssIBvyV9IbGhUjeOnED9Dm2QpwQ2PvDES7VT4OXI4OmgxvPVs/n1wjwLEOapmW3Ny1fi7e/OoF7PPrzAxSDBszw5ApyVqOrrAoWU6Tx3b/97Gpf3HkTzfj3h7OGOum1a4saR40XaY8xrSCKVYtTXc7BSZdj7x5jXO+fl5SQTNzCv0agBUgHkZWdjzbRP8NKcj+Hi6VHEG4kTfVoN6qfx1grwN+hNJpyINiZqbP7yO3gG+Jv1ULJElBJuZyiU0Fz+KLGCm/A4+pgLPyxucnhOePGvFYL67dvoHEs/AXr8hfNFtivuJGVpVRssCSUVzoKCfPjXpoQpY99FgUQBQAlXVycoFHIUFFg+vidhirALjo7aS68sV+UTkw/FGL1qaAdDSjWw4o43Hj58SqXGoRHhNm8+hRdeaIuqVatg4MA2+OuvY/znwop8922YX8pWnD9/F61avofJk/tj9mcj4OLiiOBgP2zZ+gnWrz+Gye/8ZHHFC0PEnDmPqF7aajKKgiwsXzYO/fu3LvG+CaKiYCiULyysKurVqw4AOH78ho54RRBlETGe1YGBXti+Y1bpNYoAYC7PkQS30pxwN90R0pN/49URbeHk5IDAQG/8tnwyXp/UG++8vQwnT94qsqWwIp8lfSGxeXUOLl+DU/9swYAZ70ISGIBDj90wICTD5DaODnJEdmyJbL3lT3NlCHBWQiIBXvnkDZzZeUBn4H9x934079cTANC0e3QRYcpkXiOJBIyZTiSu7yHtW6M6er3cH53DXdHAS1waDAcnJyAXOLd1F/Kys+Hq5Wl0XU702bP0N3SdMNbIShKc+nsL+k55E8+9/BLAiooaoxZ8YVLAEZKVmgYXD3eLvcLCWjUX5Z2lK7glwjPAT1TbxLbf0LEGffw+5I6OSE98alYsMpaD6/rhYzi88g88P2EsEgPDeQ/FFycOwd49F+Di7YUB779TRLA5uWGzqLQOpVFtsCwIZwFmQvkkUilCmzXFkFkzAQkgkeh+F/mCFDbePu5ITLB8nEPCFGEX7BHKZ0/kUmDr/77Atj92VfgwIrEsXrQdL7zQFgAw6Y3eRoWpe/fKZ0iSSqXG/Pkb8fffJ7Bk6SR07x4FAHjxxfbo1q0p3p+2HL/+utfoTKkYctK5DiRDXc98dAnKhXMkiVIEIUQYyudV6GKuG8ZXtBofQZRFzHlWBwf7leniBxUVc3mOmFqNpwmJmDP+W8z/fA2+/uYVDBqk6f+0aBGGE/9+g9WrD2LG9BWQy2W8+Nixo9ZNvqBAhcjIUFE5AsW0h/PkYWo1L+i0bB2OAT8OM3u+7lWqICdXV4x4miMHvPMAALWreRbx0rn972nkpGfA2cMdDTt3gNzBAcr8fP5zayQS5zykmzatjbGjmmJIa0Amszw3a+KD/0R7nYW2iDIqzEgkEp2E5PpZ1bmk5WI5uuZPdJ80zuKQxa4TX0GLAb2xad63yE5LF/U9/3f5GrwC/UUfo8h+xAhaUincq/hg5JezAZgWi4wJL4wxNOjYHmc2bcffn3+Nvt1WIrLws+FvjUDYsHEGv2PPAH/RyeJtXW3QlDddVmqaWbHKWsKZbihfqtk26pOr0h67cftm2Pf3PqPrGoOEKcIuVDZhCgDirt8iUUrAgQOXceNGLMLDg9GxYwQiImri6tX/AAC1a2tvfLasyFcaPHiQgJ49PsXw4R3x7Xfj4OfnCS8vN/z081sY+XJnTBi/CLduxVm830bRHdF26AtwlqkRXTUTYZ7aDl62UoL1m29iaK9aNEAhKj26oXwajykujA8Atm6l/FJExUCMV1VlK35QGpis7quXwuHBgwQMGTwXnTs3xnffj0OjRiEAgJdf7owXXmgDBwc5FIqiw7NPPh2GTz4dJqqCqyXt4ZbdPXsBHqp0AOaFKUM8FVRE83NSFRF2VAUFuHrwCFr07w1ndzf0fGs8rh8+zg+2rVHR7bnnIjB9xmD07NlMZ3lmZg7c3JxF7Z8xhv7TJosOZwtt1pR/fez3DXhw8TIie3dDw47tRW0vRsDhhMR9P6/Ek5h7RkMWTcF5zhxZs05Uu2o2bihqPWtiTCy6evCoaW+6QuHl1IrlUDLt91DDNR//ZTrw349+fjSdYwvCGDlhjEvq3rhrZ1HtF3qmmYPzkGrYuYNobzpj4pm1hDMu+XlWVi6ysvMQ2jyS9z7sPmkczCXmzxN4TAUFB5lY0zgkTBF2oTIKU0RRFi/ajh8XTgQAvPFGb7z++mIA+h5T5VuY4vj998PYtes8vpn/KsaMiQag6URdvPQD5n6xHvPmbRCd5J2bHQlzz0N09Sy4yLUPi9tpDtgf54LUgAhERIyFl6fxZJ40QCFKG7EVSK0pmOrnmPLyckWHDppO9507j4olDBNEWaW4hV6IkmG0uq+RCrcHD15GVOQ7mDChBz7730j4+LjD1dV80RKxFVwtbU9JcFeoIBy0VnUpQHUfBRAZCkDb10hN0FZY7jRmBDqNGcEPtsWGH+qvJ5FI0KdPC8yYOQRt2tTX+SwxMRXff7cFW7acwukz35p87ijVQI5KwocN8vmjjHmd6YlBjDG0e2kQ7p45h2r16ooSi8SgLyQaClnsPmkcGDPtRcV5zkT17m7Z8RkDGHBp7wE07R5dwrMxjSmxyJzwUqOGHz5eNQ4KmfZ5X8NdiRHuqfx7Lq0KJ07p74MxpiOMWZrUXeiZZur3ZdD7SIQ3nbGwPGuIuoBWmHqWmoOPdv9jQPw07aWXq9aeg0yVy59HSGQTUe0DSJgi7AQJUwQArFp1AF/MHQV3dxeMGNkJ06evQHp6NmoL8ipUFGEK0IQUvTL2O6xZfQBLl72BOnWqwtFRgVmzR+DFoR0wYfwiHD9+3ex+ojq3xrDmzqjvpX0A5yglOPDIDbfTHQEATl6ekPtVw4VTZ212PoQ47CHGlFXs4dGhUqmRlpYFT09X+Pi4o0ePZpDLNZ1NqsZHEIS1sLQSrEqlxuLFO7Bu3VHMnj0cE1/vySc7t0d7xJLx7BmYs0awcVeoMCYsBXLBmNXPWYXNa17n3+fk5GPAq8vx/Gtjigg23GB71ZQPRCcSBwC5XIZhw57D+9MHo2HDGjrrPniQgG++/ge//bYPubkab3Lhc6d6g3p4cdZMnW10vGkKPUx2LfzJaOhckYTohYLWCx9Ng3sVH1gLQ0KiflEXQ15UhuBC5zKTU+Dq7SU6fxRjatRs3NCkUKeP5v/1FM7OznD0cCtWtUROTGs/4kWz6zrLGBRmfjpyqWa9DCPDTkNJ7i3FkHgkzB8l1vvIWHuYWo3Bn85ATkYmfw24+Xgb2rwIpsRfRycH3qNc5uUHT3/dPpqYayVPEMoXWN0fXSeMRevB/eFftaqo9gEkTBF2goQpAgAyMnKwZvUhvD6pF9zcnDF6dDR+/HErH8qXnp5dIQfrBw5cRuNGb+GTT17C1GkvQC6XoUGDGjh67EssXbIDixZth4OR0sgdO0bgo0+Hw8dLG7p3J80BBx65IVul+xD18K1i0/MgxEHhNbrYw6MjOTkTnp6u8PZ21Qvjo/xSBEFYj+JUgk1OzsBbby3DkSNX8ef6GXZvjzkOLl+LVpPeBVOr4SxjOqKUIZydHTDwndeQjaJeF9xgu9/77+Ds2jUY9sHbRQbBjGm8hnYv/xVOjgq8+mpXTJk6EDVr6uZAunLlAb6ctwHr1x/TqfQM6D13AmsjMdf8EDjpYZxBrzNjcMKPtdg071sc+2ODWSGREyC7vf4quk18xex+75w+h8gez4tuhzmhroj3WKGX19avv0dkVBQihg+yOC+W8Nhu3l4Wb2cJ7goVnGXGhSKhaGnOE04/p1NE5w5GQi8t/y64/bt5e+H1XxciKyUVAOBq5vthjCEzOQUe/n4IbR5ZRJxuFN0Roz99l3+frZQW638lDOWLHjkIfsnOFufRJWGKsAskTBEcixZtx+uTegEAJr3RC0uW7OA7GxXJW0qf3Nx8fPDBKqxbdwQ//fwWWrasCwCY+HovTJjYU9TsRK5SggOPXXErzRFA0fXTk55Zu9lEMaHwGvuSkpKJWrUC4OPjzucfSUvLwtGj1+zcMoIgCA0xMeWjzxNz+jxuxmgEm4AQcRNgmoTphge7XCjWRz+NhIMizeg+hq58Denpw1Cliu4kz/Hj1zFv7gbs2HFW1EDYkrDBu2cv8F5njbt2RvvhQ0RtKwZjIgfnHSZGlBJuE3PqrChhKqRxhNl1DGFMqGNqNSQCTz/Oy+v6oWMIdnTF6qkfop9eVTxrwtRqZKUZv26E1HDNR6ZSimyl5lo05PGnjzAEUGxSd++gQIz86jM06dalSP4oa4R4AoCLiaqR+hhLMs95cAU6FwDQOAJkKYsnmuUKhCnHQqHP0nMlYYqwC+VFmBITgkOUjOvXH+Lgwcvo3Lkx6tWrjjFjovnEnxVZmOK4fPkB2raZhjfe6I05X7wMNzdnUTfyBykMux97IVtd9DbO1GrkpWfgfqHLO0FUdrjKfAqFnHdX37XrfJFZdYIgiMqKJWHnsRc0Xjr9RvbCiJUTzO473CsXvrlyZBeKAtkqCXKUUqgKk1U7yxgczMRiKRQyHVFq+/Yz+HLeBhw7Zj4FghBLqhZy7zmvMzHCVGZyCly9PMWFvBnxNhImpxeL2fMqPJZ3VU0eV5VSib3LlkMmk6GrCEFLX6jjwkO5JOH64aJcWOrVA0dwef9h1I5qAk9/P/SfPln092MO7vs68MsqjG/2stn1OwRlo0NQNp7lyhCXpUB6vtSsx5+5EEBj8Dm5rCRE6SNW9CmSu0svyTxjDK4OWvHMEmFKIwpqBK1cZ21OWydp8SqOkzBF2AWHciJMGQrBCQz0gpeXG/9eoZBh2U9v6oht+lSW/DHFZfGi7ejcuTEAYPZnI/jl9+8l2KtJpYparcaPP27Fpk0nsXrNe3juOfOzWdNnrkarSe8CMFxx5862vVQFkiAKSU7OLLJsG4XxEQRB8Fgads7UasRfvyVq31G+uQaX56okyFFKUKAWN8hWqVT4889j+OrLDbh8+YGobfSxtGohh1hBa/PXP2D0N3OM7vvImnW4dvAoXLy9MEDPk6gkyelNnRdQVKCQymTo/vpr2vxexRDqOMyFiwq3KcjLM9pGS+G+L3lyHADzwhRHFScVqjiV3YkpayTPz8nIgJObm8F8aPrvXQVFlLJFClPc9Xxs7XoMefdVuCq043lPByX8nTQFnRQS8SIVCVOEXdD1mBJXicxeiAnBOXjwCuWPKQHnz99FQkIKAgK8ERSkjc8vKFAiMjK00nx/sbFP8e7kX3Du/Hdm1xW60ut3arZ+/QOCHV1t2FKCKB8EB/vB19cDMpluR0ulUiE+/hmCg/0qxb2FIAhCDKUddu4kY3Aykd9Hn4ED5mCbFYpWFKdqoVhBy5J9Xy30JLJWcnpjxzYkdHCJzfu9/w42ffW9STGtOB5c1mijKYQ5uCILK0CaY/OeawgLDUS9Wj6QScUf68VaqchTS5GvArLzVMhXAWqZA/LVUuSpJchXSZCvLrTC13kqaZFlKgYYSr1hiJKKU87u7mbX4fJr+Tlpx+JyKeNFJVP5tbjruXo1nyLhkGGeBQjzTOX2KLrNJExZmerVq6BWLW28tb53TWqqZtaWW5aeno2oZsHw9OwAtZoZXCc1NRNPnqTqHKe4A3Wuky5E2EZjxwJgVnixZB1/Qbb/unWr8tuVVwGC8scUn+BgP1y9ttig6/iMmUMwY+YQ5OTko369ifQd62Gs4o5UIkFwz572bh5B2JXgYD/cvLXU4L1FJpPhwMEv6N5CEESZoSJXcF2w7DCa9OkFF7kKLnLARa7mzVkuXpyKj0+2WpuKU7VQrOgkdt+2SE4vPHZYq+boOvEVowIHlw8pOyXVYqHOWm3kqtW1HtzfojxUmckpFotln834GRcu3IWbuwsGje6NgX2bo1+3Bma3U8gAhUwNKAAfJ+67tDziR6UGL1blqQSilUqCPLUE967ewn837iJfLUXdjh0hd/fUEb24bTQehiXzqDKWX6tzUBb/msuvlZ6nOd6uRT8h6WGczvUcMqKX2XBIsZAwpcekSZMwbdo0BAYG4tKlS3jrrbdw5ox4Zf7U6QVwdi7Of6eXRWsXpzNtqpNuCq7UqpOT8e1Kss7efZ/zr2mQUPnw9fUwe006OzvA19eDrgsDGOzUWLHcNEGUV+jeQhBEeaIiV3Bdu2wjVv51xmiFu0DnAgwLFZfA2poURxiyp+gkFu7YHn6+otb38PPFhZ17LRbqrNFGjn0/r9QR08whTGJvqaibmZGNlQv/wuXj59Gv23dmj5WaJ4WEqSBV5cPVWQFpMUMQZVLAWcrgDMNCbJOOIUDHEMGSDIPrMYYi3ljc6zwVkJmdDzg4F34m1fXkKnzt4aASnV/rYcJTowJl/M3b4k5eBCRMCXjxxRexYMECTJw4EadOncLkyZOxe/du1KtXD0+finsIaH4Qtg9NK05nWkwn3RCmxCZrr0ODBIIgCIIgCKIyUt488JOS0pGfr4SDg/EhpX7CdEMV7tTMNgmibYU9RSdLsKQCIVA2xLR75y+hxYDeonNeAbYXdUe/ugjb/tilCXWUSODi4ggPDxd4erkivEUT1GveCG36d4eDlGlMxuAoZVDwr9VwkDGdzxVQQVFMVyOJRFP5zlHGAEMpjn0AIKdY+9Zn/ay5/LkbgqmLl+jcECRMCXjvvffw888/Y8WKFQCAiRMnonfv3njllVfw5Zdf2rdxBoiMrAVXV0fR69etW9WGrSEIgiAIgiAIorIQG5uEiRP/xNUr56BSGR646idMt6TCHVEyLK1AWBYobnJ6W4q6cddv8cdjjCErKxdZWbl4/DgZN2/EQrJmBz4Kb2X0exaS8vgJ733k4CCHh4cL3N2d4eHhomfaZbUahiG8VVO4uTrxApcCSjjIACe5BA4W5GkrybnbGhKmClEoFGjWrBnmzp3LL2OMYd++fWjTpk2R9R0cHODoqBWF3EUkGLM2v/z6TqkfszSQyaR8iVFCi0wmg1Ra8b4b/aTEptaraOduiJSUTFHuyCkpmUa/j4p6rRCmof+7LnRvMQ1dL4Qh6LogLEEmkyH5WTYuX/4PKpXxKmf619N/l64i9UkiPP19IZFKkaOSQKmGydAic30fwjBbvvoeL5tIbL716x8glUisngaiJPeS64eOYfXUD9Hv/cnwCvTnl6cmPMXWr7/H9UPHrHIdWKPPzWHuez669k9cP3wc9wvDI2UyGVQqhpSULKSkZBnfcSESqRS1oprAw7cK0pOe4X6hmFgrqgm8/H2hzMpAyv27cHdzgoeHC9zcnRHRthnaDeoNL2933lPLQaqGo5TBTaFGNVfzUV7m+khi+1piIGGqEF9fX8jlciQk6JanT0hIQP369YusP3PmTMyaNauUWle5aNuuHQID69m7GWUOmUyGqKgoSCQSkw//8kbtUHHx75Xpunjjjb/g4eFk9PP09FxERLRARIThzyvqtUKYhv7vutC9xTR0vRCGoOuCsISSXC+x+47Ac8QgMMaQUSDDijvecJYxMKbx/ojZuR+p9/7j1zfX9yGMc/X3vxHWpxucvLShbnnpGbizbS+CHV1tUjDHGveSCz/8Aq+QYDh4uCE/PROpD2Kt3t6S9rmFmPqeVdfvop5fIOp1Dyxxm4P098MAuHijWsPmOuvdOv0It85ov0NnH29UbRkFJy8P+DspMaJOqtljmesjie1riYGEqWIyd+5cLFiwgH/v7u6O+Pj4Um3DqlUHkJgoPlGgv78nRo3qYsMWWYcTx4/jwoV79m5GmUMmk4Exhl27dlWozmJkZG0Ag8yuR9eFeCrqtUKYhv7vutC9xTR0vRCGoOuCsIQSXS87dyLi/HneKyajQIaMAiDlcQK2fv09rh44YptGV0Z27oRkwQ9FPG5sGaJVKe8ldvieLYXzvGrRqj5GfD/U7Prm+khi+1piIGGqkKSkJCiVSgQE6FaKCAgIwJMnT4qsn5+fj/z8/NJqnkG+/24LLly4K3r9yMjQciFMqVTqynMDsxC1WvPdVKTvx1hOAkPrVaTztjUV8VohzEP/dy10bzEPXS+EIei6ICyhJNfLpb0HcXn/4VKrBFepUalw59TZUj1kpbyX2OF7tojC9rnlpwAwL0yZ6yMlJKSaDYcUCwlThRQUFODcuXOIjo7G5s2bAQASiQTR0dFYuHChnVtHEBUXS0u8EgRBiIHuLQRBEGWf8lLhjiAqEtbqI5mriOjh4Y5Dh3qIahMJUwIWLFiAlStX4uzZszh9+jQmT54MV1dXLF++XPQ+NP9g6yUBM3UcSzvTYi5AQ+TmajzDnJyMb2etdWiQUPmwdYlXgiAqJ3RvIQiCIAiCKIo1+0imKiJ6eXmJbhMJUwLWr18PPz8/fPbZZwgMDMTFixfRo0cPJCYmit5Hq5bvQaHQvg8M9IKXlxv/PjU1EwD4Zenp2WjWvDnuxtyAWs0MrpOamoknT1J1jlOczrSxC1DYRmPHAmD2wrXWOjRIqHzYssQrQRCVF7q3EARBEARBFKWs9ZFImNJj0aJFWLRoUbG3j4t7htTUVNHry2QySCQB2LnzaKnE35bkAhSrmFpjHYIgCIIgCIIgCIIgKj62jzkjCIIgCIIgCIIgCIIgCAOQMEUQBEEQBEEQBEEQBEHYBRKmCIIgCIIgCIIgCIIgCLtAwhRBEARBEARBEARBEARhF0iYIgiCIAiCIAiCIAiCIOwCCVMEQRAEQRAEQRAEQRCEXSBhiiAIgiAIgiAIgiAIgrALJEwRBEEQBEEQBEEQBEEQdoGEKYIgCIIgCIIgCIIgCMIukDBFEARBEARBEARBEARB2AUSpgiCIAiCIAiCIAiCIAi7QMIUQRAEQRAEQRAEQRAEYRdImCIIgiAIgiAIgiAIgiDsAglTBEEQBEEQBEEQBEEQhF2Q27sBFQ13d3eoVCrR68tkMjg7O1u8HVH5oGuFEAtdK5UT+r8TlkDXC2EIui4IS6DrhTAGXRsEoNFGxELClJXw8fEBADx8+NDOLSEIgiAIgiAIgiAIgrA/Pj4+yMjIMLkOCVNWIjk5GQBQrVo1nS/99OnTaNmypdHt3N3dER8fX2Q7a2Du2LbGnseviOcu9lqh753O3Zb3FXPHtgf0f6f/e2U6trWOX9zrpSKcOx3bOMaui8pw7mXx+GX93GkcU/GOba3j0zOmfB3bVsfnrgNOKzEFCVNWJiMjQ+fHp1arRf0Y9bezBmKPbSvsefyKfO7mrhX63uncOWxxXxF77NKE/u/0f69Mx7b28S29XirSudOxjVPc/qwtoGuu7J87jWMqzrGtfXx6xpSPY5eF41PycxuzaNGiSnlsex+fzr3yHdvex6dztx907pXv2PY+Pp27/ais507fu/2gc698x7b38enc7Qedu/2QAGB2bUEFwd3dHenp6fDw8LBIaSzudkTlg64VQix0rVRO6P9OWAJdL4Qh6LogLIGuF8IYdG0QgGXXAXlMWYm8vDzMmjULeXl5pbIdUfmga4UQC10rlRP6vxOWQNcLYQi6LghLoOuFMAZdGwRg2XVAHlMEQRAEQRAEQRAEQRCEXSCPKYIgCIIgCIIgCIIgCMIukDBFEARBEARBEARBEARB2AUSpgiCIAiCIAiCIAiCIAi7QMIUQRAEQRAEQRAEQRAlplGjRpDJZPZuBlHOIGGKIAiCIAiikiKRSOzdBKKM0qlTJ3s3gSCIcsbHH3+MixcvomPHjpBKSWogxENXC0EQBEEQRCWFMU1x5tmzZ6Nt27Z2bg1RVmjfvj02bdoEX19fezeFKKMIRW2FQmHHlhBlif/973/Ys2cPVqxYgc6dO5M4RYiGrpQySI0aNezdBIIgCKKC0L59e7Rp08bezSDKGMJB5YABA/Dhhx9CpVLZsUVEWeLYsWMIDQ1FUlISQkJC7N0cogzCidpTpkzBihUrsGnTJjRp0oREqkqMXC4HAPTs2RM3b97EypUrSZwiRENXSRmjd+/euH//PiZOnGjvphAEUc6hEB2iS5cuOHz4MHx8fPgOI0EA2kHlSy+9hKpVq2LixIk4deqUnVtFlCWePXuG4OBg3L17Fx9++KG9m0OUEYR9i+nTp+Ojjz7C06dPUbNmTezZswdDhgyBi4uLHVtI2AOJRAKlUsm/79atG27cuEHiFGERjKxs2NChQ1leXh5LSkpic+fOtXt7yMjIyqc5OjrqvG/atCkbPHgw69mzJ6tevbrd20dWOla1alU2btw4NmPGDLu3haxsWmhoKHvw4AFTqVTs3XffZQCYTCaze7vIypZNnjyZ5eTksKlTp9q9LWRlx2rUqMGWLl3K2rdvzy/77bffWHx8PBs5ciRzcXGxexvJSt969erFWrduzb/fu3cvi4uLY9HR0Uwqldq9fWRl2uzeADKADR48mKlUKtarVy82Y8YMtmfPHgaASSQSu7eNjIys/NjMmTPZd999x6pUqcIAsP79+7OcnBx28eJFlpeXx44cOcLefvttu7eTzLYWHBzMVCoVS01NZe+//77d20NWNs3R0ZH169ePXbx4kZ05c4ZfToMHMn174403mEqlInGKjAFgI0aMYCqVil2/fp01a9ZM57Nff/2VxcXFsREjRjA3Nze7t5Ws9Kxu3brs8ePHbPny5TrXBSdOdenShZ4vZKbM7g2o9BYdHc1UKhUbO3YsA8BmzJjBjh07Zvd2kdnHSIwkK4mNGzeOqVQq9vnnn7N69eqxf//9l40fP545OTmxBg0asB9//JGdPXuWvfnmm3ZvK5l17JtvvmFdu3Ytsnz8+PEsNzeX/frrr8zJycnu7SSzr+k/WxQKBQPA5HI569GjB7t//z7bu3cv/zkNHiqvNWzYkHXu3Jn1799fZzmJU5XXDPVNN27cyFQqFRs5ciSTy+U6n/38889MpVKx7t27273tZKVrgwcPZpcvX2a//PKLjji1Z88e9t9//7FevXrRWIfMmNm9AZXeatSowXr37s2/79u3Lztz5oxOp1DoEklWOezNN99kP/zwA1u6dCnz8/Oze3vIyo9xM5nz589nGzZs0Ll+atasyZYsWcL279/Pe1WRlV/z9vZmc+fOZY0bNzb4+YQJE5hKpWLTpk2ze1vJyoZNmzaN/f333+zEiRNs4sSJrEaNGgwA69GjB7t58ybbtWsXvy4NHiqfDRgwgD18+JBdunSJJScns127drGIiAj+WnjjjTdYTk4O+/jjj+3eVrLSN/0wrZ07d7JHjx4ZDNOaOXMmCdyVxNzd3XXev/DCC+zatWvsl19+YZGRkfzy8+fPs02bNtm9vWRl1uzegEpt3Iyl0J5//nmWkpLCAgICGAD2v//9j6lUKhInKrgJBwCzZs1iycnJ7I8//mAxMTHs/v37rG3btnZvI1n5sZdffpmpVCpWUFDAoqKidD6LiopiKpWKdezY0e7tJCu+ff7552zcuHF8TrHGjRuTlwNZERM+Wz799FOWlJTEvv32W/bzzz+zpKQktmbNGv4e0bNnT3b16lV27tw5u7ebrPSuC86io6PZs2fP2CuvvMIATX5ClUrF9u3bpzO4nDp1KktKSmLe3t52Pw+y0rPihmmROFXxrHXr1iwsLIwBYO+88w774YcfWM2aNXXWGTx4MEtISGArV67UuX/QhAeZCbN7AyqlRUdHswULFrDdu3ezV199lTVs2JD/LDIykiUkJDA3Nzf2wQcfsPT09CLx22QV1/z8/Njy5ctZ8+bNGaBJRLtt2zYWHx+vk2CSjMycDRkyhKlUKrZo0SJe6AbAqlSpwq5du8aef/55u7eRrPj2+eef888Ozsvh8uXL5OVAZtCCg4PZF198wTp37swv69q1Kztz5gz77bffmLOzM3N0dGSDBg1ia9eupcFDBTbuf1uzZk3Wt29fBmgmShcsWMA+/fRTBoCFhISwmJgY9ttvv7GYmBh24sQJFhUVxW/r5eVl9/MgK32jMC2ymjVrsn///Zdt3ryZBQQEsDFjxrCnT5+yOXPm8B64nH3yyScsOTmZ/fnnnyw8PJxfTmIlmRGzewMqnQ0YMIClpaWxxYsXs9mzZ7PY2Fi2bds2FhISwgAwDw8Pdu3aNbZz506Wm5tLolQlsldeeYWlpqay06dPs7p16+p8tm3bNhYXF8fatWtn93aSlU2rXbs2a9myJWvTpg1zcHBgANiYMWOYSqViS5cuZe3atWM1a9Zkc+bMYcnJyVShrxyaoQ5/165d2bNnz9irr77KAPJyICtqffv2ZSqVij158kRHmALAunfvznJzc3kPSmFVPhpgVlwLCgpiiYmJ7Nq1a2zYsGEM0Hjsh4eHM09PT3bq1Cn2008/MQCsU6dOTKVSsTNnzhgNGyaruEZhWmT69tprr7H9+/ezP/74g7m6urIhQ4awR48esblz5+p4Tr3zzjvs4MGDbPny5fQ8IRNjdm9ApbLq1auzixcvsgkTJvDL0tLS2Ny5c/n3Pj4+LDs7m6lUKtaoUSO7t5ms9MzPz4/t3buX5efn86F7whv55s2bmUqloo4hWREbOHAgu379Ort9+zY7ceIEu3jxIh/+y+WcUqlU7M8//2SHDx9mTZo0sXubySwzoZdDv379GADm4ODAvv/+e94TirwcyITXCvc3KCiI/fjjj0ylUrFRo0YxQFeAunLlCpsyZYrd201WetaxY0emVCrZqVOn2KZNm9jQoUP5zwYMGMBOnz7NQkNDGaDJP7Zp0yZ27tw5VqtWLbu3ncy2RmFaZGJs9OjR7OjRo+yPP/5gbm5ubOjQoSw+Pp7NmzePtWjRgsnlcrZhwwY2aNAguj7IxJrdG1CprHr16uzcuXPMxcWF1alTh8XGxrJly5bxn7dq1YoBYB06dCjiMUNWsczYzdnX15edPHmSXbt2je8UCu2rr74iF9hKbNx14+zszC/r0KEDS09PZ+PGjWMKhYL17t2bqVQqNnnyZH6dAQMGMJVKxT766CPm4eFh9/MgK54Z8nLo0aMHCw8PZ15eXuTlQMaGDh3Kfv31VxYWFsZcXFz45QEBAWzFihUsKytLx2vK3d2dxcTEsPHjx9u97WSla7/88gs7f/48++uvv9j+/fvZyJEjGaApmhAbG8uqVq3KAE3Y8KxZs6jvUQmMwrTILDF9cWrQoEHs0qVL7NGjR+zmzZvsypUrOpMgZGRmzO4NqLDm7OzMqlSpwjp16sSqVq3KPDw8WNWqVVlsbCzr1KkTu3PnDlu2bBk/0GzUqBH7448/dGYeyCqmCUWpiIgI1rRpU74DCGhyAJ05c4ZduXLFoDgFmH7wh4SEsAkTJrD58+ez559/nqqvVRDjrpuoqCh2584dfgZz2rRp7Mcff2SARvx+8OAB/x4AX8Z5+PDhrH79+nY/D7Lim76Xw4svvsh/Rl4OZO7u7uzOnTssISGBXbp0if38889s9OjR/OfOzs5s7dq1LDs7m3377bds2rRpbMuWLezy5cs0eKjApj8RxoV69+zZk/3222+sa9eubMOGDezQoUOsf//+zNPTk8XGxrI7d+6wo0ePspSUFPKyrURGYVpklphQnPLy8mJ16tRhL730Ehs/fjz/XCGxkkyk2b0BFdLCwsLYihUr2PXr11l2djZLSUlha9asYQ0bNmTz5s1jKpWKrV+/XmebOXPmsFOnTrHAwEC7t5/MdiZ8eM+ePZvFxMSwmJgYlp6ezkaPHs2H2vj4+LDTp0+zixcvsnr16onef0REBIuLi2Pbt29nt27dYjdu3GDTpk2jTkM5N+7/17hxY5aWlsYWLFjAf7Zs2TL2888/s6CgIPbw4UO2dOlS/rNBgwax9957jzoFFcjIy4HMmEmlUjZnzhw2fvx4FhkZyaZMmcKSk5PZ2rVr2fTp05lcLmdVqlRh8+fP5/shQ4YM4YUKEqcqnnHPjurVq7MBAwbofObr68uuX7/OJk2axHx9fdmGDRvY0aNHWe/evZmfnx/76quv2Jw5c2hCoxIahWmRWWJjxoxhR44cYX/88QefM5kz6oOQWWB2b0CFs0aNGrH4+Hi2ePFiNmrUKFavXj02d+5cdufOHXb9+nU2ZcoUtmLFCnb79m0WHR3NXnjhBTZ//nyWlpZG4RaVyD7++GMWHx/PV0ZbtWoVS01NZVOnTmWenp4M0IhTDx48YKtWrRK1zxo1arBbt26xzz//nPeS+eKLL9jt27f5kvJk5c+EXpWZmZns888/1/l8woQJbP369Sw+Pp4P4wI0nlKLFi1iCxYsYE5OTnY/D7Li/d85Iy8HMjHWo0cPlpaWxueodHR0ZLNnz+bDOqdNm8Z69OjB5s+fz9LT01mbNm10ri+yimfVq1dnT58+ZSqVim3bto0NGTKEzyHUp08fdvjwYebr68vq16/PNmzYwA4cOKAjOJBVTqMwLTJLbMyYMezQoUNs1apVrFq1anZvD1m5NLs3oEIZN3CcM2dOkZv10KFD2cmTJ9m///7LXn31Vfbrr7+ylJQUdunSJbZnzx5KdF7BTZhMsmHDhmzPnj2sd+/eDADr168fe/bsGdu4cSNTqVRs6tSpzMfHhwGaKo1iZhukUil766232Lp161hAQAC/jb+/P3vw4AGLiIiw+3dAVnyrXr06S0xMZOvWrdNZPm7cOLZ27Vp26dIllpCQwFq3bs0AMFdXV/b555+z+Ph4yldXDo28HMhKYgsXLmQLFy7k31+9epX9888/7Ouvv2Y7duxgKpWKvf/++2zVqlUsOTmZr8hHVjGtRo0a7PTp0+z48ePs7Nmz7KeffmL3799n48aNY0OGDGFbtmxhPXr0YABYeHg427NnD9uyZUuRamxklc8oTIvMEhs7diz7999/2dixY+3eFrJyaXZvQIUxbuD4559/6iwXClTjx49nT58+Za+99hoDwEJDQ5mLiwtzc3Oze/vJbGdcMslNmzaxWrVqMQcHB/bKK68whULB2rdvz+Li4tgbb7zBALB169ax5ORkNmvWLJ3rQsyDf9SoUeytt97SWebn50cDjwpgNWvW5PMKcRUbZ8yYwTIzM1mDBg1Y1apV2b1799jZs2fZzZs32fbt21l8fDxr2rSp3dtOVjwjLwey4torr7zCjh49yry8vNi5c+fY0aNHeZGhWrVqbMiQIUwmkzEXFxe2ceNGFhsbS16VFdzq1KnDNmzYwP755x82YMAA1r9/f3bgwAH2zz//MJVKxf7991+mUCgYAFa3bl3yeCDjjcK0yCyxrVu3so0bN9q9HWTl0uzegApjwoFju3btjK535MgR9vfffzOAbuiVyV577TW2b98+tm7dOp0O37Jly9ivv/7Kh9798MMP/EDCGsd1dHRk169fZy1atOCX9e3bl1WvXt3u3wmZZVanTh22Y8cOtmnTJrZs2TL25MkT1rVrV/7zgIAANnToUDZnzhw2bNiwIh1IsvJl5OVAVhI7deoUU6lU7ODBg8zb29vgOjKZjPn4+LCgoCC7t5fM9la3bl22fft2tmvXLr5qY6tWrdiWLVvYiBEj7N4+srJrFKZFJtYWLlzIfv/9d17oJiOzwOzegApl3MBx586dRsWpAwcOsNWrV9u9rWSlb6NHj2aHDx9m69at4z0fDh06pFNB7e+//xadaywoKIj17t2bDRw4kDVr1oxfLhQ8FQoFu3r1KmvevDkDNEn2Hz16xIKDg+3+fZBZbmFhYWz37t0sKyuLvffee/xyyvNQMY28HMiKayNGjGCXL19mUVFRdm8LWdmxOnXqsF27drFdu3bx3rdkZGKMwrTIzFmVKlXY0aNHWcOGDe3eFrJyaXZvQIUzoTglfOhLJBJWrVo1tn37djZq1Ci7t5PMPjZ69Gh28OBBtm7dOubj48MmTZrElEolW7t2LTt37hy7evWqKJEhIiKCxcTEsJMnT7LExER2+vRpg2E8np6eLCEhgbVp04Z9+OGHLDs7W0fEIit/Vrt2bbZr1y62fft2k96ZZBXDyMuBrDhWtWpVFh8fz6ZPn273tpCVLRMziUpGZsgoTIvMnFGxJbISmN0bUCHN2EN/7ty57MKFCzSrXclt9OjRfLx+QEAAGzduHFu3bh1bsmQJH9JnKsyzdu3a7OHDh2zevHnMw8ODRUVFseXLl7NffvmlyHaurq7s3Llz7MCBAyw7O5tmzyuIGRPAySqmkZcDWXHszTffZE+fPmXh4eF2bwtZ2bI6deqwLVu2sBMnTrBWrVrZvT1k5cMoTIuMjMyGZvcGVFgTDhybNm3Kpk2bxtLT00WHaZFVbBMmk/T392eAthKXKY8phULBvvnmG7Zu3TqdjsHYsWPZ06dP+Wp+nHl4eLD79++zpKQkuvYqmNHAonIZeTmQWWq1a9dmK1as4J8tZGRCq1evHlu/fj2F9pOJMgrTIiMjs7HZvQEV2riB45MnT1heXh55q5Dp2JgxY9jhw4fZypUrRXvROTo6ssmTJ7NXX31VZ3m9evXY/fv3WUBAQJFtPvjgA1avXj27ny+Z9Y0GFpXLSIwkK65RsRUyQ0aeL2SWGIVpkZGR2dDs3oAKb3Xr1mWbNm1iDRo0sHtbyMqeFSeZpKFqawEBAez27ds61fYol1TlMBpYVC4jMZKMjIyMjIyMjKwimaTwBWFj5HI5lEqlvZtBlFG2bt0KpVKJgQMHGvw8MDAQwcHB8PHxwZ49e8CY5mcrlUqhVqsBAHXr1sXx48cRGRmJuLg4zJ49G2+++SbCwsKQnJxcaudCEITtUSgUKCgosHczCIIgCIIgCKLEyO3dgMoCiVKEKf777z/4+PgYHGw2atQIW7ZsQV5eHgICAvD48WN89tln2L17N1JSUvj1GGNQq9XIzMzEhx9+iKlTp6JDhw4kShFEBYREKYIgCIIgCKKiILV3AwiislOlShU0adIEc+bMKTLY9PX1xZ9//om1a9eiZ8+eaNCgAS5duoSPP/4Yb7/9Nnx9ffl1U1NTERcXhyVLluDjjz9Ghw4dcP78+dI+HYIgCIIgCIIgCIIQDYXyEUQZwNHREXl5eUWWh4eHY/v27Rg8eLCOyDR37lz06tULq1evxqJFi5CTk4P69evj2rVryM3NRdu2bXHp0qXSPAWCIAiCIAiCIAiCsBjymCKIMoAhUQrQ5JGRy+VwcXEBADg5OQEAZs6ciYMHD+L1119HnTp1AAApKSlYvHgxoqKiSJQiCIIgCIIgCIIgygXkMUUQZZxTp04hMzMT0dHRAAAHBwfk5+cDAE6fPo2YmBgMHz4cgHHPK4IgCIIgCIIgCIIoi5DHFEGUIVxcXODm5gZ3d3d+2YQJE9CwYUOsXbsWAJCfnw+ZTAYAOHLkCFxdXfl1SZQiCIIgCIIgCIIgyhMkTBFEGSE8PBz//PMPDh8+jBs3bvBeUDdu3MA777yDrl27Yv369ZDL5VCr1QAAf39/ZGVl8UIVQRAEQRAEQRAEQZQn5PZuAEEQGlHqyJEjWLVqFc6ePYtmzZph+fLluH79Oi5evIgtW7YgKysLixcvxuXLl3Hz5k3k5+ejd+/eaN26NVQqlb1PgSAIgiAIgiAIgiAshnJMEYSd8fb2xh9//IGbN29i8uTJ/PIDBw7gypUreOedd/hlbm5u+Oijj+Dj44Pc3FwsWbIEN27csEOrCYIgCIIgCIIgCKLkkMcUQdgZhUIBLy8vbNiwAQAgkUjAGMP9+/fh4+PDryeRSJCZmYkZM2borEcQBEEQBEEQBEEQ5RXKMUUQdiYxMREjR47EsWPHAIDPFxUfH8/nkgIAxphOUnQSpQiCIAiCIAiCIIjyDglTBFEGiImJAaDxglIqlfxrf39/fp0ZM2bgtddeo0TnBEEQBEEQBEEQRIWBQvkIogyh7wXFeUzNnj0bH330ESIjIynROUEQBEEQBEEQBFFhII8pgihjSCQSAIBSqURsbCymTJmC999/H82bN8fly5ft3DqCIAiCIAiCIAiCsB7kMUUQZQzOa6qgoADjxo1Deno62rdvjwsXLti5ZQRBEARBEARBEARhXchjiiDKKLt37wYAtG3bFufOnbNzawiCIAiCIAiCIAjC+kgAUGkvgiijuLi4IDs7297NIAiCIAiCIAiCIAibQMIUQRAEQRAEQRAEQRAEYRcolI8gCIIgCIIgCIIgCIKwCyRMEQRBEARBEARBEARBEHaBhCmCIAiCIAiCIAiCIAjCLpAwRRAEQRAEQRAEQRAEQdgFEqYIgiAIgiAIgiAIgiAIu0DCFEEQBEEQBEEQBEEQBGEXSJgiCIIgCIKoYCxfvhwbN24s0T46duwIxhg8PT2t1CqCIAiCIIiikDBFEARBEARhR5YvXw7GGBhjyMvLw507d/Dxxx9DJpMVe5/vvPMOxowZY71GEgRBEARB2Ai5vRtAEARBEARR2dm5cyfGjh0LR0dH9OrVC4sWLUJBQQHmzZtn0X6kUikYY0hPT7dRSwmCIAiCIKwLeUwRBEEQBEHYmby8PCQkJODhw4dYunQp9u3bh379+sHBwQFff/014uLikJmZiZMnT6Jjx478dqNHj0ZKSgr69u2La9euIS8vDzVq1CgSyufg4IDvv/8eCQkJyMnJwdGjR9G8eXOdNvTs2RO3bt1CdnY2Dhw4gJCQkNI6fYIgCIIgKjEkTBEEQRAEQZQxcnJy4ODggIULF6JNmzZ46aWX0LhxY/z111/YtWsX6tSpw6/r4uKC6dOn47XXXkPDhg2RmJhYZH9fffUVBg0ahNGjRyMqKgoxMTHYvXs3vL29AQDVq1fHP//8g61bt6Jp06b45ZdfLPbWIgiCIAiCKC6MjIyMjIyMjIzMPrZ8+XK2ceNG/n10dDTLyclhv/32GysoKGBBQUE66+/du5fNmTOHAWCjR49mjDHWuHFjo/t0cXFheXl5bNiwYfzncrmcxcXFsalTpzIAbM6cOezq1as6+5g7dy5jjDFPT0+7f0dkZGRkZGRkFdcoxxRBEARBEISd6dOnDzIyMqBQKCCVSvH7779jw4YNGDt2LG7fvq2zrqOjI549e8a/z8vLw+XLl43uOzQ0FA4ODjh+/Di/TKlU4vTp0wgPDwcAhIeH49SpUzrb/fvvv9Y4NYIgCIIgCJOQMEUQBEEQBGFnDh48iNdffx35+fl49OgRVCoVXnzxRSiVSjRr1gwqlUpn/czMTP51Tk5OaTeXIAiCIAjCapAwRRAEQRAEYWeysrJw9+5dnWUXLlyAXC6Hv78/jh07Vux93717F3l5eWjXrh0ePnwIAJDL5WjRogW+++47AMCNGzfQr18/ne1at25d7GMSBEEQBEGIhZKfEwRBEARBlEHu3LmDNWvWYNWqVRg4cCBCQkLQokULzJgxA7169RK9n+zsbCxZsgRff/01unfvjvDwcPz8889wcXHBr7/+CgBYunQpwsLC8NVXX6Fu3boYNmwYxowZY6MzIwiCIAiC0ELCFEEQBEEQRBll7NixWLVqFebPn49bt25h06ZNaNGiBe/5JJYZM2bg77//xurVq3H+/HnUqVMH3bt3R2pqKgAgNjYWgwYNwoABA3Dp0iVMnDgRH3zwgQ3OiCAIgiAIQhcJNFnQCYIgCIIgCIIgCIIgCKJUIY8pgiAIgiAIgiAIgiAIwi6QMEUQBEEQBEEQBEEQBEHYBRKmCIIgCIIgCIIgCIIgCLtAwhRBEARBEARBEARBEARhF0iYIgiCIAiCIAiCIAiCIOwCCVMEQRAEQRAEQRAEQRCEXSBhiiAIgiAIgiAIgiAIgrALJEwRBEEQBEEQBEEQBEEQdoGEKYIgCIIgCIIgCIIgCMIukDBFEARBEARBEARBEARB2AUSpgiCIAiCIAiCIAiCIAi7QMIUQRAEQRAEQRAEQRAEYRf+D6NO/wk3E0zlAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Total Royalties (through 2023): $2,023,005.07\n", "Total Expenses (through 2023): $393,624.60\n", "Net (Royalties - Expenses): $1,629,380.47\n"]}], "source": ["# Plot royalties and expenses combined\n", "# First, let's prepare the royalties data for plotting\n", "royalties_plot = royalties.copy()\n", "royalties_plot['block_timestamp'] = pd.to_datetime(royalties_plot['block_timestamp'])\n", "royalties_plot = royalties_plot[royalties_plot['block_timestamp'] <= '2022-07-31']\n", "\n", "# Prepare expenses data (all_transactions) for plotting\n", "expenses_plot = all_transactions.copy()\n", "expenses_plot = expenses_plot[expenses_plot['block_timestamp'] <= '2022-07-31']\n", "\n", "# Create the plot\n", "fig, ax = plt.subplots(figsize=(12, 6))\n", "\n", "# Plot royalties\n", "royalties_grouped = royalties_plot.groupby(royalties_plot['block_timestamp'].dt.to_period('d'))['amount_usd'].sum()\n", "royalties_grouped.plot(kind='line', ax=ax, label='Royalties', marker='o', linewidth=2)\n", "\n", "# Plot expenses\n", "expenses_grouped = expenses_plot.groupby(expenses_plot['block_timestamp'].dt.to_period('d'))['amount_usd'].sum()\n", "expenses_grouped.plot(kind='line', ax=ax, label='Expenses (Floor Sweeps + Buybacks)', marker='s', linewidth=2)\n", "\n", "plt.title('Royalties vs Expenses by Month (Through End of 2023)')\n", "plt.xlabel('Period')\n", "plt.ylabel('Amount USD')\n", "plt.legend()\n", "plt.xticks(rotation=45)\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Display totals\n", "print(f\"Total Royalties (through 2023): ${royalties_plot['amount_usd'].sum():,.2f}\")\n", "print(f\"Total Expenses (through 2023): ${expenses_plot['amount_usd'].sum():,.2f}\")\n", "print(f\"Net (Royalties - Expenses): ${royalties_plot['amount_usd'].sum() - expenses_plot['amount_usd'].sum():,.2f}\")\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["found 12981 rows\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>block_timestamp</th>\n", "      <th>block_id</th>\n", "      <th>tx_id</th>\n", "      <th>index</th>\n", "      <th>tx_from</th>\n", "      <th>tx_to</th>\n", "      <th>amount</th>\n", "      <th>mint</th>\n", "      <th>fact_transfers_id</th>\n", "      <th>inserted_timestamp</th>\n", "      <th>modified_timestamp</th>\n", "      <th>__row_index</th>\n", "      <th>day</th>\n", "      <th>accounting_period</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2021-11-27 17:10:36+00:00</td>\n", "      <td>*********</td>\n", "      <td>3tzWhQyb2W3hY5mwQxQjDT1Sro34nJG6t95FiHRtVrCTggrJ65rKD1Ggahuzs7RY5Z7VgegmDmQytajuaTXkEzM6</td>\n", "      <td>0</td>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>6FKvsq4ydWFci6nGq9ckbjYMtnmaqAoatz5c9XWjiDuS</td>\n", "      <td>0.00</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>d53b51ef2581cc5a991bb622eac5b073</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>0</td>\n", "      <td>2021-11-27</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2021-11-27 17:10:43+00:00</td>\n", "      <td>*********</td>\n", "      <td>iJhbaTeux4eqZNfEBqdmdDrdrbXy64Lh6Z5Y4yGBppdoBTKkKABM5hkwHcRH3EWfFBV6GVd9fwb1zrNLumJAUtr</td>\n", "      <td>0</td>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>6FKvsq4ydWFci6nGq9ckbjYMtnmaqAoatz5c9XWjiDuS</td>\n", "      <td>0.00</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>dda7447fa4530ccbf959fd3b9c011e95</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>1</td>\n", "      <td>2021-11-27</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2021-11-27 17:10:57+00:00</td>\n", "      <td>*********</td>\n", "      <td>2ojQaMYFi9yTwCxyxuUeS3RL6pRhYAidzqftkrMH6iAfXN44pLKkZ92yB2aHkex8nd2DW99YB4z2fKbCqY9FY1RZ</td>\n", "      <td>0</td>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>6FKvsq4ydWFci6nGq9ckbjYMtnmaqAoatz5c9XWjiDuS</td>\n", "      <td>0.00</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>c7be1452031e7101712279c16c4de999</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2</td>\n", "      <td>2021-11-27</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2021-11-27 17:11:04+00:00</td>\n", "      <td>108977902</td>\n", "      <td>5RXQ9HcM8sa1FT9p8wNaida7F3qk88hpNxrwBoUDByR7NhEVkZJepv3drg6WZCcfssttWRP4rRd874bwSiXFseH2</td>\n", "      <td>0</td>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>6FKvsq4ydWFci6nGq9ckbjYMtnmaqAoatz5c9XWjiDuS</td>\n", "      <td>0.00</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>c32b661ab13a95894825ad8b4729c47c</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>3</td>\n", "      <td>2021-11-27</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2021-11-27 17:11:09+00:00</td>\n", "      <td>108977912</td>\n", "      <td>9fa72UUsD3cCLWZHwnRLhW6qfKwiJZDeDUxg5XnZwnKR5D61Y3CdzW97xDk3SagM3AUJ4LXDinXP1GEMvyTeUxZ</td>\n", "      <td>0</td>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>6FKvsq4ydWFci6nGq9ckbjYMtnmaqAoatz5c9XWjiDuS</td>\n", "      <td>0.00</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>c05e0c68edc165cb04dc9acc286ad892</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>4</td>\n", "      <td>2021-11-27</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12976</th>\n", "      <td>2022-05-20 23:42:31+00:00</td>\n", "      <td>134585797</td>\n", "      <td>2oqXSmbPX9LF8HZabWEKcpGQaiffRHh4Vb363CR397UMNy91hZyctRKcoFqvgjZC5jgHNMHKhc4DDbnUDUiNVw6x</td>\n", "      <td>0</td>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>6FKvsq4ydWFci6nGq9ckbjYMtnmaqAoatz5c9XWjiDuS</td>\n", "      <td>0.00</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>d74b0c5ecbc3a3d7b52594295f702185</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>12976</td>\n", "      <td>2022-05-20</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12977</th>\n", "      <td>2022-05-21 02:12:01+00:00</td>\n", "      <td>134598673</td>\n", "      <td>3LuF8eYEtkZgPHLENvirssHZoK5LhK6aagWZC8t9P7gEjtMBmDKgAPWCem9CjcDaSuGtgzFLoUVFYtmpAwm3MSbC</td>\n", "      <td>0</td>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>6FKvsq4ydWFci6nGq9ckbjYMtnmaqAoatz5c9XWjiDuS</td>\n", "      <td>0.00</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>d25ebf892ab857835726c619fd41a189</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>12977</td>\n", "      <td>2022-05-21</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12978</th>\n", "      <td>2022-05-21 18:22:56+00:00</td>\n", "      <td>134686818</td>\n", "      <td>4Mf7dqsHEAGX8GYKvxmASdsbD6L5uuw6Ns7xvVKt9fcbfy4AbzHTyJ1w2n2GxnTS7SWVLaRZXx8bcdBdDq5MTyLD</td>\n", "      <td>0</td>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>6FKvsq4ydWFci6nGq9ckbjYMtnmaqAoatz5c9XWjiDuS</td>\n", "      <td>0.00</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>349c53e3b19c31c6b0cc4b692e81b521</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>12978</td>\n", "      <td>2022-05-21</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12979</th>\n", "      <td>2022-05-21 18:23:40+00:00</td>\n", "      <td>134686877</td>\n", "      <td>Mvxft3kYhBxdyVaSyiYRUYtGtqZ9h1L3rFQL5PxDc1kciCzA7nDiykPPbwuBXa2dBZgT29CEyDrALmiAQcw5TUh</td>\n", "      <td>0</td>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>6FKvsq4ydWFci6nGq9ckbjYMtnmaqAoatz5c9XWjiDuS</td>\n", "      <td>0.00</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>f04d9c73c8a65b3712015d9a751b8a7d</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>12979</td>\n", "      <td>2022-05-21</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12980</th>\n", "      <td>2022-05-21 20:52:56+00:00</td>\n", "      <td>134700224</td>\n", "      <td>5ppv816gmx9FgeWoCbsfoMcYyQFhbtwR44PFe1hxoJD4xwJjtujnEDeSLVZrC82Sr48Yy1QNakq2XMb2hgoASsTc</td>\n", "      <td>0</td>\n", "      <td>PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb</td>\n", "      <td>6FKvsq4ydWFci6nGq9ckbjYMtnmaqAoatz5c9XWjiDuS</td>\n", "      <td>0.00</td>\n", "      <td>So11111111111111111111111111111111111111111</td>\n", "      <td>62be62c4eef62a2eb54c2f982278ecee</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>2000-01-01T00:00:00.000Z</td>\n", "      <td>12980</td>\n", "      <td>2022-05-21</td>\n", "      <td>2022</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>12981 rows × 14 columns</p>\n", "</div>"], "text/plain": ["                block_timestamp   block_id                                                                                     tx_id index                                      tx_from                                         tx_to  amount                                         mint                 fact_transfers_id        inserted_timestamp        modified_timestamp  __row_index         day  accounting_period\n", "0     2021-11-27 17:10:36+00:00  *********  3tzWhQyb2W3hY5mwQxQjDT1Sro34nJG6t95FiHRtVrCTggrJ65rKD1Ggahuzs7RY5Z7VgegmDmQytajuaTXkEzM6     0  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb  6FKvsq4ydWFci6nGq9ckbjYMtnmaqAoatz5c9XWjiDuS    0.00  So11111111111111111111111111111111111111111  d53b51ef2581cc5a991bb622eac5b073  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z            0  2021-11-27               2022\n", "1     2021-11-27 17:10:43+00:00  *********   iJhbaTeux4eqZNfEBqdmdDrdrbXy64Lh6Z5Y4yGBppdoBTKkKABM5hkwHcRH3EWfFBV6GVd9fwb1zrNLumJAUtr     0  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb  6FKvsq4ydWFci6nGq9ckbjYMtnmaqAoatz5c9XWjiDuS    0.00  So11111111111111111111111111111111111111111  dda7447fa4530ccbf959fd3b9c011e95  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z            1  2021-11-27               2022\n", "2     2021-11-27 17:10:57+00:00  *********  2ojQaMYFi9yTwCxyxuUeS3RL6pRhYAidzqftkrMH6iAfXN44pLKkZ92yB2aHkex8nd2DW99YB4z2fKbCqY9FY1RZ     0  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb  6FKvsq4ydWFci6nGq9ckbjYMtnmaqAoatz5c9XWjiDuS    0.00  So11111111111111111111111111111111111111111  c7be1452031e7101712279c16c4de999  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z            2  2021-11-27               2022\n", "3     2021-11-27 17:11:04+00:00  108977902  5RXQ9HcM8sa1FT9p8wNaida7F3qk88hpNxrwBoUDByR7NhEVkZJepv3drg6WZCcfssttWRP4rRd874bwSiXFseH2     0  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb  6FKvsq4ydWFci6nGq9ckbjYMtnmaqAoatz5c9XWjiDuS    0.00  So11111111111111111111111111111111111111111  c32b661ab13a95894825ad8b4729c47c  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z            3  2021-11-27               2022\n", "4     2021-11-27 17:11:09+00:00  108977912   9fa72UUsD3cCLWZHwnRLhW6qfKwiJZDeDUxg5XnZwnKR5D61Y3CdzW97xDk3SagM3AUJ4LXDinXP1GEMvyTeUxZ     0  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb  6FKvsq4ydWFci6nGq9ckbjYMtnmaqAoatz5c9XWjiDuS    0.00  So11111111111111111111111111111111111111111  c05e0c68edc165cb04dc9acc286ad892  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z            4  2021-11-27               2022\n", "...                         ...        ...                                                                                       ...   ...                                          ...                                           ...     ...                                          ...                               ...                       ...                       ...          ...         ...                ...\n", "12976 2022-05-20 23:42:31+00:00  134585797  2oqXSmbPX9LF8HZabWEKcpGQaiffRHh4Vb363CR397UMNy91hZyctRKcoFqvgjZC5jgHNMHKhc4DDbnUDUiNVw6x     0  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb  6FKvsq4ydWFci6nGq9ckbjYMtnmaqAoatz5c9XWjiDuS    0.00  So11111111111111111111111111111111111111111  d74b0c5ecbc3a3d7b52594295f702185  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z        12976  2022-05-20               2022\n", "12977 2022-05-21 02:12:01+00:00  134598673  3LuF8eYEtkZgPHLENvirssHZoK5LhK6aagWZC8t9P7gEjtMBmDKgAPWCem9CjcDaSuGtgzFLoUVFYtmpAwm3MSbC     0  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb  6FKvsq4ydWFci6nGq9ckbjYMtnmaqAoatz5c9XWjiDuS    0.00  So11111111111111111111111111111111111111111  d25ebf892ab857835726c619fd41a189  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z        12977  2022-05-21               2022\n", "12978 2022-05-21 18:22:56+00:00  134686818  4Mf7dqsHEAGX8GYKvxmASdsbD6L5uuw6Ns7xvVKt9fcbfy4AbzHTyJ1w2n2GxnTS7SWVLaRZXx8bcdBdDq5MTyLD     0  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb  6FKvsq4ydWFci6nGq9ckbjYMtnmaqAoatz5c9XWjiDuS    0.00  So11111111111111111111111111111111111111111  349c53e3b19c31c6b0cc4b692e81b521  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z        12978  2022-05-21               2022\n", "12979 2022-05-21 18:23:40+00:00  134686877   Mvxft3kYhBxdyVaSyiYRUYtGtqZ9h1L3rFQL5PxDc1kciCzA7nDiykPPbwuBXa2dBZgT29CEyDrALmiAQcw5TUh     0  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb  6FKvsq4ydWFci6nGq9ckbjYMtnmaqAoatz5c9XWjiDuS    0.00  So11111111111111111111111111111111111111111  f04d9c73c8a65b3712015d9a751b8a7d  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z        12979  2022-05-21               2022\n", "12980 2022-05-21 20:52:56+00:00  134700224  5ppv816gmx9FgeWoCbsfoMcYyQFhbtwR44PFe1hxoJD4xwJjtujnEDeSLVZrC82Sr48Yy1QNakq2XMb2hgoASsTc     0  PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb  6FKvsq4ydWFci6nGq9ckbjYMtnmaqAoatz5c9XWjiDuS    0.00  So11111111111111111111111111111111111111111  62be62c4eef62a2eb54c2f982278ecee  2000-01-01T00:00:00.000Z  2000-01-01T00:00:00.000Z        12980  2022-05-21               2022\n", "\n", "[12981 rows x 14 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["airweave_fees = utils.query_df(f\"\"\"\n", "    SELECT\n", "        *\n", "    FROM\n", "        solana.core.fact_transfers\n", "    WHERE\n", "        tx_from = 'PUFFgnKKhQ23vp8uSPwdzrUhEr7WpLmjM85NB1FQgpb' and tx_to = '6FKvsq4ydWFci6nGq9ckbjYMtnmaqAoatz5c9XWjiDuS'\n", "    ORDER BY block_timestamp;\"\"\")\n", "\n", "airweave_fees"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.282898253"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["airweave_fees[\"amount\"].sum()"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"text/plain": ["304101.974236109"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["floor_sweeps.to_csv(\"sac_floor_sweeps.csv\")\n", "floor_sweeps[\"price_usd\"].sum()"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["found 1 rows\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>min(block_timestamp)</th>\n", "      <th>__row_index</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2021-02-14T03:54:02.000Z</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       min(block_timestamp)  __row_index\n", "0  2021-02-14T03:54:02.000Z            0"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# see first\n", "\n", "query_df(\"select min(block_timestamp) from solana.defi.ez_dex_swaps;\")"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["found 3247 rows\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3247 entries, 0 to 3246\n", "Data columns (total 21 columns):\n", " #   Column                Non-Null Count  Dtype              \n", "---  ------                --------------  -----              \n", " 0   swap_program          3247 non-null   object             \n", " 1   block_id              3247 non-null   int64              \n", " 2   block_timestamp       3247 non-null   datetime64[ns, UTC]\n", " 3   tx_id                 3247 non-null   object             \n", " 4   program_id            3247 non-null   object             \n", " 5   swapper               3247 non-null   object             \n", " 6   swap_from_mint        3247 non-null   object             \n", " 7   swap_from_symbol      3247 non-null   object             \n", " 8   swap_from_amount      3247 non-null   float64            \n", " 9   swap_from_amount_usd  3247 non-null   float64            \n", " 10  swap_to_mint          3247 non-null   object             \n", " 11  swap_to_symbol        2887 non-null   object             \n", " 12  swap_to_amount        3247 non-null   float64            \n", " 13  swap_to_amount_usd    2887 non-null   float64            \n", " 14  _log_id               3247 non-null   object             \n", " 15  inserted_timestamp    3247 non-null   object             \n", " 16  modified_timestamp    3247 non-null   object             \n", " 17  ez_swaps_id           3247 non-null   object             \n", " 18  __row_index           3247 non-null   int64              \n", " 19  day                   3247 non-null   object             \n", " 20  accounting_period     3247 non-null   int64              \n", "dtypes: datetime64[ns, UTC](1), float64(4), int64(3), object(13)\n", "memory usage: 532.8+ KB\n"]}], "source": ["all_big_buys = utils.query_df(f\"\"\"\n", "    SELECT\n", "        *\n", "    FROM\n", "        solana.defi.ez_dex_swaps\n", "    WHERE\n", "        swap_to_mint in ('{mint_list}')\n", "        and swap_from_amount_usd > 1000\n", "    ORDER BY\n", "        block_timestamp;\"\"\")\n", "all_big_buys.info()"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["all_big_buys  = utils.merge_company_wallets(all_big_buys, company_wallets, \"swapper\")\n", "all_big_buys = utils.move_columns_to_front(all_big_buys, [\"block_timestamp\", \"accounting_period\", \"tx_id\", \"by_sac\", \"swapper\", \"swap_from_symbol\", \"swap_to_symbol\", \"swap_from_amount_usd\", \"swap_to_amount\", \"swap_from_amount_usd\", \"swap_to_amount_usd\"])\n", "all_big_buys.to_csv(\"sac_big_buys.csv\")"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['block_timestamp', 'accounting_period', 'tx_id', 'by_sac', 'swapper',\n", "       'swap_from_symbol', 'swap_to_symbol', 'swap_from_amount_usd',\n", "       'swap_to_amount', 'swap_from_amount_usd', 'swap_to_amount_usd',\n", "       'swap_program', 'block_id', 'program_id', 'swap_from_mint',\n", "       'swap_from_amount', 'swap_to_mint', '_log_id', 'inserted_timestamp',\n", "       'modified_timestamp', 'ez_swaps_id', '__row_index', 'day', 'sac_pubkey',\n", "       'sac_name', 'sac_category', 'sac_tag', 'sac_Spalte 2', 'sac_Spalte 3',\n", "       'sac_Spalte 4'],\n", "      dtype='object')"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["all_big_buys.columns"]}], "metadata": {"deepnote_notebook_id": "e0ea8a814de143c7bafe54a8a3ffb5b9", "deepnote_persisted_session": {"createdAt": "2025-05-22T09:12:20.551Z"}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 0}