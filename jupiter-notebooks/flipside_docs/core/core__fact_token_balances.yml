version: 2
models:
  - name: core__fact_token_balances
    description: "{{ doc('token_balance_table_doc') }}"
    recent_date_filter: &recent_date_filter
      config:
        where: modified_timestamp >= current_date - 7
    tests:
      - reference_tx_missing:
          reference_tables:
            - 'silver__token_balances'
          id_column: 'tx_id'
    columns:
      - name: BLOCK_TIMESTAMP
        description: "{{ doc('block_timestamp') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: BLOCK_ID
        description: "{{ doc('block_id') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: TX_ID
        description: "{{ doc('tx_id') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: TX_INDEX
        description: "{{ doc('tx_index') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: SUCCEEDED
        description: "{{ doc('tx_succeeded') }}"
        tests: 
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: ACCOUNT_ADDRESS
        description: "{{ doc('balances_account') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: MINT
        description: "{{ doc('mint') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: OWNER
        description: "{{ doc('token_balances_block_owner') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: PRE_BALANCE
        description: "{{ doc('balances_pre_amount') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: BALANCE
        description: "{{ doc('balances_post_amount') }}" 
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: FACT_TOKEN_BALANCES_ID
        description: '{{ doc("pk") }}'
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
          - unique: *recent_date_filter    
      - name: INSERTED_TIMESTAMP
        description: '{{ doc("inserted_timestamp") }}'
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter    
      - name: MODIFIED_TIMESTAMP
        description: '{{ doc("modified_timestamp") }}' 
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter 
          