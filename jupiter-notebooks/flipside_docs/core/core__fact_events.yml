version: 2
models:
  - name: core__fact_events
    description: Contains each event that occurs on Solana. A transaction can consist of more than one event. 
    columns:
      - name: BLOCK_TIMESTAMP
        description: "{{ doc('block_timestamp') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: BLOCK_ID
        description: "{{ doc('block_id') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: TX_ID
        description: "{{ doc('tx_id') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: SIGNERS
        description: "{{ doc('signers') }}"
        tests: 
          - dbt_expectations.expect_column_to_exist
      - name: SUCCEEDED
        description: "{{ doc('tx_succeeded') }}"
        tests: 
          - dbt_expectations.expect_column_to_exist
      - name: INDEX
        description: "{{ doc('index') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: PROGRAM_ID
        description: "{{ doc('program_id') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: EVENT_TYPE
        description: "{{ doc('event_type') }}"
      - name: INSTRUCTION
        description: "{{ doc('instruction') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: INNER_INSTRUCTION
        description: "{{ doc('inner_instruction') }}" 
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: FACT_EVENTS_ID
        description: '{{ doc("pk") }}'   
      - name: INSERTED_TIMESTAMP
        description: '{{ doc("inserted_timestamp") }}'   
      - name: MODIFIED_TIMESTAMP
        description: '{{ doc("modified_timestamp") }}' 