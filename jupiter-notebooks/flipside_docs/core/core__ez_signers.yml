version: 2
models:
  - name: core__ez_signers
    description: A table that contains aggregate information about addresses that have signed transactions.
    columns:
      - name: SIGNER
        description: The address of the user that initiated the transaction
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: FIRST_TX_DATE
        description: The first date that the wallet performed a transaction on.
        tests: 
          - dbt_expectations.expect_column_to_exist
      - name: FIRST_PROGRAM_ID
        description: The ID of the first program this signer interacted with, excluding chain admin programs.
        tests: 
          - dbt_expectations.expect_column_to_exist
      - name: LAST_TX_DATE
        description: The date of the most recent transaction the signer has performed.
        tests: 
          - dbt_expectations.expect_column_to_exist
      - name: LAST_PROGRAM_ID
        description: The ID of the last program this signer interacted with, excluding chain admin programs.
        tests: 
          - dbt_expectations.expect_column_to_exist
      - name: NUM_DAYS_ACTIVE
        description: A count of the total number of unique days that this signer has performed a transaction.
        tests: 
          - dbt_expectations.expect_column_to_exist
      - name: NUM_TXS
        description: The total number of distinct transactions initiated by this signer. 
        tests: 
          - dbt_expectations.expect_column_to_exist
      - name: TOTAL_FEES
        description: The total amount of fees (in lamports) that the signer has paid on a given day. This field can be null, as only the first signer pays fees in a transaction.
        tests: 
          - dbt_expectations.expect_column_to_exist
      - name: PROGRAMS_USED
        description: An array containing all program IDs a user interacted with on a given day.
        tests: 
          - dbt_expectations.expect_column_to_exist
      - name: EZ_SIGNERS_ID
        description: '{{ doc("pk") }}'   
      - name: INSERTED_TIMESTAMP
        description: '{{ doc("inserted_timestamp") }}'   
      - name: MODIFIED_TIMESTAMP
        description: '{{ doc("modified_timestamp") }}' 
          