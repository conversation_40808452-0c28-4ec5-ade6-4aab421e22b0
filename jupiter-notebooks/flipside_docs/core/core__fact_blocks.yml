version: 2
models:
  - name: core__fact_blocks
    description: Contains general information about each block produced on Solana. 
    columns:
      - name: BLOCK_ID
        description: "{{ doc('block_id') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: BLOCK_TIMESTAMP
        description: "{{ doc('block_timestamp') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: NETWORK
        description: solana network name
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: CHAIN_ID
        description: chain identifier, this will always be solana.  Field is used in joins with crosschain tables
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: BLOCK_HEIGHT
        description: heigh of the block
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: BLOCK_HASH
        description: hash of the block
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: PREVIOUS_BLOCK_ID
        description: previous slot value
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: PREVIOUS_BLOCK_HASH
        description: previous block's hash value
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: FACT_BLOCKS_ID
        description: '{{ doc("pk") }}'   
      - name: INSERTED_TIMESTAMP
        description: '{{ doc("inserted_timestamp") }}'   
      - name: MODIFIED_TIMESTAMP
        description: '{{ doc("modified_timestamp") }}' 
