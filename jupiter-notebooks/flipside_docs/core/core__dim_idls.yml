version: 2
models:
  - name: core__dim_idls
    description: The status of Program IDL's submitted for decoding events 
    columns:
      - name: PROGRAM_ID
        description: "{{ doc('program_id') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: IDL
        description: "The complete submitted IDL that defines the program"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: IDL_HASH
        description: "The deployed hash of the program IDL"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: EARLIEST_DECODED_BLOCK
        description: "{{ doc('earliest_decoded_block') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: IS_VALID
        description: "{{ doc('is_valid') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: IS_ACTIVE
        description: "If the program has decoded instructions in the last 14 days"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: LAST_ACTIVITY_TIMESTAMP
        description: "Most recent date that the program has decoded instructions"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: SUBMITTED_BY
        description: "{{ doc('submitted_by') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: DATE_SUBMITTED
        description: "{{ doc('date_submitted') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: FIRST_BLOCK_ID
        description: "{{ doc('first_block_id') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: BACKFILL_STATUS
        description: "{{ doc('backfill_status') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: DIM_IDLS_ID
        description: "{{ doc('id') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: MODIFIED_TIMESTAMP
        description: "{{ doc('modified_timestamp') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: INSERTED_TIMESTAMP
        description: "{{ doc('inserted_timestamp') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
