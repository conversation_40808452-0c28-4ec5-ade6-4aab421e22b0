version: 2
models:
  - name: price__ez_asset_metadata
    description: '{{ doc("prices_ez_asset_metadata_table_doc") }}'
    recent_date_filter: &recent_date_filter
      config:
        where: modified_timestamp >= current_date - 7
    columns:
      - name: ASSET_ID
        description: '{{ doc("prices_asset_id") }}'
        data_tests: 
          - not_null: *recent_date_filter
      - name: NAME
        description: '{{ doc("prices_name") }}'
        data_tests: 
          - not_null: *recent_date_filter
      - name: SYMBOL
        description: '{{ doc("prices_symbol") }}'
        data_tests: 
          - not_null: *recent_date_filter
      - name: TOKEN_ADDRESS
        description: '{{ doc("prices_token_address") }}'
      - name: BLOCKCHAIN
        description: '{{ doc("prices_blockchain") }}'
        data_tests: 
          - not_null: *recent_date_filter
      - name: DECIMALS
        description: '{{ doc("prices_decimals") }}'
      - name: IS_NATIVE
        description: '{{ doc("prices_is_native") }}'
        data_tests: 
          - not_null: *recent_date_filter
      - name: IS_DEPRECATED
        description: '{{ doc("prices_is_deprecated") }}'
        data_tests: 
          - not_null: *recent_date_filter
      - name: EZ_ASSET_METADATA_ID
        description: '{{ doc("pk") }}'   
        data_tests: 
          - unique: *recent_date_filter
          - not_null: *recent_date_filter 
      - name: INSERTED_TIMESTAMP
        description: '{{ doc("inserted_timestamp") }}'  
        data_tests: 
          - not_null: *recent_date_filter 
      - name: MODIFIED_TIMESTAMP
        description: '{{ doc("modified_timestamp") }}' 
        data_tests: 
          - not_null
      