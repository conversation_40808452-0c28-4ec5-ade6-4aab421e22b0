version: 2
models:
  - name: price__ez_prices_hourly
    description: '{{ doc("prices_ez_prices_hourly_table_doc") }}'
    recent_date_filter: &recent_date_filter
      config:
        where: >
          modified_timestamp > current_date - 7
    columns:
      - name: HOUR
        description: '{{ doc("prices_hour")}}'
        data_tests: 
          - not_null: *recent_date_filter 
      - name: TOKEN_ADDRESS
        description: '{{ doc("prices_token_address") }}'
      - name: SYMBOL
        description: '{{ doc("prices_symbol") }}'
        data_tests: 
          - not_null: *recent_date_filter 
      - name: BLOCKCHAIN
        description: '{{ doc("prices_blockchain") }}'
        data_tests: 
          - not_null: *recent_date_filter 
      - name: DECIMALS
        description: '{{ doc("prices_decimals") }}'
      - name: PRICE
        description: '{{ doc("prices_price") }}'
        data_tests: 
          - not_null: *recent_date_filter 
      - name: IS_NATIVE
        description: '{{ doc("prices_is_native") }}'
        data_tests: 
          - not_null: *recent_date_filter 
      - name: IS_IMPUTED
        description: '{{ doc("prices_is_imputed") }}'
        data_tests: 
          - not_null: *recent_date_filter 
      - name: IS_DEPRECATED
        description: '{{ doc("prices_is_deprecated") }}'
        data_tests: 
          - not_null: *recent_date_filter 
      - name: EZ_PRICES_HOURLY_ID
        description: '{{ doc("pk") }}'   
        data_tests: 
          - unique: *recent_date_filter
          - not_null: *recent_date_filter 
      - name: INSERTED_TIMESTAMP
        description: '{{ doc("inserted_timestamp") }}'  
        data_tests: 
          - not_null: *recent_date_filter 
      - name: MODIFIED_TIMESTAMP
        description: '{{ doc("modified_timestamp") }}' 
        data_tests: 
          - not_null