version: 2
models:
  - name: gov__fact_votes_agg_block
    description: An aggregate table containing the number of automatic validator voting transactions that occurred during a block. 
    columns:
      - name: BLOCK_TIMESTAMP
        description: "{{ doc('block_timestamp') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - dbt_expectations.expect_row_values_to_have_recent_data:
              datepart: day
              interval: 2
      - name: BLOCK_ID
        description: "{{ doc('block_id') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: NUM_VOTES
        description: The number of vote events that occurred within the block
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: FACT_VOTES_AGG_BLOCK_ID
        description: '{{ doc("pk") }}'   
      - name: INSERTED_TIMESTAMP
        description: '{{ doc("inserted_timestamp") }}'   
      - name: MODIFIED_TIMESTAMP
        description: '{{ doc("modified_timestamp") }}' 