version: 2
models:
  - name: gov__fact_gauges_votes
    description: All gauge votes on <PERSON><PERSON> for <PERSON><PERSON> and <PERSON><PERSON>. Gauges allow voting escrows to allocate the rewards of a set of liquidity mining pools. 
    recent_date_filter: &recent_date_filter
      config:
        where: modified_timestamp >= current_date - 7
    columns:
      - name: PROGRAM_NAME
        description: "name of solana program"
        data_tests: 
          - not_null: *recent_date_filter 
      - name: BLOCK_TIMESTAMP
        description: "{{ doc('block_timestamp') }}"
        data_tests: 
          - not_null: *recent_date_filter 
      - name: BLOCK_ID
        description: "{{ doc('block_id') }}"
        data_tests: 
          - not_null: *recent_date_filter 
      - name: TX_ID
        description: "{{ doc('tx_id') }}"
        data_tests: 
          - not_null: *recent_date_filter 
      - name: SUCCEEDED
        description: "{{ doc('tx_succeeded') }}"
        data_tests: 
          - not_null: *recent_date_filter 
      - name: VOTER
        description: "{{ doc('tribeca_gauge_voter') }}"
        data_tests: 
          - not_null: *recent_date_filter 
      - name: VOTER_NFT
        description: "{{ doc('gov_action_locker_nft') }}"
        data_tests: 
          - not_null: *recent_date_filter 
      - name: GAUGE
        description: "{{ doc('tribeca_gauge') }}"
        data_tests: 
          - not_null: *recent_date_filter 
      - name: DELEGATED_SHARES
        description: "{{ doc('tribeca_gauge_delegated_shares') }}"
        data_tests: 
          - not_null: *recent_date_filter 
      - name: POWER
        description: "{{ doc('tribeca_gauge_power') }}"
        data_tests: 
          - not_null: *recent_date_filter 
      - name: FACT_GAUGES_VOTES_ID
        description: '{{ doc("pk") }}'   
        data_tests: 
          - unique: *recent_date_filter 
          - not_null: *recent_date_filter 
      - name: INSERTED_TIMESTAMP
        description: '{{ doc("inserted_timestamp") }}'   
        data_tests: 
          - not_null: *recent_date_filter 
      - name: MODIFIED_TIMESTAMP
        description: '{{ doc("modified_timestamp") }}' 
        data_tests: 
          - not_null