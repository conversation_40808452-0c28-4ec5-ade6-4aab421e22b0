version: 2
models:
  - name: gov__fact_proposal_votes
    description: All governance votes for Realms spaces and Marinade / Saber votes hosted on Tribeca. 
    columns:
      - name: GOVERNANCE_PLATFORM
        description: "platform used for governance space"
        tests:  
          - dbt_expectations.expect_column_to_exist
      - name: PROGRAM_NAME
        description: "name of solana program"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: BLOCK_TIMESTAMP
        description: "{{ doc('block_timestamp') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - dbt_expectations.expect_row_values_to_have_recent_data:
              datepart: day
              interval: 2 
      - name: BLOCK_ID
        description: "{{ doc('block_id') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: TX_ID
        description: "{{ doc('tx_id') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: SUCCEEDED
        description: "{{ doc('tx_succeeded') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: VOTER
        description: Address voting on the proposal
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: VOTER_ACCOUNT
        description: Account with locked tokens linked to the NFT (determines voting power)
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: VOTER_NFT
        description: NFT mint used in this vote on Marinade
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: PROPOSAL
        description: Address representing the proposal being voted on
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: REALMS_ID
        description: Address representing the voting group within Realms
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: VOTE_CHOICE
        description: The voting option selected by the user on a Realms proposal
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: VOTE_RANK
        description: The order which a user ranks their choices on a ranked vote on Realms
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: VOTE_WEIGHT
        description: Percentage of voting power put towards a voting option on Realms
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: FACT_PROPOSAL_VOTES_ID
        description: '{{ doc("pk") }}'   
      - name: INSERTED_TIMESTAMP
        description: '{{ doc("inserted_timestamp") }}'   
      - name: MODIFIED_TIMESTAMP
        description: '{{ doc("modified_timestamp") }}' 