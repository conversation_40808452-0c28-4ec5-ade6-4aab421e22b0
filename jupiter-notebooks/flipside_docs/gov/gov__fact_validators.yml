version: 2
models:
  - name: gov__fact_validators
    description: "This table contains validator data by epoch, sourced from the Validators.app API"
    recent_date_filter: &recent_date_filter
      config:
        where: modified_timestamp >= current_date - 7
    recent_date_filter_and_exclude_records: &recent_date_filter_and_exclude_records
      config:
        where: modified_timestamp >= current_date - 7 and NOT (node_pubkey = 'KtKiqBftjVcPR9WNi8dyoXoajj4xWtebf8enapgDv2Y' AND epoch in (666,667,668)) and NOT (node_pubkey = '2ViH5QVqCX3LjsCHBniMN2yfUEVc6W2cp9M6m6ZpVpxi' AND epoch = 667) and NOT (node_pubkey = 'isusUYC5gwsBpARY4Mf8wnFahMDVCtvPp91VBnR81yF' AND epoch = 668)
        # API response returned null values for these validators from epochs 666-668 so excluding from tests
    tests:
      - reference_tx_missing:
          reference_tables:
            - 'silver__snapshot_validators_app_data'
          id_column: 'node_pubkey'
    columns:
      - name: epoch
        description: "The epoch when data was recorded"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: node_pubkey
        description: "Account for the validator node"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: vote_pubkey
        description: "vote account for the validator"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: active_stake
        description: "Active stake in SOL delegated to the validator"
        tests:
          - dbt_expectations.expect_column_to_exist
          - null_threshold:
              threshold_percent: 0.99 # api returns null for some validators
              where: modified_timestamp >= current_date - 7
      - name: admin_warning
        description: "Admin warning for the validator"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: avatar_url
        description: "URL of avatar image"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: commission
        description: "% of rewards payout to the vote account"
        tests:
          - dbt_expectations.expect_column_to_exist
          - null_threshold:
              threshold_percent: 0.99 # api returns null for some validators
              where: modified_timestamp >= current_date - 7
      - name: created_at
        description: "date when validator was created"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: data_center_key
        description: "identifier for the data center"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: data_center_host
        description: "host for the data center"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: delinquent
        description: "Status whether the validator is offline/delinquent"
        tests:
          - dbt_expectations.expect_column_to_exist
          - null_threshold:
              threshold_percent: 0.99 # api returns null for some validators
              where: modified_timestamp >= current_date - 7
      - name: details
        description: "Details for the validator"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: epoch_active
        description: "last epoch when vote account was active"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: epoch_credits
        description: "Epoch credits for the validator"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: keybase_id
        description: "Keybase ID for the validator"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: latitude
        description: "Latitude coordinates of data center"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: longitude
        description: "Longitude coordinates of data center"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: validator_name
        description: "Name of the validator"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: software_version
        description: "Solana mainnet version"
        tests:
          - dbt_expectations.expect_column_to_exist
          - null_threshold:
              threshold_percent: 0.99 # api returns null for some validators
              where: modified_timestamp >= current_date - 7
      - name: updated_at
        description: "Last date validator was updated"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: www_url
        description: "url for the validator"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: FACT_VALIDATORS_ID
        description: '{{ doc("pk") }}'
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
          - unique: *recent_date_filter
      - name: INSERTED_TIMESTAMP
        description: '{{ doc("inserted_timestamp") }}'
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter 
      - name: MODIFIED_TIMESTAMP
        description: '{{ doc("modified_timestamp") }}'
        tests:
          - dbt_expectations.expect_column_to_exist