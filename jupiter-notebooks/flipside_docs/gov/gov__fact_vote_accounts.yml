version: 2
models:
  - name: gov__fact_vote_accounts
    description: "This table contains data for each vote account by epoch, including the validator's public key, voting weight, commission rate, and related information."
    recent_date_filter: &recent_date_filter
      config:
        where: modified_timestamp >= current_date - 7
    tests:
      - reference_tx_missing:
          reference_tables:
            - 'silver__snapshot_vote_accounts'
          id_column: 'vote_pubkey'
    columns:
      - name: epoch
        description: "Epoch when data was captured"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: authorized_voter
        description: "Account responsible for signing vote transactions"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null:
              where: (modified_timestamp >= current_date - 7) and authorized_withdrawer <> '11111111111111111111111111111111'
      - name: last_epoch_active
        description: "last epoch when vote account was active"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: authorized_withdrawer
        description: "Account responsible for signing stake withdrawal transactions"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: commission
        description: "% of rewards payout to the vote account"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: epoch_credits
        description: "Credits earned by the end of epochs, containing epoch/credits/previous credits"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: last_timestamp_slot
        description: "Last slot voted on"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: last_timestamp
        description: "The timestamp when last slot was voted on"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: node_pubkey
        description: "pubkey for the Solana validator node"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: prior_voters
        description: "Prior voters for the vote account"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: root_slot
        description: "latest slot confirmed"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: votes
        description: "Votes during epoch"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: account_sol
        description: "SOL assigned to this account"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: owner
        description: "Program account that owns the vote account"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: rent_epoch
        description: "Epoch at which this account will next owe rent"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: vote_pubkey
        description: "Public key for the vote account"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: FACT_VOTE_ACCOUNTS_ID
        description: '{{ doc("pk") }}'
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
          - unique: *recent_date_filter  
      - name: INSERTED_TIMESTAMP
        description: '{{ doc("inserted_timestamp") }}'
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter   
      - name: MODIFIED_TIMESTAMP
        description: '{{ doc("modified_timestamp") }}'
        tests:
          - dbt_expectations.expect_column_to_exist