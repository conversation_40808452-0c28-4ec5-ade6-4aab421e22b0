version: 2
models:
  - name: gov__ez_staking_lp_actions
    description: EZ table for staking & LP actions that contains additional information about the validator. 
    recent_date_filter: &recent_date_filter
      config:
        where: modified_timestamp >= current_date - 7
    columns:
      - name: BLOCK_TIMESTAMP
        description: "{{ doc('block_timestamp') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - dbt_expectations.expect_row_values_to_have_recent_data:
              datepart: day
              interval: 2
      - name: BLOCK_ID
        description: "{{ doc('block_id') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: TX_ID
        description: "{{ doc('tx_id') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: INDEX
        description: "{{ doc('index') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: INNER_INDEX
        description: "{{ doc('inner_index') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: SUCCEEDED
        description: "{{ doc('tx_succeeded') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: EVENT_TYPE
        description: "{{ doc('event_type') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: SIGNERS
        description: List of accounts that signed the transaction
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: STAKE_AUTHORITY
        description: The wallet address of the user who initialized the transaction. 
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: STAKE_ACCOUNT
        description: An account address containing balances of staked SOL that belongs to the stake authority. 
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: PRE_TX_STAKED_BALANCE
        description: The amount of Solana belonging to the stake account before the transaction.
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: POST_TX_STAKED_BALANCE
        description: The amount of Solana belonging to the stake account after the transaction.
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: MOVE_AMOUNT
        description: The amount of SOL being moved from the stake account.
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: MOVE_DESTINATION
        description: The destination wallet address of the moved SOL.
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: VOTE_ACCOUNT
        description: A voting account belonging to the validator. 
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: NODE_PUBKEY
        description: A unique key belonging to the validator node. 
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: VALIDATOR_RANK
        description: The rank of the validator by amount of delegated SOL. 
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: COMMISSION
        description: The percentage of staked earnings given to the validator. 
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: EZ_STAKING_LP_ACTIONS_ID
        description: '{{ doc("pk") }}'   
      - name: INSERTED_TIMESTAMP
        description: '{{ doc("inserted_timestamp") }}'   
      - name: MODIFIED_TIMESTAMP
        description: '{{ doc("modified_timestamp") }}' 