version: 2
models:
  - name: gov__fact_gauges_creates
    description: Creation events for gauges on <PERSON><PERSON> and <PERSON><PERSON>. Gauges allow voting escrows to allocate the rewards of a set of liquidity mining pools. 
    columns:
      - name: PROGRAM_NAME
        description: "name of solana program"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: BLOCK_TIMESTAMP
        description: "{{ doc('block_timestamp') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: BLOCK_ID
        description: "{{ doc('block_id') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: TX_ID
        description: "{{ doc('tx_id') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: SUCCEEDED
        description: "{{ doc('tx_succeeded') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: SIGNER
        description: "{{ doc('gov_action_signer') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: GAUGE
        description: "{{ doc('tribeca_gauge') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: GAUGEMEISTER
        description: "{{ doc('tribeca_gaugemeister') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: VALIDATOR_ACCOUNT
        description: "{{ doc('tribeca_gauge_validator_account') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: FACT_GAUGES_CREATES_ID
        description: '{{ doc("pk") }}'   
      - name: INSERTED_TIMESTAMP
        description: '{{ doc("inserted_timestamp") }}'   
      - name: MODIFIED_TIMESTAMP
        description: '{{ doc("modified_timestamp") }}' 