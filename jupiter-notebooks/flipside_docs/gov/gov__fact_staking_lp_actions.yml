version: 2
models:
  - name: gov__fact_staking_lp_actions
    description: Staking and liquidity provider actions that take place on the Solana blockchain. 
    columns:
      - name: BLOCK_TIMESTAMP
        description: "{{ doc('block_timestamp') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: BLOCK_ID
        description: "{{ doc('block_id') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: TX_ID
        description: "{{ doc('tx_id') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: INDEX
        description: "{{ doc('index') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: INNER_INDEX
        description: "{{ doc('inner_index') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: SUCCEEDED
        description: "{{ doc('tx_succeeded') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: PROGRAM_ID
        description: "{{ doc('program_id') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: EVENT_TYPE
        description: "{{ doc('event_type') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: SIGNERS
        description: List of accounts that signed the transaction
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: ACCOUNT_KEYS
        description: List of accounts that are referenced by pre/post sol/token balances objects
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: INSTRUCTION
        description: "{{ doc('instruction') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: INNER_INSTRUCTION
        description: "{{ doc('inner_instruction') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: PRE_BALANCES
        description: List of pre-transaction balances for different accounts
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: POST_BALANCES
        description: List of post-transaction balances for different accounts
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: PRE_TOKEN_BALANCES
        description: List of pre-transaction token balances for different token accounts
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: POST_TOKEN_BALANCES
        description: List of post-transaction token balances for different token accounts
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: FACT_STAKING_LP_ACTIONS_ID
        description: '{{ doc("pk") }}'   
      - name: INSERTED_TIMESTAMP
        description: '{{ doc("inserted_timestamp") }}'   
      - name: MODIFIED_TIMESTAMP
        description: '{{ doc("modified_timestamp") }}' 