version: 2
models:
  - name: gov__fact_proposal_creation
    description: An EZ view of all proposal creations. Currently, this table is limited to only proposal creations on Realms DAO governance spaces. A space serves as a voting group for an organization, dAPP, or DAO.  
    recent_date_filter: &recent_date_filter
      config:
        where: modified_timestamp >= current_date - 7
    columns:
      - name: GOVERNANCE_PLATFORM
        description: "platform used for governance space"
        data_tests: 
          - not_null: *recent_date_filter 
      - name: BLOCK_TIMESTAMP
        description: "{{ doc('block_timestamp') }}"
        data_tests: 
          - not_null: *recent_date_filter 
      - name: BLOCK_ID
        description: "{{ doc('block_id') }}"
        data_tests: 
          - not_null: *recent_date_filter 
      - name: TX_ID
        description: "{{ doc('tx_id') }}"
        data_tests: 
          - not_null: *recent_date_filter 
          - dbt_expectations.expect_column_to_exist
      - name: SUCCEEDED
        description: "{{ doc('tx_succeeded') }}"
        data_tests: 
          - not_null: *recent_date_filter 
      - name: PROGRAM_NAME
        description: "{{ doc('program_id') }}"
        data_tests: 
          - not_null: *recent_date_filter 
      - name: REALMS_ID 
        description: An address that is unique to the space or voting group on Realms. 
        data_tests: 
          - not_null: *recent_date_filter 
      - name: PROPOSAL
        description: Address representing the proposal being voted on.
        data_tests: 
          - not_null: *recent_date_filter 
      - name: PROPOSAL_WRITER
        description: Address of the user who is submitting the proposal for voting. 
        data_tests: 
          - not_null: *recent_date_filter 
      - name: PROPOSAL_NAME
        description: The name of the proposal that is being submitted
        data_tests: 
          - not_null: *recent_date_filter 
      - name: VOTE_TYPE
        description: The type of voting strategy that will be used for voting on the proposal. 
        data_tests: 
          - not_null: *recent_date_filter 
      - name: VOTE_OPTIONS
        description: The options that will be available to users who are voting on the proposal 
        data_tests: 
          - not_null: *recent_date_filter 
      - name: FACT_PROPOSAL_CREATION_ID
        description: '{{ doc("pk") }}' 
        data_tests: 
          - unique: *recent_date_filter  
          - not_null: *recent_date_filter    
      - name: INSERTED_TIMESTAMP
        description: '{{ doc("inserted_timestamp") }}'   
        data_tests: 
          - not_null: *recent_date_filter 
      - name: MODIFIED_TIMESTAMP
        description: '{{ doc("modified_timestamp") }}' 
        data_tests: 
          - not_null