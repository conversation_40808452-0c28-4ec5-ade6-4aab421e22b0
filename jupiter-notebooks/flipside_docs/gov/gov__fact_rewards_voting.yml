version: 2
models:
  - name: gov__fact_rewards_voting
    description: <PERSON><PERSON><PERSON> recieved by vote accounts/validators. These are sent once in the beginning of an epoch, for rewards earned by activity in the previous epoch.
    columns:
      - name: BLOCK_ID
        description: "{{ doc('block_id') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: BLOCK_TIMESTAMP
        description: "{{ doc('block_timestamp') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - dbt_expectations.expect_row_values_to_have_recent_data:
              datepart: day
              interval: 2 
      - name: REWARD_AMOUNT_SOL
        description: "{{ doc('amount') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: POST_BALANCE_SOL
        description: "{{ doc('post_balance') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: VOTE_PUBKEY
        description: "{{ doc('vote_pubkey') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: EPOCH_EARNED
        description: "{{ doc('epoch_earned') }}"
        tests: 
          - dbt_expectations.expect_column_to_exist
      - name: FACT_REWARDS_VOTING_ID
        description: "{{ doc('id') }}"
        tests: 
          - dbt_expectations.expect_column_to_exist
      - name: MODIFIED_TIMESTAMP
        description: "{{ doc('modified_timestamp') }}"
        tests: 
          - dbt_expectations.expect_column_to_exist
      - name: INSERTED_TIMESTAMP
        description: "{{ doc('inserted_timestamp') }}"
        tests: 
          - dbt_expectations.expect_column_to_exist
      - name: DIM_EPOCH_ID
        description: "Join key to the dim_epoch table"
        tests: 
          - dbt_expectations.expect_column_to_exist