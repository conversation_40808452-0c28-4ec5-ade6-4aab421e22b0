version: 2
models:
  - name: gov__fact_gov_actions
    description: All governance votes for Realms spaces and Marinade / Saber votes hosted on Tribeca. 
    recent_date_filter: &recent_date_filter
      config:
        where: modified_timestamp >= current_date - 7
    columns:
      - name: PROGRAM_NAME
        description: "name of solana program"
        data_tests:
          - not_null: *recent_date_filter 
      - name: BLOCK_TIMESTAMP
        description: "{{ doc('block_timestamp') }}"
        data_tests:
          - not_null: *recent_date_filter 
      - name: BLOCK_ID
        description: "{{ doc('block_id') }}"
        data_tests:
          - not_null: *recent_date_filter 
      - name: TX_ID
        description: "{{ doc('tx_id') }}"
        data_tests:
          - not_null: *recent_date_filter 
      - name: SUCCEEDED
        description: "{{ doc('tx_succeeded') }}"
        data_tests:
          - not_null: *recent_date_filter 
      - name: SIGNER
        description: "{{ doc('gov_action_signer') }}"
        data_tests:
          - not_null: *recent_date_filter 
      - name: LOCKER_ACCOUNT
        description: "{{ doc('gov_action_locker_account') }}"
      - name: LOCKER_NFT
        description: "{{ doc('gov_action_locker_nft') }}"
      - name: MINT
        description: "{{ doc('gov_action_mint') }}"
      - name: ACTION
        description: "{{ doc('gov_action_action') }}"
        data_tests:
          - not_null: *recent_date_filter 
      - name: AMOUNT
        description: "{{ doc('gov_action_amount') }}"
      - name: FACT_GOV_ACTIONS_ID
        description: '{{ doc("pk") }}'
        data_tests: 
          - unique: *recent_date_filter   
          - not_null: *recent_date_filter 
      - name: INSERTED_TIMESTAMP
        description: '{{ doc("inserted_timestamp") }}'   
        data_tests: 
          - not_null: *recent_date_filter 
      - name: MODIFIED_TIMESTAMP
        description: '{{ doc("modified_timestamp") }}' 
        data_tests: 
          - not_null