version: 2
models:
  - name: nft__fact_nft_mints
    description: An easy table containing information about Solana NFT mints including the purchaser, mint price, and NFT minted. 
    recent_date_filter: &recent_date_filter
      config:
        where: modified_timestamp >= current_date - 7
    columns:
      - name: BLOCK_TIMESTAMP
        description: "{{ doc('block_timestamp') }}"
        data_tests: 
          - not_null: *recent_date_filter 
      - name: BLOCK_ID
        description: "{{ doc('block_id') }}"
        data_tests: 
          - not_null: *recent_date_filter 
      - name: TX_ID
        description: "{{ doc('tx_id') }}"
        data_tests: 
          - not_null: *recent_date_filter 
      - name: SUCCEEDED
        description: "{{ doc('tx_succeeded') }}"
        data_tests: 
          - not_null: *recent_date_filter 
      - name: PROGRAM_ID 
        description: "{{ doc('program_id') }}"
        data_tests: 
          - not_null:
              where: mint_price is not null and modified_timestamp >= current_date - 7
      - name: PURCHASER
        description: "{{ doc('purchaser') }}"
        data_tests: 
          - not_null:
              where: mint_price is not null and modified_timestamp >= current_date - 7
      - name: MINT_PRICE
        description: "{{ doc('mint_price') }}"
      - name: MINT_CURRENCY
        description: "{{ doc('mint_currency') }}"
        data_tests: 
          - not_null:
              where: mint_price is not null and modified_timestamp >= current_date - 7
      - name: MINT
        description: "{{ doc('mint') }}"
        data_tests: 
          - not_null: *recent_date_filter 
      - name: IS_COMPRESSED
        description: "Identifies whether the mint is a compressed NFT mint"
        data_tests: 
          - not_null: *recent_date_filter 
      - name: FACT_NFT_MINTS_ID
        description: '{{ doc("pk") }}'
        data_tests: 
          - unique: *recent_date_filter   
          - not_null: *recent_date_filter 
      - name: INSERTED_TIMESTAMP
        description: '{{ doc("inserted_timestamp") }}'   
        data_tests: 
          - not_null: *recent_date_filter 
      - name: MODIFIED_TIMESTAMP
        description: '{{ doc("modified_timestamp") }}' 
        data_tests: 
          - not_null