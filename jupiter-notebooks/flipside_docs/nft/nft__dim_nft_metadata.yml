version: 2
models:
  - name: nft__dim_nft_metadata
    description: Contains NFT metadata sourced from Solscan and Helius API. 
    columns:
      - name: MINT
        description: "{{ doc('mint') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: COLLECTION_ID
        description: "The address of the NFT collection"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: CREATORS
        description:  "Creators of the NFT and what percentage of royalties they receive"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: AUTHORITY
        description: "Authority address for the mint. When editions are minted, the authority remains the one from the master NFT"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: IMAGE_URL
        description: "{{ doc('image_url') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: NFT_NAME
        description: "The name of the NFT"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: NFT_COLLECTION_NAME
        description: "The name of the collection provided by Solscan"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: METADATA
        description: "{{ doc('token_metadata') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: METADATA_URI
        description: "{{ doc('token_metadata_uri') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: DIM_NFT_METADATA_ID
        description: "{{ doc('pk') }}"
        tests: 
          - dbt_expectations.expect_column_to_exist
      - name: MODIFIED_TIMESTAMP
        description: "{{ doc('modified_timestamp') }}"
        tests: 
          - dbt_expectations.expect_column_to_exist
      - name: INSERTED_TIMESTAMP
        description: "{{ doc('inserted_timestamp') }}"
        tests: 
          - dbt_expectations.expect_column_to_exist