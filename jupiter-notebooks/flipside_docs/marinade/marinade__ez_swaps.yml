version: 2
models:
  - name: marinade__ez_swaps
    description: A convenience table containing swaps involving mSOL and MNDE tokens.
    recent_date_filter: &recent_date_filter
      config:
        where: block_timestamp >= current_date - 7
    recent_modified_date_filter: &recent_modified_date_filter
      config:
        where: modified_timestamp >= current_date - 7
    columns:
      - name: BLOCK_TIMESTAMP
        description: "{{ doc('block_timestamp') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_modified_date_filter
      - name: BLOCK_ID
        description: "{{ doc('block_id') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: TX_ID
        description: "{{ doc('tx_id') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: SWAPPER
        description: Address that initiated the swap 
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: SWAP_FROM_AMOUNT
        description: Total amount of the token sent in to initiate the swap 
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: SWAP_FROM_MINT
        description: Token being sent or swapped from
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: SWAP_FROM_AMOUNT_USD
        description: The amount of tokens put into the swap converted to USD using the price of the token 
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: SWAP_FROM_SYMBOL
        description: The symbol of the token being swapped from
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: SWAP_TO_AMOUNT
        description: Total amount of the token received in the swap
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: SWAP_TO_MINT
        description: Token being received or swapped for 
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: SWAP_TO_AMOUNT_USD
        description: The amount of tokens taken out of or received from the swap converted to USD using the price of the token
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: SWAP_TO_SYMBOL
        description: The symbol of the token being swapped to
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: PROGRAM_ID
        description: "{{ doc('program_id') }}" 
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: PLATFORM
        description: name of decentralized exchange used to perform the swap
        tests:
          - not_null: *recent_date_filter
      - name: MARINADE_EZ_SWAPS_ID
        description: '{{ doc("pk") }}' 
        tests:
          - not_null: *recent_date_filter
          - unique: *recent_date_filter    
      - name: INSERTED_TIMESTAMP
        description: '{{ doc("inserted_timestamp") }}'
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter    
      - name: MODIFIED_TIMESTAMP
        description: '{{ doc("modified_timestamp") }}'
        tests:
          - dbt_expectations.expect_column_to_exist