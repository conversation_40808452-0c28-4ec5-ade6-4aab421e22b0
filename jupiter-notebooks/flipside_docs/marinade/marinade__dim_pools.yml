version: 2
models:
  - name: marinade__dim_pools
    description: Dimensional table containing information on liquidity pools that involve MNDE or MSOL tokens as one of the liquidity pairs.
    columns:
      - name: POOL_ADDRESS
        description: "{{ doc('liquidity_pool_address') }}"
        data_tests: 
          - not_null
          - unique
      - name: POOL_NAME
        description: >
          Name of the liquidity pool
        data_tests: 
          - not_null:
              config:
                where: >
                  token_a_symbol IS NOT NULL
                  AND token_b_symbol IS NOT NULL
      - name: POOL_TOKEN_MINT
        description: "{{ doc('liquidity_pool_token_mint') }}"
      - name: TOKEN_A_MINT
        description:  "{{ doc('token_a_mint') }}"
        data_tests: 
          - not_null
      - name: TOKEN_A_SYMBOL
        description:  "{{ doc('prices_symbol') }}"
      - name: TOKEN_A_ACCOUNT
        description:  "{{ doc('token_a_account') }}"
        data_tests: 
          - not_null
      - name: TOKEN_B_MINT
        description:  "{{ doc('token_b_mint') }}"
        data_tests: 
          - not_null
      - name: TOKEN_B_SYMBOL
        description:  "{{ doc('prices_symbol') }}"
      - name: TOKEN_B_ACCOUNT
        description:  "{{ doc('token_b_account') }}"
        data_tests: 
          - not_null
      - name: INITIALIZED_AT_BLOCK_TIMESTAMP
        description: "{{ doc('block_timestamp') }}"
        data_tests:
          - not_null
      - name: INITIALIZED_AT_BLOCK_ID
        description: "{{ doc('block_id') }}"
        data_tests:
          - not_null
      - name: INITIALIZED_AT_TX_ID
        description: "{{ doc('tx_id') }}"
        data_tests:
          - not_null
      - name: INITIALIZED_AT_INDEX
        description: "{{ doc('event_index') }}"
        data_tests: 
          - not_null
      - name: INITIALIZED_AT_INNER_INDEX
        description: "{{ doc('inner_index') }}"
      - name: IS_MSOL_POOL
        description: >
          Whether the pool has MSOL as one of the tokens
        data_tests: 
          - not_null
      - name: IS_MNDE_POOL
        description: >
          Whether the pool has MNDE as one of the tokens
        data_tests: 
          - not_null
      - name: PROGRAM_ID
        description: "{{ doc('program_id') }}"
        data_tests: 
          - not_null
      - name: PLATFORM
        description: >
          Name of the liquidity pool protocol
        data_tests: 
          - not_null
      - name: DIM_POOLS_ID
        description: '{{ doc("pk") }}'   
        data_tests: 
          - unique
      - name: INSERTED_TIMESTAMP
        description: '{{ doc("inserted_timestamp") }}'   
        data_tests: 
          - not_null
      - name: MODIFIED_TIMESTAMP
        description: '{{ doc("modified_timestamp") }}' 
        data_tests: 
          - not_null
