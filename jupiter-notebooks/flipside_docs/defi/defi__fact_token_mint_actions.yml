version: 2
models:
  - name: defi__fact_token_mint_actions
    description: Contains information on all mint events for tokens
    columns:
      - name: BLOCK_ID
        description: "{{ doc('block_id') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: BLOCK_TIMESTAMP
        description: "{{ doc('block_timestamp') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: TX_ID
        description: "{{ doc('tx_id') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: SUCCEEDED
        description: "{{ doc('tx_succeeded') }}"
        tests: 
          - dbt_expectations.expect_column_to_exist
      - name: INDEX
        description: Location of the event within the instructions of a transaction
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: INNER_INDEX
        description: Location of the event within the inner instructions of a transaction
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: EVENT_TYPE
        description: "{{ doc('event_type') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: MINT
        description: "{{ doc('mint') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: TOKEN_ACCOUNT
        description: The account address where tokens are minted to
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: MINT_AMOUNT
        description: "{{ doc('mint_amount') }}"
        tests: 
          - dbt_expectations.expect_column_to_exist
      - name: MINT_AUTHORITY
        description: The address of account authorizing the mint
        tests: 
          - dbt_expectations.expect_column_to_exist
      - name: SIGNERS
        description: "{{ doc('signers') }}"
        tests: 
          - dbt_expectations.expect_column_to_exist
      - name: DECIMAL
        description: "{{ doc('decimal') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: MINT_STANDARD_TYPE
        description: "{{ doc('mint_standard_type') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: FACT_TOKEN_MINT_ACTIONS_ID
        description: '{{ doc("pk") }}'   
      - name: INSERTED_TIMESTAMP
        description: '{{ doc("inserted_timestamp") }}'   
      - name: MODIFIED_TIMESTAMP
        description: '{{ doc("modified_timestamp") }}' 
          