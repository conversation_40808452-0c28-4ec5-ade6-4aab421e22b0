version: 2
models:
  - name: defi__fact_swaps_jupiter_summary
    description: This table contains summarized swaps for trades initiated on Jupiter. This showcases the the initial amount/mint that is swapped from and the final amount/mint that is swapped to, without the intermediate swaps that <PERSON> routes through. This also includes columns for DCA (dollar cost averaging) information to identify the initial requester of the DCA swaps. Jupiter V4 swaps before 2023-10-31 are aggregated at the transaction level, so these contain NULL index and inner_index values, and default to 0 for swap_index. This documentation [guide](https://docs.google.com/document/d/1gxU7Q8BNf2w6xsDIczfxxOFMPM0ujQSJTzwGPDkZTVU/edit?tab=t.0) and [video](https://www.loom.com/share/f20d1f54b63342eea6457381be7175a1?sid=589f65ec-505a-446c-b249-19e6a9b9a6fb) explain how Flipside models Solana swaps, why we split the data, and how to use each table effectively.
    recent_date_filter: &recent_date_filter
      config:
        where: modified_timestamp >= current_date - 7
    tests:
      - reference_tx_missing:
          reference_tables:
            - 'silver__swaps_intermediate_jupiterv6_2'
            - 'silver__swaps_intermediate_jupiterv4_2'
          id_column: 'tx_id'
    columns:
      - name: BLOCK_TIMESTAMP
        description: "{{ doc('block_timestamp') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: BLOCK_ID
        description: "{{ doc('block_id') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: TX_ID
        description: "{{ doc('tx_id') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: INDEX
        description: "{{ doc('event_index') }}. Jupiter V4 swaps prior to 2023-10-31 are NULL"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null:
              where: modified_timestamp >= current_date - 7 AND (program_id <> 'JUP4Fb2cqiRUcaTHdrPC8h2gNsA2ETXiPDD33WcGuJB' AND block_timestamp::date >= '2023-10-31')
      - name: INNER_INDEX
        description: "{{ doc('inner_index') }}. This is the inner index of the log event listing the inner swap. This value is null for Jupiter v5 swaps (which do not have swaps within the inner instruction), as well as Jupiter v4 swaps prior to 2023-10-31'"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: SWAP_INDEX
        description: "{{ doc('swaps_swap_index') }} as it relates to the top level Jupiter swap instruction. This value is 0 for Jupiter v4 swaps prior to 2023-10-31'"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: SUCCEEDED
        description: "{{ doc('tx_succeeded') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: PROGRAM_ID
        description: "{{ doc('program_id') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: SWAPPER
        description: "{{ doc('swaps_swapper') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: SWAP_FROM_AMOUNT
        description:  "{{ doc('swaps_from_amt') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: SWAP_FROM_MINT
        description:  "{{ doc('swaps_from_mint') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: SWAP_TO_AMOUNT
        description:  "{{ doc('swaps_to_amt') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: SWAP_TO_MINT
        description:  "{{ doc('swaps_to_mint') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: IS_DCA_SWAP
        description: "Whether the swap was initiated by a Jupiter DCA. If value is NULL then it is NOT a DCA Swap"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: DCA_REQUESTER
        description: "Original address that requested the DCA swap"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: IS_LIMIT_SWAP
        description: "Whether the swap was initiated by a Jupiter limit order. If value is NULL then it is NOT a limit order swap"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: LIMIT_REQUESTER
        description: "Original address that requested the limit order swap"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: FACT_SWAPS_JUPITER_SUMMARY_ID
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
          - unique: *recent_date_filter   
      - name: INSERTED_TIMESTAMP
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter    
      - name: MODIFIED_TIMESTAMP
        description: '{{ doc("modified_timestamp") }}' 
        tests:
          - dbt_expectations.expect_column_to_exist
