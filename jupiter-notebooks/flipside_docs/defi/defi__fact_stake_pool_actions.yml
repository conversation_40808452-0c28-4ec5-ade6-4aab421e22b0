version: 2
models:
  - name: defi__fact_stake_pool_actions
    description: Deposit and withdraw actions with a given stake pool
    recent_date_filter: &recent_date_filter
      config:
        where: modified_timestamp >= current_date - 7
    tests:
      - reference_tx_missing:
          reference_tables:
            - 'silver__stake_pool_actions_lido'
            - 'silver__stake_pool_actions_generic'
            - 'silver__stake_pool_actions_marinade'
            - 'silver__stake_pool_actions_jito'
          id_column: 'tx_id'
    columns:
      - name: STAKE_POOL_NAME
        description: "Name of stake pool action is performed against"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter 
      - name: BLOCK_TIMESTAMP
        description: "{{ doc('block_timestamp') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter 
      - name: BLOCK_ID
        description: "{{ doc('block_id') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter 
      - name: TX_ID
        description: "{{ doc('tx_id') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter 
      - name: INDEX
        description: Location of the stake pool action within a transaction
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter 
      - name: SUCCEEDED
        description: "{{ doc('tx_succeeded') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter 
      - name: ACTION
        description: "{{ doc('stake_pool_action') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter 
      - name: STAKE_POOL
        description: "{{ doc('stake_pool') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter 
      - name: ADDRESS
        description: "{{ doc('stake_pool_address') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter 
      - name: AMOUNT
        description: "Amount in Lamports"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null:
              config:
                where: >
                  modified_timestamp >= current_date - 7
                  AND succeeded
      - name: TOKEN
        description: "Token utilized in the stake pool action"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter 
      - name: FACT_STAKE_POOL_ACTIONS_ID
        description: '{{ doc("pk") }}'
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
          - unique: *recent_date_filter    
      - name: INSERTED_TIMESTAMP
        description: '{{ doc("inserted_timestamp") }}' 
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter   
      - name: MODIFIED_TIMESTAMP
        description: '{{ doc("modified_timestamp") }}' 
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter 