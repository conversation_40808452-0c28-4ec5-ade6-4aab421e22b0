version: 2
models:
  - name: defi__fact_swaps_jupiter_inner
    description: This table contain each intermediate swap that is a part of a Jupiter route. These are the individual steps that are executed by <PERSON> to complete a swap. This documentation [guide](https://docs.google.com/document/d/1gxU7Q8BNf2w6xsDIczfxxOFMPM0ujQSJTzwGPDkZTVU/edit?tab=t.0) and [video](https://www.loom.com/share/f20d1f54b63342eea6457381be7175a1?sid=589f65ec-505a-446c-b249-19e6a9b9a6fb) explain how Flipside models Solana swaps, why we split the data, and how to use each table effectively.
    recent_date_filter: &recent_date_filter
      config:
        where: modified_timestamp >= current_date - 7
    tests:
      - reference_tx_missing:
          reference_tables:
            - 'silver__swaps_inner_intermediate_jupiterv6'
            - 'silver__swaps_inner_intermediate_jupiterv4'
          id_column: 'tx_id'
    columns:
      - name: BLOCK_TIMESTAMP
        description: "{{ doc('block_timestamp') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: BLOCK_ID
        description: "{{ doc('block_id') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: TX_ID
        description: "{{ doc('tx_id') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: INDEX
        description: "{{ doc('event_index') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: INNER_INDEX
        description: "{{ doc('inner_index') }}. This is the inner index of the log event listing the inner swap"
        tests:
          - dbt_expectations.expect_column_to_exist
      - name: SWAP_INDEX
        description: "{{ doc('swaps_swap_index') }} as it relates to the top level Jupiter swap instruction"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: SUCCEEDED
        description: "{{ doc('tx_succeeded') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: SWAP_PROGRAM_ID
        description: "{{ doc('program_id') }}. This is the AMM performing the swap."
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: AGGREGATOR_PROGRAM_ID
        description: "{{ doc('program_id') }}. This is the aggregator calling the different AMMs."
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: SWAPPER
        description: "{{ doc('swaps_swapper') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: SWAP_FROM_AMOUNT
        description:  "{{ doc('swaps_from_amt') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: SWAP_FROM_MINT
        description:  "{{ doc('swaps_from_mint') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: SWAP_TO_AMOUNT
        description:  "{{ doc('swaps_to_amt') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: SWAP_TO_MINT
        description:  "{{ doc('swaps_to_mint') }}"
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: FACT_SWAPS_JUPITER_INNER_ID
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: INSERTED_TIMESTAMP
        tests:
          - dbt_expectations.expect_column_to_exist
          - not_null: *recent_date_filter
      - name: MODIFIED_TIMESTAMP
        description: '{{ doc("modified_timestamp") }}' 
        tests:
          - dbt_expectations.expect_column_to_exist
