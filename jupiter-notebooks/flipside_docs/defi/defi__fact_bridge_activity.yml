version: 2
models:
  - name: defi__fact_bridge_activity
    description: Table containing bridging actions on Worm<PERSON>, DeBridge and Mayan Finance
    recent_date_filter: &recent_date_filter
      config:
        where: modified_timestamp >= current_date - 7
    columns:
      - name: BLOCK_TIMESTAMP
        description: "{{ doc('block_timestamp') }}"
        data_tests:
          - not_null: *recent_date_filter 
      - name: BLOCK_ID
        description: "{{ doc('block_id') }}"
        data_tests:
          - not_null: *recent_date_filter 
      - name: TX_ID
        description: "{{ doc('tx_id') }}"
        data_tests:
          - not_null: *recent_date_filter 
      - name: SUCCEEDED
        description: "{{ doc('tx_succeeded') }}" 
        data_tests:
          - not_null: *recent_date_filter 
      - name: PROGRAM_ID
        description: "{{ doc('program_id') }}"
        data_tests:
          - not_null: *recent_date_filter 
      - name: PLATFORM
        description: Name of the bridge protocol
        data_tests:
          - not_null: *recent_date_filter 
      - name: DIRECTION
        description: Direction of the bridge - either inbound to Solana or outbound from Solana
        data_tests:
          - not_null: *recent_date_filter 
      - name: USER_ADDRESS
        description: The address receiving or sending bridged tokens
        data_tests:
          - not_null: *recent_date_filter 
      - name: AMOUNT
        description: "{{ doc('amount') }}"
        data_tests:
          - not_null: *recent_date_filter 
      - name: MINT
        description: "{{ doc('mint') }}"
        data_tests:
          - not_null: *recent_date_filter 
      - name: FACT_BRIDGE_ACTIVITY_ID
        description: '{{ doc("pk") }}'  
        data_tests: 
          - unique: *recent_date_filter 
      - name: INSERTED_TIMESTAMP
        description: '{{ doc("inserted_timestamp") }}'   
        data_tests: 
          - not_null: *recent_date_filter 
      - name: MODIFIED_TIMESTAMP
        description: '{{ doc("modified_timestamp") }}' 
        data_tests: 
          - not_null
