{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"cell_id": "e1bd6f1893e44e798d8a9a818a2f4b54", "deepnote_cell_type": "code", "execution_context_id": "5ba8fb5a-825a-485f-81db-f13eaf5a5225", "execution_millis": 7447, "execution_start": 1748328301670, "source_hash": "70f387e9"}, "outputs": [], "source": ["import os\n", "from dotenv import load_dotenv\n", "import pandas as pd\n", "\n", "load_dotenv()  # This will load environment variables from a .env file into os.environ\n", "\n", "from flipside import Flipside\n", "\n", "# Initialize `Flipside` with your API Key and API Url\n", "flipside = Flipside(os.getenv(\"FLIPSIDE_API_KEY\"), \"https://api-v2.flipsidecrypto.xyz\")\n", "\n", "pd.set_option('display.max_colwidth', None)\n", "pd.set_option('display.width', 0)\n", "pd.set_option('display.expand_frame_repr', False)\n", "import locale\n", "locale.setlocale(locale.LC_ALL, '')  # Set to the user's default locale (or specify e.g. 'en_US.UTF-8')\n", "\n", "def format_money(x):\n", "    return locale.format_string('%.2f', x, grouping=True)\n", "\n", "def query_df(sql):\n", "    query_result_set = flipside.query(sql)\n", "    print(f\"found {len(query_result_set.rows)} rows\")\n", "    return pd.DataFrame(query_result_set.records)\n", "\n", "def move_columns_to_front(df, columns_to_front):\n", "    columns_to_front = [col for col in columns_to_front if col in df.columns]\n", "    remaining_columns = [col for col in df.columns if col not in columns_to_front]\n", "    new_order = columns_to_front + remaining_columns\n", "    return df[new_order]\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"cell_id": "9860a2f89edf47c0b33a91f5df365b05", "deepnote_cell_type": "code", "deepnote_table_loading": false, "deepnote_table_state": {"cellFormattingRules": [], "columnDisplayNames": [], "columnOrder": ["database_name", "schema_name", "table_name", "column_name", "column_order", "data_type", "is_nullable", "character_maximum_length", "numeric_precision", "numeric_scale", "column_default", "__row_index"], "conditionalFilters": [], "filters": [], "hiddenColumnIds": [], "pageIndex": 20, "pageSize": 10, "sortBy": [], "wrappedTextColumnIds": []}, "execution_context_id": "2481b31f-37f2-4e31-b4f2-b58fe0303c88", "execution_millis": 588, "execution_start": 1747901971628, "source_hash": "dc9217a9"}, "outputs": [{"ename": "NameError", "evalue": "name 'query_flipside' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[2], line 13\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;66;03m# Modify the code to include table name information on the table level along with columns details in JSON format\u001b[39;00m\n\u001b[1;32m      3\u001b[0m query \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\"\"\u001b[39m\u001b[38;5;124mSELECT\u001b[39m\n\u001b[1;32m      4\u001b[0m \u001b[38;5;124m    *\u001b[39m\n\u001b[1;32m      5\u001b[0m \u001b[38;5;124mFROM\u001b[39m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     10\u001b[0m \u001b[38;5;124m    TABLE_NAME,\u001b[39m\n\u001b[1;32m     11\u001b[0m \u001b[38;5;124m    ORDINAL_POSITION;\u001b[39m\u001b[38;5;124m\"\"\"\u001b[39m\n\u001b[0;32m---> 13\u001b[0m results \u001b[38;5;241m=\u001b[39m \u001b[43mquery_flipside\u001b[49m(query)\n\u001b[1;32m     15\u001b[0m results\n", "\u001b[0;31mNameError\u001b[0m: name 'query_flipside' is not defined"]}], "source": ["# Modify the code to include table name information on the table level along with columns details in JSON format\n", "\n", "query = \"\"\"SELECT\n", "    *\n", "FROM\n", "    solana.INFORMATION_SCHEMA.COLUMNS\n", "WHERE\n", "    TABLE_NAME != 'SOLANA_SHARE'\n", "ORDER BY\n", "    TABLE_NAME,\n", "    ORDINAL_POSITION;\"\"\"\n", "\n", "results = query_flipside(query)\n", "\n", "results"]}, {"cell_type": "markdown", "metadata": {"cell_id": "d22c7075d09248dfb9bcb399babddd45", "color": "purple", "deepnote_cell_type": "text-cell-p", "formattedRanges": []}, "source": []}, {"cell_type": "markdown", "metadata": {"cell_id": "169e5058495f429cbdb14ddfce778017", "deepnote_cell_type": "text-cell-p", "formattedRanges": []}, "source": ["Convert the queried Solana schema (stored in `results`) into JSON format."]}, {"cell_type": "code", "execution_count": 19, "metadata": {"cell_id": "ca209b95f18b4b6787c608d0a59aa054", "deepnote_cell_type": "code", "execution_context_id": "2481b31f-37f2-4e31-b4f2-b58fe0303c88", "execution_millis": 1, "execution_start": 1747902011706, "source_hash": "5376e0bc"}, "outputs": [{"data": {"text/plain": ["array(['INFORMATION_SCHEMA', 'SA', 'PRICE', 'GOV', 'CORE', 'SOLANA_SHARE',\n", "       'NFT', 'MA<PERSON><PERSON><PERSON>', 'STATS', 'DEFI'], dtype=object)"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["# Print all unique table_schema values\n", "filter_table_names = ''\n", "unique_schemas = results['table_schema'].unique()\n", "unique_schemas"]}, {"cell_type": "code", "execution_count": 37, "metadata": {"cell_id": "8850ee5fb7874a57ab04ab177921fc8f", "deepnote_cell_type": "code", "execution_context_id": "2481b31f-37f2-4e31-b4f2-b58fe0303c88", "execution_millis": 1, "execution_start": 1747902273689, "source_hash": "568bcf9a"}, "outputs": [{"data": {"application/vnd.deepnote.dataframe.v3+json": {"column_count": 2, "columns": [{"dtype": "object", "name": "table_schema", "stats": {"categories": [{"count": 1, "name": "CORE"}, {"count": 1, "name": "DEFI"}, {"count": 5, "name": "5 others"}], "nan_count": 0, "unique_count": 7}}, {"dtype": "int64", "name": "row_count", "stats": {"histogram": [{"bin_end": 35.6, "bin_start": 13, "count": 1}, {"bin_end": 58.2, "bin_start": 35.6, "count": 1}, {"bin_end": 80.80000000000001, "bin_start": 58.2, "count": 0}, {"bin_end": 103.4, "bin_start": 80.80000000000001, "count": 1}, {"bin_end": 126, "bin_start": 103.4, "count": 0}, {"bin_end": 148.60000000000002, "bin_start": 126, "count": 0}, {"bin_end": 171.20000000000002, "bin_start": 148.60000000000002, "count": 3}, {"bin_end": 193.8, "bin_start": 171.20000000000002, "count": 0}, {"bin_end": 216.4, "bin_start": 193.8, "count": 0}, {"bin_end": 239, "bin_start": 216.4, "count": 1}], "max": "239", "min": "13", "nan_count": 0, "unique_count": 7}}, {"dtype": "int64", "name": "_deepnote_index_column"}], "row_count": 7, "rows": [{"_deepnote_index_column": 0, "row_count": 167, "table_schema": "CORE"}, {"_deepnote_index_column": 1, "row_count": 162, "table_schema": "DEFI"}, {"_deepnote_index_column": 2, "row_count": 239, "table_schema": "GOV"}, {"_deepnote_index_column": 3, "row_count": 158, "table_schema": "MARINADE"}, {"_deepnote_index_column": 4, "row_count": 88, "table_schema": "NFT"}, {"_deepnote_index_column": 5, "row_count": 44, "table_schema": "PRICE"}, {"_deepnote_index_column": 6, "row_count": 13, "table_schema": "STATS"}], "type": "dataframe"}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>table_schema</th>\n", "      <th>row_count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>CORE</td>\n", "      <td>167</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>DEFI</td>\n", "      <td>162</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>GOV</td>\n", "      <td>239</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>MARINADE</td>\n", "      <td>158</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NFT</td>\n", "      <td>88</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>PRICE</td>\n", "      <td>44</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>STATS</td>\n", "      <td>13</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  table_schema  row_count\n", "0         CORE        167\n", "1         DEFI        162\n", "2          GOV        239\n", "3     MARINADE        158\n", "4          NFT         88\n", "5        PRICE         44\n", "6        STATS         13"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["# Aggregate the row count per table_schema\n", "schema_row_count = filtered_results.groupby('table_schema').size().reset_index(name='row_count')\n", "\n", "# Display the aggregated result\n", "schema_row_count"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"cell_id": "eea1629129b94add9af83364dca79ff2", "deepnote_cell_type": "code", "deepnote_table_loading": false, "deepnote_table_state": {"cellFormattingRules": [], "columnDisplayNames": [], "columnOrder": ["marketplace", "marketplace_version", "block_timestamp", "block_id", "tx_id", "succeeded", "index", "inner_index", "program_id", "buyer_address", "seller_address", "mint", "nft_name", "price", "currency_address", "currency_symbol", "price_usd", "tree_authority", "merkle_tree", "leaf_index", "is_compressed", "nft_collection_name", "collection_id", "creators", "authority", "metadata", "image_url", "metadata_uri", "ez_nft_sales_id", "inserted_timestamp", "modified_timestamp", "__row_index"], "conditionalFilters": [], "filters": [], "hiddenColumnIds": [], "pageIndex": 0, "pageSize": 10, "sortBy": [{"id": "block_timestamp", "type": "asc"}], "wrappedTextColumnIds": []}, "execution_context_id": "2481b31f-37f2-4e31-b4f2-b58fe0303c88", "execution_millis": 9796, "execution_start": 1747903576879, "source_hash": "281fe159"}, "outputs": [], "source": ["def query_liquidity_pool_actions(mints):\n", "    mint_list = \"', '\".join(mints)\n", "    query = f\"\"\"\n", "    SELECT\n", "        *\n", "    FROM\n", "        solana.defi.ez_liquidity_pool_actions\n", "    WHERE\n", "        token_a_mint IN ('{mint_list}') or token_b_mint IN ('{mint_list}')\n", "        and action_type= 'deposit'\n", "    ORDER BY\n", "        block_timestamp;\"\"\"\n", "        \n", "    df = query_df(query)\n", "    df['block_date'] = pd.to_datetime(df['block_timestamp']).dt.date\n", "    return df"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["found 22129 rows\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 22129 entries, 0 to 22128\n", "Data columns (total 32 columns):\n", " #   Column                        Non-Null Count  Dtype  \n", "---  ------                        --------------  -----  \n", " 0   block_id                      22129 non-null  int64  \n", " 1   block_timestamp               22129 non-null  object \n", " 2   tx_id                         22129 non-null  object \n", " 3   index                         22129 non-null  int64  \n", " 4   inner_index                   3127 non-null   float64\n", " 5   action_type                   22129 non-null  object \n", " 6   provider_address              22129 non-null  object \n", " 7   token_a_mint                  22129 non-null  object \n", " 8   token_a_symbol                22129 non-null  object \n", " 9   token_a_amount                22129 non-null  float64\n", " 10  token_a_amount_usd            20756 non-null  float64\n", " 11  token_b_mint                  21273 non-null  object \n", " 12  token_b_symbol                21268 non-null  object \n", " 13  token_b_amount                21273 non-null  float64\n", " 14  token_b_amount_usd            21266 non-null  float64\n", " 15  token_c_mint                  0 non-null      object \n", " 16  token_c_symbol                0 non-null      object \n", " 17  token_c_amount                0 non-null      object \n", " 18  token_c_amount_usd            0 non-null      object \n", " 19  token_d_mint                  0 non-null      object \n", " 20  token_d_symbol                0 non-null      object \n", " 21  token_d_amount                0 non-null      object \n", " 22  token_d_amount_usd            0 non-null      object \n", " 23  pool_address                  22129 non-null  object \n", " 24  pool_name                     22124 non-null  object \n", " 25  program_id                    22129 non-null  object \n", " 26  platform                      22129 non-null  object \n", " 27  ez_liquidity_pool_actions_id  22129 non-null  object \n", " 28  inserted_timestamp            22129 non-null  object \n", " 29  modified_timestamp            22129 non-null  object \n", " 30  __row_index                   22129 non-null  int64  \n", " 31  block_date                    22129 non-null  object \n", "dtypes: float64(5), int64(3), object(24)\n", "memory usage: 5.4+ MB\n"]}], "source": ["mints = [\"G9tt98aYSznRk7jWsfuz9FnTdokxS6Brohdo9hSmjTRB\", \"7ScYHk4VDgSRnQngAUtQk4Eyf7fGat8P4wXq6e2dkzLj\"]\n", "liquidity_actions = query_liquidity_pool_actions(mints)\n", "liquidity_actions.info()"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["liquidity_actions.to_csv(\"liquidity_actions.csv\", index=False)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data type of block_timestamp:\n", "object\n", "\n", "Sample values:\n", "0    2021-12-14T12:40:07.000Z\n", "1    2021-12-14T12:47:33.000Z\n", "2    2021-12-14T12:49:01.000Z\n", "3    2021-12-14T13:11:17.000Z\n", "4    2021-12-14T13:31:51.000Z\n", "Name: block_timestamp, dtype: object\n", "\n", "After conversion:\n", "Data type of block_date:\n", "object\n", "\n", "Sample date values:\n", "0    2021-12-14\n", "1    2021-12-14\n", "2    2021-12-14\n", "3    2021-12-14\n", "4    2021-12-14\n", "Name: block_date, dtype: object\n"]}], "source": ["# Check the data type of block_timestamp column\n", "print(\"Data type of block_timestamp:\")\n", "print(all_liquidity_actions['block_timestamp'].dtype)\n", "print(\"\\nSample values:\")\n", "print(all_liquidity_actions['block_timestamp'].head())\n", "\n", "# Convert block_timestamp to datetime and then to date\n", "all_liquidity_actions['block_date'] = pd.to_datetime(all_liquidity_actions['block_timestamp']).dt.date\n", "\n", "print(\"\\nAfter conversion:\")\n", "print(\"Data type of block_date:\")\n", "print(all_liquidity_actions['block_date'].dtype)\n", "print(\"\\nSample date values:\")\n", "print(all_liquidity_actions['block_date'].head())\n", "\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["found 1182 rows\n", "found 849 deposit actions\n"]}, {"data": {"text/plain": ["'256,802.23'"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["all_token = \"7ScYHk4VDgSRnQngAUtQk4Eyf7fGat8P4wXq6e2dkzLj\"\n", "all_liquidity_actions = query_liquidity_pool_actions(all_token)\n", "\n", "all_deposit_actions = all_liquidity_actions[all_liquidity_actions['action_type'] == 'deposit']\n", "\n", "# print deposit actions info\n", "print(f\"found {len(all_deposit_actions)} deposit actions\")\n", "\n", "total_token_sold_amount_usd = all_deposit_actions['token_b_amount_usd'].astype(float).sum()\n", "format_money(total_token_sold_amount_usd)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["found 20947 rows\n", "found 16691 deposit actions\n"]}, {"data": {"text/plain": ["'4,318,376.89'"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["puff_token = \"G9tt98aYSznRk7jWsfuz9FnTdokxS6Brohdo9hSmjTRB\"\n", "puff_liquidity_actions = query_liquidity_pool_actions(puff_token)\n", "\n", "puff_deposit_actions = puff_liquidity_actions[puff_liquidity_actions['action_type'] == 'deposit']\n", "\n", "# print deposit actions info\n", "print(f\"found {len(puff_deposit_actions)} deposit actions\")\n", "\n", "total_token_sold_amount_usd = puff_deposit_actions['token_b_amount_usd'].astype(float).sum()\n", "format_money(total_token_sold_amount_usd)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "Can only use .dt accessor with datetimelike values", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[25], line 3\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;66;03m# Print block_timestamp without milliseconds\u001b[39;00m\n\u001b[1;32m      2\u001b[0m puff_deposit_actions_clean \u001b[38;5;241m=\u001b[39m puff_deposit_actions\u001b[38;5;241m.\u001b[39mcopy()\n\u001b[0;32m----> 3\u001b[0m puff_deposit_actions_clean[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mblock_timestamp\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[43mpuff_deposit_actions_clean\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mblock_timestamp\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdt\u001b[49m\u001b[38;5;241m.\u001b[39mstrftime(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m%\u001b[39m\u001b[38;5;124mY-\u001b[39m\u001b[38;5;124m%\u001b[39m\u001b[38;5;124mm-\u001b[39m\u001b[38;5;132;01m%d\u001b[39;00m\u001b[38;5;124m \u001b[39m\u001b[38;5;124m%\u001b[39m\u001b[38;5;124mH:\u001b[39m\u001b[38;5;124m%\u001b[39m\u001b[38;5;124mM:\u001b[39m\u001b[38;5;124m%\u001b[39m\u001b[38;5;124mS\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m      4\u001b[0m \u001b[38;5;28mprint\u001b[39m(puff_deposit_actions_clean[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mblock_timestamp\u001b[39m\u001b[38;5;124m'\u001b[39m])\n\u001b[1;32m      5\u001b[0m puff_deposit_actions_clean\u001b[38;5;241m.\u001b[39mto_csv(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpuff_deposit_actions.csv\u001b[39m\u001b[38;5;124m\"\u001b[39m, index\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m)\n", "File \u001b[0;32m~/.pyenv/versions/3.10.9/lib/python3.10/site-packages/pandas/core/generic.py:5902\u001b[0m, in \u001b[0;36mNDFrame.__getattr__\u001b[0;34m(self, name)\u001b[0m\n\u001b[1;32m   5895\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m (\n\u001b[1;32m   5896\u001b[0m     name \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_internal_names_set\n\u001b[1;32m   5897\u001b[0m     \u001b[38;5;129;01mand\u001b[39;00m name \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_metadata\n\u001b[1;32m   5898\u001b[0m     \u001b[38;5;129;01mand\u001b[39;00m name \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_accessors\n\u001b[1;32m   5899\u001b[0m     \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_info_axis\u001b[38;5;241m.\u001b[39m_can_hold_identifiers_and_holds_name(name)\n\u001b[1;32m   5900\u001b[0m ):\n\u001b[1;32m   5901\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m[name]\n\u001b[0;32m-> 5902\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mobject\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;21;43m__getattribute__\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mname\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.pyenv/versions/3.10.9/lib/python3.10/site-packages/pandas/core/accessor.py:182\u001b[0m, in \u001b[0;36mCachedAccessor.__get__\u001b[0;34m(self, obj, cls)\u001b[0m\n\u001b[1;32m    179\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m obj \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    180\u001b[0m     \u001b[38;5;66;03m# we're accessing the attribute of the class, i.e., Dataset.geo\u001b[39;00m\n\u001b[1;32m    181\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_accessor\n\u001b[0;32m--> 182\u001b[0m accessor_obj \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_accessor\u001b[49m\u001b[43m(\u001b[49m\u001b[43mobj\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    183\u001b[0m \u001b[38;5;66;03m# Replace the property with the accessor object. Inspired by:\u001b[39;00m\n\u001b[1;32m    184\u001b[0m \u001b[38;5;66;03m# https://www.pydanny.com/cached-property.html\u001b[39;00m\n\u001b[1;32m    185\u001b[0m \u001b[38;5;66;03m# We need to use object.__setattr__ because we overwrite __setattr__ on\u001b[39;00m\n\u001b[1;32m    186\u001b[0m \u001b[38;5;66;03m# NDFrame\u001b[39;00m\n\u001b[1;32m    187\u001b[0m \u001b[38;5;28mobject\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;21m__setattr__\u001b[39m(obj, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_name, accessor_obj)\n", "File \u001b[0;32m~/.pyenv/versions/3.10.9/lib/python3.10/site-packages/pandas/core/indexes/accessors.py:512\u001b[0m, in \u001b[0;36mCombinedDatetimelikeProperties.__new__\u001b[0;34m(cls, data)\u001b[0m\n\u001b[1;32m    509\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m is_period_dtype(data\u001b[38;5;241m.\u001b[39mdtype):\n\u001b[1;32m    510\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m PeriodProperties(data, orig)\n\u001b[0;32m--> 512\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mAttributeError\u001b[39;00m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mCan only use .dt accessor with datetimelike values\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[0;31mAttributeError\u001b[0m: Can only use .dt accessor with datetimelike values"]}], "source": ["# Print block_timestamp without milliseconds\n", "puff_deposit_actions_clean = puff_deposit_actions.copy()\n", "puff_deposit_actions_clean['block_timestamp'] = puff_deposit_actions_clean['block_timestamp'].dt.strftime('%Y-%m-%d %H:%M:%S')\n", "print(puff_deposit_actions_clean['block_timestamp'])\n", "puff_deposit_actions_clean.to_csv(\"puff_deposit_actions.csv\", index=False)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"cell_id": "a7ada12c5b504fb09873dfd20a9d04aa", "deepnote_cell_type": "code", "execution_context_id": "33fd4e3b-8369-4b13-9536-ede9ea21b980", "execution_millis": 89448, "execution_start": 1747904092514, "source_hash": "62edf066"}, "outputs": [{"ename": "QueryRunExecutionError", "evalue": "QUERY_RUN_EXECUTION_ERROR: an error has occured while executing your query. errorName=QueryResultsTooLarge, errorMessage=The size of the returned query results are too large. Please reduce the number of rows returned. Limit: 1024 MB. Estimated size: 5931 MB., errorData={'resultSizelimitGB': 1024, 'estimatedResultSizeMB': 5931}", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON>ueryRunExecutionError\u001b[0m                    <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[11], line 27\u001b[0m\n\u001b[1;32m     24\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m query_flipside(query)\n\u001b[1;32m     26\u001b[0m \u001b[38;5;66;03m# Example usage\u001b[39;00m\n\u001b[0;32m---> 27\u001b[0m example_collection_mints \u001b[38;5;241m=\u001b[39m \u001b[43mquery_nft_collection_mints\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mcndy3Z4yapfJBmL3ShUp5exZKqR3z33thTzeNMm2gRZ\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m     28\u001b[0m example_collection_mints\n", "Cell \u001b[0;32mIn[11], line 24\u001b[0m, in \u001b[0;36mquery_nft_collection_mints\u001b[0;34m(candy_machine_id)\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mquery_nft_collection_mints\u001b[39m(candy_machine_id):\n\u001b[1;32m      2\u001b[0m     query \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\"\"\u001b[39m\n\u001b[1;32m      3\u001b[0m \u001b[38;5;124m    SELECT\u001b[39m\n\u001b[1;32m      4\u001b[0m \u001b[38;5;124m        block_timestamp,\u001b[39m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     22\u001b[0m \u001b[38;5;124m        block_timestamp;\u001b[39m\n\u001b[1;32m     23\u001b[0m \u001b[38;5;124m    \u001b[39m\u001b[38;5;124m\"\"\"\u001b[39m\n\u001b[0;32m---> 24\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[43mquery_flipside\u001b[49m\u001b[43m(\u001b[49m\u001b[43mquery\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[0;32mIn[8], line 9\u001b[0m, in \u001b[0;36m<PERSON>y_flipside\u001b[0;34m(query)\u001b[0m\n\u001b[1;32m      8\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mquery_flipside\u001b[39m(query):\n\u001b[0;32m----> 9\u001b[0m     flipside_res \u001b[38;5;241m=\u001b[39m \u001b[43mflipside\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mquery\u001b[49m\u001b[43m(\u001b[49m\u001b[43mquery\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     10\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m pd\u001b[38;5;241m.\u001b[39mDataFrame(flipside_res\u001b[38;5;241m.\u001b[39mrecords)\n", "File \u001b[0;32m~/venv/lib/python3.10/site-packages/flipside/flipside.py:48\u001b[0m, in \u001b[0;36mFlipside.query\u001b[0;34m(self, sql, ttl_minutes, max_age_minutes, cached, timeout_minutes, retry_interval_seconds, page_size, page_number, data_source, data_provider)\u001b[0m\n\u001b[1;32m     33\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mquery\u001b[39m(\n\u001b[1;32m     34\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m     35\u001b[0m     sql,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     44\u001b[0m     data_provider\u001b[38;5;241m=\u001b[39mDEFAULT_DATA_PROVIDER,\n\u001b[1;32m     45\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m QueryResultSet:\n\u001b[1;32m     46\u001b[0m     query_integration \u001b[38;5;241m=\u001b[39m CompassQueryIntegration(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mrpc)\n\u001b[0;32m---> 48\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mquery_integration\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrun\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m     49\u001b[0m \u001b[43m        \u001b[49m\u001b[43mQuery\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m     50\u001b[0m \u001b[43m            \u001b[49m\u001b[43msql\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msql\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     51\u001b[0m \u001b[43m            \u001b[49m\u001b[43mttl_minutes\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mttl_minutes\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     52\u001b[0m \u001b[43m            \u001b[49m\u001b[43mtimeout_minutes\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout_minutes\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     53\u001b[0m \u001b[43m            \u001b[49m\u001b[43mmax_age_minutes\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmax_age_minutes\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     54\u001b[0m \u001b[43m            \u001b[49m\u001b[43mretry_interval_seconds\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mretry_interval_seconds\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     55\u001b[0m \u001b[43m            \u001b[49m\u001b[43mpage_size\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mpage_size\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     56\u001b[0m \u001b[43m            \u001b[49m\u001b[43mpage_number\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mpage_number\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     57\u001b[0m \u001b[43m            \u001b[49m\u001b[43mcached\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcached\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     58\u001b[0m \u001b[43m            \u001b[49m\u001b[43msdk_package\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mSDK_PACKAGE\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     59\u001b[0m \u001b[43m            \u001b[49m\u001b[43msdk_version\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mSDK_VERSION\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     60\u001b[0m \u001b[43m            \u001b[49m\u001b[43mdata_source\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdata_source\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     61\u001b[0m \u001b[43m            \u001b[49m\u001b[43mdata_provider\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdata_provider\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     62\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     63\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/venv/lib/python3.10/site-packages/flipside/integrations/query_integration/compass_query_integration.py:69\u001b[0m, in \u001b[0;36mCompassQueryIntegration.run\u001b[0;34m(self, query)\u001b[0m\n\u001b[1;32m     66\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m created_query\u001b[38;5;241m.\u001b[39mresult \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m created_query\u001b[38;5;241m.\u001b[39mresult\u001b[38;5;241m.\u001b[39mqueryRun:\n\u001b[1;32m     67\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m SDKError(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mexpected `query_run` from server but got `None`\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m---> 69\u001b[0m query_run \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_get_query_run_loop\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m     70\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcreated_query\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mresult\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mqueryRun\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mid\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     71\u001b[0m \u001b[43m    \u001b[49m\u001b[43mpage_number\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mquery\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpage_number\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m     72\u001b[0m \u001b[43m    \u001b[49m\u001b[43mpage_size\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mquery\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpage_size\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;241;43m100000\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m     73\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtimeout_minutes\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mquery\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtimeout_minutes\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;241;43m20\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m     74\u001b[0m \u001b[43m    \u001b[49m\u001b[43mretry_interval_seconds\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mretry_interval_seconds\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     75\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     77\u001b[0m query_result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_query_results(\n\u001b[1;32m     78\u001b[0m     query_run\u001b[38;5;241m.\u001b[39mid,\n\u001b[1;32m     79\u001b[0m     page_number\u001b[38;5;241m=\u001b[39mquery\u001b[38;5;241m.\u001b[39mpage_number \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;241m1\u001b[39m,\n\u001b[1;32m     80\u001b[0m     page_size\u001b[38;5;241m=\u001b[39mquery\u001b[38;5;241m.\u001b[39mpage_size \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;241m100000\u001b[39m,\n\u001b[1;32m     81\u001b[0m )\n\u001b[1;32m     83\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m QueryResultSetBuilder(\n\u001b[1;32m     84\u001b[0m     query_run\u001b[38;5;241m=\u001b[39mquery_run,\n\u001b[1;32m     85\u001b[0m     query_result\u001b[38;5;241m=\u001b[39mquery_result,\n\u001b[1;32m     86\u001b[0m )\u001b[38;5;241m.\u001b[39mbuild()\n", "File \u001b[0;32m~/venv/lib/python3.10/site-packages/flipside/integrations/query_integration/compass_query_integration.py:268\u001b[0m, in \u001b[0;36mCompassQueryIntegration._get_query_run_loop\u001b[0;34m(self, query_run_id, page_number, page_size, attempts, timeout_minutes, retry_interval_seconds)\u001b[0m\n\u001b[1;32m    258\u001b[0m     elapsed_seconds \u001b[38;5;241m=\u001b[39m get_elapsed_linear_seconds(\n\u001b[1;32m    259\u001b[0m         SleepConfig(\n\u001b[1;32m    260\u001b[0m             attempts\u001b[38;5;241m=\u001b[39mattempts,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    263\u001b[0m         )\n\u001b[1;32m    264\u001b[0m     )\n\u001b[1;32m    266\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m QueryRunTimeoutError(elapsed_seconds)\n\u001b[0;32m--> 268\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_get_query_run_loop\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    269\u001b[0m \u001b[43m    \u001b[49m\u001b[43mquery_run_id\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    270\u001b[0m \u001b[43m    \u001b[49m\u001b[43mpage_number\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    271\u001b[0m \u001b[43m    \u001b[49m\u001b[43mpage_size\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    272\u001b[0m \u001b[43m    \u001b[49m\u001b[43mattempts\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m+\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m    273\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtimeout_minutes\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    274\u001b[0m \u001b[43m    \u001b[49m\u001b[43mretry_interval_seconds\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    275\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/venv/lib/python3.10/site-packages/flipside/integrations/query_integration/compass_query_integration.py:268\u001b[0m, in \u001b[0;36mCompassQueryIntegration._get_query_run_loop\u001b[0;34m(self, query_run_id, page_number, page_size, attempts, timeout_minutes, retry_interval_seconds)\u001b[0m\n\u001b[1;32m    258\u001b[0m     elapsed_seconds \u001b[38;5;241m=\u001b[39m get_elapsed_linear_seconds(\n\u001b[1;32m    259\u001b[0m         SleepConfig(\n\u001b[1;32m    260\u001b[0m             attempts\u001b[38;5;241m=\u001b[39mattempts,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    263\u001b[0m         )\n\u001b[1;32m    264\u001b[0m     )\n\u001b[1;32m    266\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m QueryRunTimeoutError(elapsed_seconds)\n\u001b[0;32m--> 268\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_get_query_run_loop\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    269\u001b[0m \u001b[43m    \u001b[49m\u001b[43mquery_run_id\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    270\u001b[0m \u001b[43m    \u001b[49m\u001b[43mpage_number\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    271\u001b[0m \u001b[43m    \u001b[49m\u001b[43mpage_size\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    272\u001b[0m \u001b[43m    \u001b[49m\u001b[43mattempts\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m+\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m    273\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtimeout_minutes\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    274\u001b[0m \u001b[43m    \u001b[49m\u001b[43mretry_interval_seconds\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    275\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "    \u001b[0;31m[... skipping similar frames: CompassQueryIntegration._get_query_run_loop at line 268 (9 times)]\u001b[0m\n", "File \u001b[0;32m~/venv/lib/python3.10/site-packages/flipside/integrations/query_integration/compass_query_integration.py:268\u001b[0m, in \u001b[0;36mCompassQueryIntegration._get_query_run_loop\u001b[0;34m(self, query_run_id, page_number, page_size, attempts, timeout_minutes, retry_interval_seconds)\u001b[0m\n\u001b[1;32m    258\u001b[0m     elapsed_seconds \u001b[38;5;241m=\u001b[39m get_elapsed_linear_seconds(\n\u001b[1;32m    259\u001b[0m         SleepConfig(\n\u001b[1;32m    260\u001b[0m             attempts\u001b[38;5;241m=\u001b[39mattempts,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    263\u001b[0m         )\n\u001b[1;32m    264\u001b[0m     )\n\u001b[1;32m    266\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m QueryRunTimeoutError(elapsed_seconds)\n\u001b[0;32m--> 268\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_get_query_run_loop\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    269\u001b[0m \u001b[43m    \u001b[49m\u001b[43mquery_run_id\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    270\u001b[0m \u001b[43m    \u001b[49m\u001b[43mpage_number\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    271\u001b[0m \u001b[43m    \u001b[49m\u001b[43mpage_size\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    272\u001b[0m \u001b[43m    \u001b[49m\u001b[43mattempts\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m+\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m    273\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtimeout_minutes\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    274\u001b[0m \u001b[43m    \u001b[49m\u001b[43mretry_interval_seconds\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    275\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/venv/lib/python3.10/site-packages/flipside/integrations/query_integration/compass_query_integration.py:236\u001b[0m, in \u001b[0;36mCompassQueryIntegration._get_query_run_loop\u001b[0;34m(self, query_run_id, page_number, page_size, attempts, timeout_minutes, retry_interval_seconds)\u001b[0m\n\u001b[1;32m    233\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m query_run\u001b[38;5;241m.\u001b[39merrorName \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mQueryRunTimedOut\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[1;32m    234\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m QueryRunTimeoutError()\n\u001b[0;32m--> 236\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m QueryRunExecutionError(\n\u001b[1;32m    237\u001b[0m         error_message\u001b[38;5;241m=\u001b[39mquery_run\u001b[38;5;241m.\u001b[39merrorMessage,\n\u001b[1;32m    238\u001b[0m         error_name\u001b[38;5;241m=\u001b[39mquery_run\u001b[38;5;241m.\u001b[39merrorName,\n\u001b[1;32m    239\u001b[0m         error_data\u001b[38;5;241m=\u001b[39mquery_run\u001b[38;5;241m.\u001b[39merrorData,\n\u001b[1;32m    240\u001b[0m     )\n\u001b[1;32m    242\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m query_status \u001b[38;5;241m==\u001b[39m QueryStatus\u001b[38;5;241m.\u001b[39mCanceled:\n\u001b[1;32m    243\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m QueryRunCancelledError(\n\u001b[1;32m    244\u001b[0m         error_message\u001b[38;5;241m=\u001b[39mquery_run\u001b[38;5;241m.\u001b[39merrorMessage,\n\u001b[1;32m    245\u001b[0m         error_name\u001b[38;5;241m=\u001b[39mquery_run\u001b[38;5;241m.\u001b[39merrorName,\n\u001b[1;32m    246\u001b[0m         error_data\u001b[38;5;241m=\u001b[39mquery_run\u001b[38;5;241m.\u001b[39merrorData,\n\u001b[1;32m    247\u001b[0m     )\n", "\u001b[0;31mQueryRunExecutionError\u001b[0m: QUERY_RUN_EXECUTION_ERROR: an error has occured while executing your query. errorName=QueryResultsTooLarge, errorMessage=The size of the returned query results are too large. Please reduce the number of rows returned. Limit: 1024 MB. Estimated size: 5931 MB., errorData={'resultSizelimitGB': 1024, 'estimatedResultSizeMB': 5931}"]}], "source": ["def query_nft_collection_mints(candy_machine_id):\n", "    query = f\"\"\"\n", "    SELECT\n", "        block_timestamp,\n", "        block_id,\n", "        tx_id,\n", "        succeeded,\n", "        program_id,\n", "        purchaser,\n", "        mint_price,\n", "        mint_currency,\n", "        mint,\n", "        is_compressed,\n", "        fact_nft_mints_id,\n", "        inserted_timestamp,\n", "        modified_timestamp\n", "    FROM\n", "        solana.nft.fact_nft_mints\n", "    WHERE\n", "        program_id = '{candy_machine_id}'\n", "    ORDER BY\n", "        block_timestamp;\n", "    \"\"\"\n", "    return query_flipside(query)\n", "\n", "# Example usage\n", "example_collection_mints = query_nft_collection_mints(\"cndy3Z4yapfJBmL3ShUp5exZKqR3z33thTzeNMm2gRZ\")\n", "example_collection_mints"]}, {"cell_type": "markdown", "metadata": {"created_in_deepnote_cell": true, "deepnote_cell_type": "markdown"}, "source": ["<a style='text-decoration:none;line-height:16px;display:flex;color:#5B5B62;padding:10px;justify-content:end;' href='https://deepnote.com?utm_source=created-in-deepnote-cell&projectId=63ee9690-e7fb-43cc-9ed0-72edbb3327f4' target=\"_blank\">\n", "<img alt='Created in deepnote.com' style='display:inline;max-height:16px;margin:0px;margin-right:7.5px;' src='data:image/svg+xml;base64,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' > </img>\n", "Created in <span style='font-weight:600;margin-left:4px;'>Deepnote</span></a>"]}], "metadata": {"deepnote_notebook_id": "e0ea8a814de143c7bafe54a8a3ffb5b9", "deepnote_persisted_session": {"createdAt": "2025-05-22T09:12:20.551Z"}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 0}