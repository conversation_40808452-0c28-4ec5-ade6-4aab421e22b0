import pandas as pd
import os
from dotenv import load_dotenv
import pandas as pd
from flipside import Flipside
import locale

locale.setlocale(locale.LC_ALL, "")

pd.set_option("display.max_colwidth", None)
pd.set_option("display.width", 0)
pd.set_option("display.expand_frame_repr", False)

load_dotenv()  # This will load environment variables from a .env file into os.environ


# Set to the user's default locale (or specify e.g. 'en_US.UTF-8')

def format_money(x):
    return locale.format_string("%.2f", x, grouping=True)


all_mint = "7ScYHk4VDgSRnQngAUtQk4Eyf7fGat8P4wXq6e2dkzLj"
puff_mint = "G9tt98aYSznRk7jWsfuz9FnTdokxS6Brohdo9hSmjTRB"
mints = [all_mint, puff_mint]

eu_wallet = "EUk3St1WoKqdFT491bg9BuQhqskZmKiywF7TyEvbaPKB"

sac_collection = "6o3ggULJGUBX8CvsGhomfZFirUYdH3wVdgT2d4Q7yHb8"
nuked_collection = "6yzCdFZ72HRd5xkzAdWrJLJo96XDHUeEKuHXvTvCJktq"
collections = [sac_collection, nuked_collection]

print(f"flipside api key: {os.getenv('FLIPSIDE_API_KEY')}")

# Initialize `Flipside` with your API Key and API Url
flipside = Flipside(os.getenv("FLIPSIDE_API_KEY"), "https://api-v2.flipsidecrypto.xyz")

def query_df(sql):
    query_result_set = flipside.query(sql)
    df = pd.DataFrame(query_result_set.records)
    print(f"found {len(df)} rows")

    if "block_timestamp" in df.columns:
        df["block_timestamp"] = pd.to_datetime(df["block_timestamp"])
        # create field accounting period, our accounting is from month 11 - 10, if month is bigger than 10, we are in the next year
        df["day"] = df["block_timestamp"].dt.date
        df["accounting_period"] = df["block_timestamp"].dt.year + (
            df["block_timestamp"].dt.month >= 11
        ).astype(int)

    return df


def move_columns_to_front(df, columns_to_front):
    # preserve the user’s order, drop duplicates
    columns_to_front = list(dict.fromkeys(col for col in columns_to_front if col in df.columns))
    remaining        = [c for c in df.columns if c not in columns_to_front]
    return df[columns_to_front + remaining]


def load_google_sheet(
    sheet_id,
    sheet_name,
):
    url = f"https://docs.google.com/spreadsheets/d/{sheet_id}/gviz/tq?tqx=out:csv&sheet={sheet_name}"
    df = pd.read_csv(url)

    return df


def load_company_wallets():
    company_wallets = load_google_sheet(
        sheet_id="1N3EeRbDZ6nuCgbrUTwdtspIsML0Agdrz7JpvkqZZy44",
        sheet_name="company_wallets",
    )
    company_wallets = company_wallets.add_prefix("sac_")
    # ABGLAB5UQ4oeyVH61RkFkxP9R5LY3iDQFBrDSiP86Tzc add to company_wallets
    company_wallets = company_wallets.append({
        "sac_pubkey": "ABGLAB5UQ4oeyVH61RkFkxP9R5LY3iDQFBrDSiP86Tzc",
        "sac_name": "Stoned Apes",
        "sac_email": "<EMAIL>",
        "sac_phone": "0000000000",
    }, ignore_index=True)
    return company_wallets


def merge_company_wallets(df, company_wallets, merge_on):
    new_df = df.merge(
        company_wallets, left_on=merge_on, right_on="sac_pubkey", how="left"
    )
    new_df["by_sac"] = new_df["sac_pubkey"].apply(
        lambda x: True if pd.notna(x) else False
    )
    return new_df
