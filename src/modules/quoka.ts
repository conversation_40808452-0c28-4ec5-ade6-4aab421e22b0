import { prisma } from "../prisma/prisma";

async function main() {
  const t = "sdfsd";

  console.log("t", t);

  const invoices = await prisma.invoice.findMany({
    where: {
      id: "3278552f-4b32-48cb-9b19-365b495264af"
      // vendor: {
      //   name: {
      //     contains: "upwork",
      //     mode: "insensitive",
      //   },
      // },
      // importItem: {
      //   text:{
      //     contains: "receipt",
      //     mode: "insensitive",
      //   },
      // },
    },
    include: {
      importItem: true,
    },
  });

  console.log("invoices", invoices);
}

main();
