import { envVars } from "@/envVars";
import { date } from "@/modules/core/utils/dateUtils";
import { context, trace } from "@opentelemetry/api";
import OpenAI from "openai";
import { zodResponseFormat } from "openai/helpers/zod";
import { ChatCompletion, ChatCompletionCreateParamsNonStreaming, CompletionUsage } from "openai/resources/index.mjs";
import { object, z, ZodTypeAny } from "zod";
import { openai } from "@ai-sdk/openai";
import { Span, wrapOpenAI } from "braintrust";
import { err, ok, Result, ResultAsync } from "neverthrow";
import { json } from "@/modules/core/utils/jsonUtils";
import { zodUtils } from "@/modules/core/utils/zodUtils";
import { ResponseUsage } from "openai/resources/responses/responses.mjs";
import { zodToJsonSchema } from "zod-to-json-schema";
import { google } from "@ai-sdk/google";
import { ResponseCreateParamsWithTools } from "openai/lib/ResponsesParser.mjs";
import * as R from "remeda";
import { cli } from "@/modules/core/utils/cliUtils";

export type GoogleAIModelId = Parameters<typeof google>[0];
export type OpenAIModelId = Parameters<typeof openai>[0];

export type AIModelId = GoogleAIModelId | OpenAIModelId;

export namespace llm {
  export const totalUsage = {
    promptTokens: 0,
    completionTokens: 0,
    totalTokens: 0,
  };

  export const allRequests: ChatCompletion[] = [];

  export const pricingPerModel: Partial<
    Record<
      AIModelId,
      {
        per1MInputTokens: number;
        per1MOutputTokens: number;
      }
    >
  > = {
    "gpt-4.1": {
      per1MInputTokens: 2,
      per1MOutputTokens: 8,
    },

    "gpt-4.1-2025-04-14": {
      per1MInputTokens: 0.4,
      per1MOutputTokens: 1.6,
    },
    "gpt-4.1-mini": {
      per1MInputTokens: 0.4,
      per1MOutputTokens: 1.6,
    },
    "gpt-4.1-mini-2025-04-14": {
      per1MInputTokens: 0.4,
      per1MOutputTokens: 1.6,
    },
    "o4-mini-2025-04-16": {
      per1MInputTokens: 1.1,
      per1MOutputTokens: 4,
    },
  };

  export function getUsageCost() {
    const usagePerModel = R.groupBy(allRequests, (r) => r.model);

    const costPerModel = R.mapValues(usagePerModel, (requests, model) => {
      const pricing = pricingPerModel[model as AIModelId];

      const totalInputTokens = R.sumBy(requests, (r) => r.usage?.prompt_tokens || 0);
      const totalOutputTokens = R.sumBy(requests, (r) => r.usage?.completion_tokens || 0);

      const totalCost = pricing
        ? (() => {
            const inputCost = (totalInputTokens / 1000000) * pricing.per1MInputTokens;
            const outputCost = (totalOutputTokens / 1000000) * pricing.per1MOutputTokens;
            return inputCost + outputCost;
          })()
        : null;

      return {
        totalInputTokens,
        totalOutputTokens,
        totalCost,
      };
    });

    const totalCost = R.sumBy(R.values(costPerModel), (r) => r.totalCost ?? 0);

    return {
      totalCost,
      costPerModel,
    };
  }

  export function updateUsage(usage: CompletionUsage | ResponseUsage | undefined) {
    if (!usage) return;
    if (usage && "prompt_tokens" in usage) {
      totalUsage.promptTokens += usage?.prompt_tokens || 0;
      totalUsage.completionTokens += usage?.completion_tokens || 0;
      totalUsage.totalTokens += usage?.total_tokens || 0;
    } else if ("input_tokens" in usage) {
      totalUsage.promptTokens += usage?.input_tokens || 0;
      totalUsage.completionTokens += usage?.output_tokens || 0;
      totalUsage.totalTokens += usage?.total_tokens || 0;
    }
  }

  // export const llm = wrapOpenAI(
  //   new OpenAI({
  //     baseURL: envVars.MOTL_CACHER_URL,
  //     defaultHeaders: {
  //       "x-api-key": envVars.MOTL_CACHER_API_KEY,
  //       "x-upstream-url": "https://api.openai.com/v1",
  //     },
  //   })
  // );

  export const llm = new OpenAI({
    baseURL: envVars.MOTL_CACHER_URL,
    defaultHeaders: {
      "x-api-key": envVars.MOTL_CACHER_API_KEY,
      "x-upstream-url": "https://api.openai.com/v1",
    },
  });

  // export const llm = wrapOpenAI(
  //   new OpenAI({
  //     apiKey: envVars.OPENAI_API_KEY,
  //   })
  // );

  export async function generateObject<TResultSchema extends ZodTypeAny>(
    args:
      | ({
          schema: TResultSchema;
        } & Omit<ChatCompletionCreateParamsNonStreaming, "model">) & {
          model: AIModelId;
          span?: Span;
        }
  ) {
    const { schema, span, ...restArgs } = args;

    // const activeSpan = trace.getSpan(context.active());

    const traceHeaders = span
      ? {
          "x-bt-parent": await span.export(),
        }
      : {};

    // const llm = new OpenAI({
    //   baseURL: "https://api.braintrust.dev/v1/proxy",
    //   apiKey: envVars.BRAINTRUST_API_KEY, // Can use Braintrust, Anthropic, etc. API keys here
    //   defaultHeaders: {
    //     ...traceHeaders,
    //   },
    // });

    const responseJsonSchema = zodResponseFormat(args.schema, "ResponseSchema");

    if (responseJsonSchema.json_schema.schema) delete responseJsonSchema.json_schema.schema.$schema;

    const startedAt = new Date();
    const llmResWrapped = await ResultAsync.fromPromise(
      llm.chat.completions.create({
        ...restArgs,
        model: args.model,

        response_format: {
          type: "json_schema",
          json_schema: responseJsonSchema.json_schema,
        },
      }),
      (e) =>
        ({
          type: "LLMError",
          message: e instanceof Error ? e.message : String(e),
          rawError: e,
        } as const)
    );

    if (llmResWrapped.isErr()) return llmResWrapped;

    updateUsage(llmResWrapped.value.usage);

    const llmResCreated = date.parseUnix(llmResWrapped.value.created);

    const isCached = startedAt.getTime() > llmResCreated.getTime();

    const saveJsonParse = Result.fromThrowable(
      JSON.parse,
      (e) =>
        ({
          type: "JSONParseError",
          message: e instanceof Error ? e.message : String(e),
          rawError: e,
        } as const)
    );

    const parsedJson = saveJsonParse(llmResWrapped.value.choices[0].message.content!);

    if (parsedJson.isErr()) return parsedJson;

    const parsed = args.schema.safeParse(parsedJson.value);
    if (!parsed.success) {
      console.error("Error parsing LLM response:", parsed.error);
      return err({
        type: "ZodParseError",
        message: "Failed to parse LLM response with zod",
        zodError: parsed.error,
      } as const);
    }

    const object = parsed.data as z.infer<TResultSchema>;

    return ok({
      ...llmResWrapped,
      object,
      isCached,
    });
  }

  export async function generateStructuredOutput<TResultSchema extends ZodTypeAny>(
    args:
      | ({
          schema: TResultSchema;
        } & Omit<ChatCompletionCreateParamsNonStreaming, "model">) & {
          model: AIModelId;
          span?: Span;
          dontUseBraintrust?: boolean;
          dontCache?: boolean;
          maxRetries?: number;
        }
  ) {
    const { schema, span, dontUseBraintrust, dontCache, ...restArgs } = args;

    // const llm = new OpenAI({
    //   ...(dontUseBraintrust
    //     ? {}
    //     : {
    //         baseURL: "https://api.braintrust.dev/v1/proxy",
    //         apiKey: envVars.BRAINTRUST_API_KEY,
    //       }),
    // });

    // const activeSpan = trace.getSpan(context.active());

    // const jsonSchemaResponseFormat = zodResponseFormat(args.schema, "ResponseSchema");
    // if (jsonSchemaResponseFormat.json_schema.schema) delete jsonSchemaResponseFormat.json_schema.schema.$schema;
    // console.debug("responseJsonSchema zodResponseFormat", json.prettyPrint(jsonSchemaResponseFormat));

    const jsonSchema = zodToJsonSchema(args.schema, { $refStrategy: "none" });

    const jsonSchemaResponseFormat = {
      type: "json_schema",
      json_schema: {
        strict: true,
        name: "ResponseSchema",
        schema: jsonSchema,
      },
    };

    if (jsonSchemaResponseFormat.json_schema.schema) delete jsonSchemaResponseFormat.json_schema.schema.$schema;

    // console.debug("jsonSchema", json.prettyPrint(jsonSchema));

    const messages = args.messages ?? [];

    const startedAt = new Date();
    return ResultAsync.fromPromise(
      llm.chat.completions.create(
        {
          ...restArgs,
          model: args.model,
          response_format: jsonSchemaResponseFormat as any,
          // response_format: {
          //   type: "json_schema",
          //   json_schema: jsonSchemaResponseFormat,
          // },
        },
        {
          headers: {
            ...(span ? { "x-bt-parent": await span.export() } : {}),
            ...(dontCache ? { "x-dont-cache": "1" } : {}),
          },
        }
      ),
      (e) =>
        ({
          type: "LLMError",
          message: e instanceof Error ? e.message : String(e),
          rawError: e,
        } as const)
    ).andThen((llmRes) => {
      updateUsage(llmRes.usage);
      allRequests.push(llmRes);

      const llmResCreated = date.parseUnix(llmRes.created);

      const isCached = startedAt.getTime() > llmResCreated.getTime();

      // if (llmRes.choices[0].message.content) {
      //   console.debug("isCached time diff seconds:", (llmResCreated.getTime() - startedAt.getTime()) / 1000);
      // }

      return json
        .parseSave(llmRes.choices[0].message.content!)
        .andThen((parsedJson) => zodUtils.saveParse(args.schema, parsedJson))
        .andThen((object) => {
          return ok({
            ...llmRes,
            object: object,
            isCached,
          });
        })
        .mapErr((e) => {
          return {
            type: e.type,
            message: e instanceof Error ? e.message : String(e),
            llmRes: llmRes,
            rawError: e,
          } as const;
        });
    });
  }

  export async function generateStructuredOutputResponses<TResultSchema extends ZodTypeAny>(
    args:
      | ({
          schema: TResultSchema;
        } & Omit<ResponseCreateParamsWithTools, "model">) & {
          model: AIModelId;
          span?: Span;
        }
  ) {
    const { schema, span, ...llmArgs } = args;

    // const activeSpan = trace.getSpan(context.active());

    const traceHeaders = span
      ? {
          "x-bt-parent": await span.export(),
        }
      : {};

    // const responseJsonSchema = zodTextFormat(args.schema, "ResponseSchema");
    // console.debug("responseJsonSchema zodTextFormat", json.prettyPrint(responseJsonSchema));
    const jsonSchema = zodToJsonSchema(args.schema, { $refStrategy: "none" });
    const jsonSchemaResponseFormat = {
      strict: true,
      name: "ResponseSchema",
      schema: jsonSchema,
    };
    delete jsonSchemaResponseFormat.schema.$schema;

    const startedAt = new Date();
    const llmRes = await ResultAsync.fromPromise(
      llm.responses.parse({
        ...(llmArgs as any),
        model: args.model,
        text: {
          format: {
            type: "json_schema",
            json_schema: jsonSchemaResponseFormat,
          },
        },
      }),
      (e) =>
        ({
          type: "LLMError",
          message: e instanceof Error ? e.message : String(e),
          rawError: e,
        } as const)
    );

    return llmRes.andThen((res) => {
      updateUsage(res.usage);

      const llmResCreated = date.parseUnix(res.created_at);

      const isCached = startedAt.getTime() > llmResCreated.getTime();

      return ok({
        ...res,
        object: res.output_parsed as z.infer<TResultSchema>,
        isCached,
      });
    });
  }
}

async function test() {
  const res = await llm.generateStructuredOutput({
    model: "gpt-4o-mini",
    schema: z.object({
      recipe: z.object({
        joke: z.string(),
      }),
    }),
    messages: [
      {
        role: "user",
        content: "Generate a joke.",
      },
    ],
  });

  if (res.isErr()) {
    console.error("error", res.error);
  } else {
    console.log("joke", res.value.object);
  }
}

// cli.scafold({
//   test,
// });
