import { AIModelId, llm } from "../ai/llm/llm";
import { braintrust } from "../tracing/braintrust";
import * as lfi from "lfi";
import { GenericFile, MotlFile } from "../core/motlFile";
import { prisma } from "@/prisma/prisma";
import { err, okAsync, ResultAsync, Result } from "neverthrow";
import { sleep } from "openai/core.mjs";
import yargs from "yargs";
import { cryptoCompareApi } from "../core/cryptoCompareApi";
import { invoiceAi } from "./invoiceAi";
import { json } from "../core/utils/jsonUtils";
import { cli } from "../core/utils/cliUtils";
import fs from "fs/promises";
import path from "path";
import { s3 } from "../core/s3";
import limitConcur from "limit-concur";
import { createInvoice, invoiceService } from "./invoiceService";
import { pdfPlumberApi } from "../pdfParser/pdfPlumberApi";
import { mistral } from "../ai/mistral";
import PQueue from "p-queue";
import { documentAI } from "../pdfParser/documentAi";

export async function parseInvoices(args: {
  invoiceImportItems: { file: string; metadata: { filePath: string } }[];
  baseDetailsAiModel: AIModelId;
  invoiceValuesAiModel: AIModelId;
}) {
  const { invoiceImportItems } = args;

  // rootSpan.log({
  //   input: {
  //     invoiceImportItems: invoiceImportItems.length,
  //   },
  //   metadata: {
  //     aiModel: args.baseDetailsAiModel,
  //   },
  // });

  const createInvoiceQueue = new PQueue({ concurrency: 1 });

  const isInvoicesRes = await lfi.pipe(
    lfi.asConcur(invoiceImportItems),
    lfi.mapConcur(
      limitConcur(50, async (item) => {
        return braintrust.trace("processInvoice", async (span) => {
          // const createImportItemRes = await ResultAsync.fromPromise(
          //   (async () => {
          //     console.log("processing", item.file);

          //     // const importItem = await prisma.invoiceImportItem.upsert({
          //     //   where: {
          //     //     fileHash,
          //     //   },
          //     //   update: {
          //     //     fileHash,
          //     //     fileUrl: uploadRes.url,
          //     //     importItemType: "UPLOADED",
          //     //     externalId: fileHash,
          //     //     filename: file.fileName,
          //     //     text: parsedText.value.parsedText,
          //     //     itemDate: new Date(),
          //     //   },
          //     //   create: {
          //     //     fileHash,
          //     //     fileUrl: uploadRes.url,
          //     //     importItemType: "UPLOADED",
          //     //     externalId: fileHash,
          //     //     filename: file.fileName,
          //     //     text: parsedText.value.parsedText,
          //     //     itemDate: new Date(),
          //     //   },
          //     // });

          //     span.log({
          //       input: {
          //         importItem,
          //       },
          //     });
          //     return {
          //       importItem,
          //       file,
          //       fileHash,
          //       uploadRes,
          //     };
          //   })(),
          //   (err) =>
          //     ({
          //       code: "createImportItemError",
          //       message: "error creating invoice import item",
          //       err,
          //     } as const)
          // );

          // if (createImportItemRes.isErr()) {
          //   span.log({
          //     error: createImportItemRes.error,
          //   });
          //   return createImportItemRes;
          // }

          // const { importItem, file, fileHash, uploadRes } = createImportItemRes.value as any;

          // @ts-ignore

          return invoiceAi
            .processInvoice({
              file: item.file,
              baseDetailsAiModel: args.baseDetailsAiModel,
              invoiceValuesAiModel: args.invoiceValuesAiModel,
            })
            .map((res) => {
              return {
                ...res,
                item,
              };
            })
            .mapErr((err) => {
              span.log({
                error: {
                  err,
                  code: "extractInvoiceValuesError",
                },
                output: {
                  type: "extractInvoiceValuesError",
                },
              });
              return err;
            });
        });
      })
    ),
    lfi.mapConcur(async (res) => {
      return res.map((processInvoiceRes) => {
        if (!("baseDetails" in processInvoiceRes)) {
          return processInvoiceRes;
        }

        return createInvoiceQueue
          .add(() =>
            createInvoice({
              invoice: {
                date: new Date(processInvoiceRes.baseDetails.object.invoiceDate),
                invoiceReference: processInvoiceRes.baseDetails.object.invoiceReference,
                recipient: processInvoiceRes.baseDetails.object.recipient as any,
                amountGross: processInvoiceRes.invoiceValues.amountGross,
                amountNet: processInvoiceRes.invoiceValues.amountNet,
                amountVat: processInvoiceRes.invoiceValues.amountVat,

                hasVAT: processInvoiceRes.invoiceValues.hasVAT,
                isReverseCharge: processInvoiceRes.hasTaxRes.object.isReverseCharge,
                currencyCode: processInvoiceRes.hasTaxRes.object.currencyCode,
                sourceAmountGross:
                  "sourceAmountGross" in processInvoiceRes.invoiceValues ? (processInvoiceRes.invoiceValues.sourceAmountGross as number) : undefined,
                sourceAmountNet: "sourceAmountNet" in processInvoiceRes.invoiceValues ? (processInvoiceRes.invoiceValues.sourceAmountNet as number) : undefined,
                sourceAmountVat: "sourceAmountVat" in processInvoiceRes.invoiceValues ? (processInvoiceRes.invoiceValues.sourceAmountVat as number) : undefined,
                conversationRate:
                  "conversationRate" in processInvoiceRes.invoiceValues ? (processInvoiceRes.invoiceValues.conversationRate as number) : undefined,
                lineItems: {
                  createMany: {
                    data: processInvoiceRes.invoiceValues.items.map((item) => ({
                      name: item.name,
                      description: item.description,
                      priceGross: item.priceGross,
                      priceNet: item.priceNet,
                      vatRate: item.vatRate,
                      vatAmount: item.vatAmount,
                    })),
                  },
                },
              },
              vendor: {
                name: processInvoiceRes.baseDetails.object.vendor.companyName,
                street: processInvoiceRes.baseDetails.object.vendor.address.street,
                houseNumber: processInvoiceRes.baseDetails.object.vendor.address.houseNumber,
                city: processInvoiceRes.baseDetails.object.vendor.address.city,
                country: processInvoiceRes.baseDetails.object.vendor.address.country ?? "",
                vatNumber: processInvoiceRes.baseDetails.object.vendor.uidNumber,
                contactPerson: processInvoiceRes.baseDetails.object.vendor.contactPerson,
                email: processInvoiceRes.baseDetails.object.vendor.email,
                phone: processInvoiceRes.baseDetails.object.vendor.phone,
              },
              importItem: {
                fileHash: processInvoiceRes.fileHash,
                text: processInvoiceRes.parsedText,
                fileUrl: processInvoiceRes.uploadRes.url,
                filename: (processInvoiceRes as any).item.metadata.filePath,
                externalId: processInvoiceRes.fileHash,
                importItemType: "UPLOADED",
                itemDate: new Date(),
                isInvoice: true,
              },
            })
          )
          .then((res) => res!);
      });
    }),

    lfi.reduceConcur(lfi.toArray())
  );

  return isInvoicesRes;
}

async function main() {
  const cliArgs = await yargs(process.argv.slice(2))
    .option("path", { type: "string", required: true })
    .option("limit", { type: "number", default: 1 })
    .option("skip", { type: "number", default: 0 })
    .option("delete", { type: "boolean", default: false })
    .option("externalId", { type: "string", default: undefined })
    .parse();

  if (cliArgs.delete) {
    await prisma.invoiceImportItem.deleteMany({
      where: {
        importItemType: "UPLOADED",
      },
    });
    await prisma.invoice.deleteMany({});
    await prisma.vendor.deleteMany({});
  }

  // const logger = await braintrust.getLogger();

  // const dataPath = path.join("data-local", "Belege_16.06.25");

  // const dataPath = path.join("data-local", "cloudflare-invoices");

  const dataPath = cliArgs.path;

  const files = await fs.readdir(dataPath);

  console.log("total files in folder", files.length);

  const limit = cliArgs.limit == -1 ? files.length : cliArgs.limit;

  const invoiceImportItems = files
    .map((file) => ({
      file: path.join(dataPath, file),
      metadata: {
        filePath: file,
      },
    }))
    .slice(cliArgs.skip, cliArgs.skip + limit);

  console.log("invoiceImportItems", invoiceImportItems.length);

  const invoiceResults = await parseInvoices({
    baseDetailsAiModel: "gpt-4.1",
    invoiceValuesAiModel: "o4-mini",
    invoiceImportItems: invoiceImportItems,
  });

  const awaitedRes = await Promise.all(invoiceResults.map((r) => r.unwrapOr(null)).filter((r) => r != null));
  const failed = invoiceResults.filter((r) => r.isErr());

  const successRes = awaitedRes.filter((r) => r.isOk());

  const invoices = successRes.filter((r) => "baseDetails" in r);

  const notAnInvoice = successRes.filter((r) => "type" in r && r.type === "notAnInvoice");
  console.log(`not an invoice`, {
    notAnInvoice: notAnInvoice.length,
  });

  const notImported = invoiceResults.filter((res) => res.isErr());

  const failedInvoiceParsing = notImported.filter((res) => "type" in res && res.type != "notAnInvoice");

  if (failedInvoiceParsing.length > 0) console.log("Failed results:", json.prettyPrint(failedInvoiceParsing.map((r) => r)));

  // const successFulDbWrites = awaitedRes.map((r) => r.unwrapOr(null)).filter((r) => r != null);

  // Result.combine(awaitedRes).mapErr((failedDbWrites) => {
  //   console.log("failedDbWrites", failedDbWrites);
  // });
  // console.log("successFulDbWrites", successFulDbWrites.length);

  const cachedResponses = invoices.filter((item) => item?.baseDetails);

  console.log(`total documents: ${invoiceResults.length}`);

  console.log(`cached responses: ${cachedResponses.length}/${invoices.length}`);
  console.error(`errors in invoice detection: ${failedInvoiceParsing.length}`);

  console.log(`success fully parsed invoices: ${invoices.length}/${invoiceResults.length}`);

  console.log("llm.totalUsage", llm.getUsageCost());

  // await prisma.invoiceImportItem.updateMany({
  //   where: {
  //     id: {
  //       in: results.map((r) => r.object.id),
  //     },
  //   },
  //   data: {
  //     isInvoice: true,
  //   },
  // });
}

cli.scafold({
  main,
});
