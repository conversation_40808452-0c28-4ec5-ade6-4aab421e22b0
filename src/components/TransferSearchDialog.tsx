import { useState, useEffect } from "react";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { SearchInput } from "@/components/SearchInput";
import { trpc } from "@/utils/trpc";
import { Search, Link, ExternalLink, Calendar, DollarSign, Building2, Loader2, Sparkles, Target } from "lucide-react";
import { currency } from "@/modules/core/currency";
import { toast } from "sonner";

interface TransferSearchDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  invoiceId: string;
  onMatchSuccess?: () => void;
}

export function TransferSearchDialog({ open, onOpenChange, invoiceId, onMatchSuccess }: TransferSearchDialogProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedTransferId, setSelectedTransferId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("suggested");

  // Get invoice details for context
  const { data: invoice } = trpc.invoices.getById.useQuery({ id: invoiceId }, { enabled: !!invoiceId });

  // Search transfers
  const {
    data: transfers,
    isLoading: searchLoading,
    refetch,
  } = trpc.transfers.search.useQuery(
    {
      search: searchTerm,
      limit: 20,
      excludeReconciled: true,
      invoiceId: invoiceId, // Pass invoiceId to calculate confidence scores
    },
    {
      enabled: !!searchTerm && searchTerm.length > 0 && activeTab === "search",
    }
  );

  // Get suggested matches
  const {
    data: suggestedMatches,
    isLoading: suggestedLoading,
    refetch: refetchSuggested,
  } = trpc.transfers.getSuggestedMatches.useQuery(
    {
      invoiceId,
      limit: 5,
    },
    {
      enabled: open && activeTab === "suggested",
    }
  );

  // Manual match mutation
  const manualMatchMutation = trpc.reconciliation.manualMatch.useMutation({
    onSuccess: () => {
      toast.success("Transfer matched successfully!");
      onMatchSuccess?.();
      onOpenChange(false);
      setSelectedTransferId(null);
      // Refresh both queries
      refetch();
      refetchSuggested();
    },
    onError: (error) => {
      toast.error(`Failed to match transfer: ${error.message}`);
    },
  });

  const handleMatch = (transferId: string) => {
    setSelectedTransferId(transferId);
    manualMatchMutation.mutate({
      invoiceId,
      transferId,
    });
  };

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
  };

  // Helper function to render transfer cards
  const renderTransferCard = (transfer: any, showConfidenceScore = false) => (
    <Card key={transfer.id} className="hover:bg-muted/50 transition-colors">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex-1 space-y-2">
            {/* Amount and Date */}
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-1">
                <DollarSign className="h-4 w-4 text-green-600" />
                <span className="font-medium">{currency.formatMonetary(transfer.amount, transfer.currencyCode)}</span>
              </div>
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <Calendar className="h-4 w-4" />
                {new Date(transfer.transaction.executedAt).toLocaleDateString()}
              </div>
              {showConfidenceScore && transfer.confidenceScore && (
                <Badge variant={transfer.confidenceScore >= 80 ? "default" : transfer.confidenceScore >= 60 ? "secondary" : "outline"}>
                  <Target className="h-3 w-3 mr-1" />
                  {transfer.confidenceScore.toFixed(0)}% match
                </Badge>
              )}
            </div>

            {/* Detailed Score Breakdown */}
            {showConfidenceScore && transfer.detailedScore && (
              <div className="grid grid-cols-3 gap-2 text-xs">
                <div className="flex items-center gap-1">
                  <DollarSign className="h-3 w-3 text-green-600" />
                  <span className="font-medium">Amount:</span>
                  <Badge variant="outline" className="text-xs">
                    {Math.round((transfer.detailedScore.breakdown.amount.score / transfer.detailedScore.breakdown.amount.maxScore) * 100)}%
                  </Badge>
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3 text-blue-600" />
                  <span className="font-medium">Date:</span>
                  <Badge variant="outline" className="text-xs">
                    {Math.round((transfer.detailedScore.breakdown.date.score / transfer.detailedScore.breakdown.date.maxScore) * 100)}%
                  </Badge>
                </div>
                <div className="flex items-center gap-1">
                  <Building2 className="h-3 w-3 text-purple-600" />
                  <span className="font-medium">Vendor:</span>
                  <Badge variant="outline" className="text-xs">
                    {Math.round((transfer.detailedScore.breakdown.vendor.score / transfer.detailedScore.breakdown.vendor.maxScore) * 100)}%
                  </Badge>
                </div>
              </div>
            )}

            {/* Description */}
            {transfer.description && (
              <div className="text-sm">
                <span className="font-medium">Description:</span> {transfer.description}
              </div>
            )}

            {/* Counterparty */}
            {transfer.counterparty && (
              <div className="flex items-center gap-1 text-sm">
                <Building2 className="h-4 w-4" />
                <span className="font-medium">Counterparty:</span> {transfer.counterparty}
              </div>
            )}

            {/* Account */}
            <div className="text-sm text-muted-foreground">Account: {transfer.transaction.account.name}</div>

            {/* Match Reason for suggested matches */}
            {showConfidenceScore && transfer.matchReason && (
              <div className="text-xs text-muted-foreground">
                <span className="font-medium">Match reason:</span> {transfer.matchReason.replace(/_/g, " ")}
              </div>
            )}

            {/* Reconciliation Status */}
            {transfer.reconciliations.length > 0 ? <Badge variant="secondary">Already Reconciled</Badge> : <Badge variant="outline">Unmatched</Badge>}
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2 ml-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open(`/transactions?transactionId=${transfer.transaction.id}`, "_blank")}
              className="flex items-center gap-1"
            >
              <ExternalLink className="h-3 w-3" />
              View
            </Button>

            {transfer.reconciliations.length === 0 && (
              <Button
                size="sm"
                onClick={() => handleMatch(transfer.id)}
                disabled={manualMatchMutation.isPending && selectedTransferId === transfer.id}
                className="flex items-center gap-1"
              >
                {manualMatchMutation.isPending && selectedTransferId === transfer.id ? (
                  <Loader2 className="h-3 w-3 animate-spin" />
                ) : (
                  <Link className="h-3 w-3" />
                )}
                Match
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Find Transfer to Match
          </DialogTitle>
          <DialogDescription>
            View suggested matches or search for transfers to manually match with this invoice.
            {invoice && (
              <div className="mt-2 p-2 bg-muted rounded text-sm">
                <strong>Invoice:</strong> {invoice.invoiceReference || "No reference"} • {currency.formatMonetary(invoice.amountGross, invoice.currencyCode)} •{" "}
                {invoice.vendor?.name || "Unknown vendor"}
              </div>
            )}
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 overflow-hidden flex flex-col">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="suggested" className="flex items-center gap-2">
              <Sparkles className="h-4 w-4" />
              Suggested Matches
            </TabsTrigger>
            <TabsTrigger value="search" className="flex items-center gap-2">
              <Search className="h-4 w-4" />
              Search Transfers
            </TabsTrigger>
          </TabsList>

          <TabsContent value="suggested" className="flex-1 overflow-hidden flex flex-col mt-4">
            <div className="flex-1 overflow-y-auto space-y-3">
              {suggestedLoading && (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin" />
                  <span className="ml-2">Finding suggested matches...</span>
                </div>
              )}

              {!suggestedLoading && suggestedMatches && suggestedMatches.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  <p>No potential matches found for this invoice.</p>
                  <p className="text-sm mt-1">Try using the search tab to find transfers manually.</p>
                  <p className="text-xs mt-2 text-muted-foreground/70">(Matches need at least 30% confidence score to appear here)</p>
                </div>
              )}

              {!suggestedLoading && suggestedMatches && suggestedMatches.length > 0 && (
                <div className="space-y-3">
                  <div className="text-sm text-muted-foreground">
                    Top {suggestedMatches.length} suggested match{suggestedMatches.length !== 1 ? "es" : ""} (sorted by confidence)
                  </div>
                  {suggestedMatches.map((transfer) => renderTransferCard(transfer, true))}
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="search" className="flex-1 overflow-hidden flex flex-col mt-4">
            <div className="space-y-4 flex-1 overflow-hidden flex flex-col">
              {/* Search Input */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Search Term</label>
                <SearchInput
                  value={searchTerm}
                  onChange={handleSearchChange}
                  placeholder="Search transfers by description or counterparty..."
                  className="w-full"
                />
              </div>

              {/* Results */}
              <div className="flex-1 overflow-y-auto space-y-3">
                {searchLoading && (
                  <div className="flex items-center justify-center py-8">
                    <Loader2 className="h-6 w-6 animate-spin" />
                    <span className="ml-2">Searching transfers...</span>
                  </div>
                )}

                {!searchLoading && searchTerm && transfers && transfers.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">No transfers found matching "{searchTerm}"</div>
                )}

                {!searchLoading && transfers && transfers.length > 0 && (
                  <div className="space-y-3">
                    <div className="text-sm text-muted-foreground">
                      Found {transfers.length} transfer{transfers.length !== 1 ? "s" : ""}
                      {transfers.some((t: any) => t.confidenceScore) && " (sorted by match confidence)"}
                    </div>
                    {transfers.map((transfer: any) => renderTransferCard(transfer, !!(transfer as any).confidenceScore))}
                  </div>
                )}

                {!searchTerm && <div className="text-center py-8 text-muted-foreground">Enter a search term to find transfers</div>}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
